{"version": "0.2.0", "configurations": [{"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "f:/Git/KT4_1803S/HeliosSDK_EC200AEUHA_CPE_20240926/pythonSDK/components/sip/src", "program": "f:/Git/KT4_1803S/HeliosSDK_EC200AEUHA_CPE_20240926/pythonSDK/components/sip/src/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}