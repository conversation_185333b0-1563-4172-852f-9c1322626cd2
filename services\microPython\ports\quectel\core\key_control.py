import osTimer,audio
import utime as time
from common import Abstract
from common import LogAdapter
import EventMesh
from machine import Pin, ExtInt,I2C
from misc import Power,PowerKey
import utime 
"""
矩阵键盘初始化
按键触发功能响应

KeypadEvent :按键码
KeypadManager:按键事件处理
"""
class InputMethodHandler(object):
    """
    处理不同输入法的内容
    """
    def __init__(self):
        # 按键对应的字母表
        self.keypad_letters = {
            '2': 'ABC',
            '3': 'DEF',
            '4': 'GHI',
            '5': 'JKL',
            '6': 'MNO',
            '7': 'PQRS',
            '8': 'TUV',
            '9': 'WXYZ',
        }
        self.keypad_symbol = [",", ".", "!", "?", "-", "/", "#", "*"]
        self.keypad_symbol_cur = 0
        # 记录上一次按键的时间
        self.last_keypress_time = 0
        self.last_key = ""
        self.last_symbol = ""
        # 当前选中的字母索引
        self.current_letter_index = 0
        self.select_msg = ""

    def input_info_handle(self, msg):
        """根据输入法选择处理"""
        input_meethod = EventMesh.publish("get_input_method")
        # print("input_info_handle:mag ",msg," input_meethod:",input_meethod)
        if (input_meethod == "ABC" or input_meethod == "abc") and (msg != "1" and msg != "0" and msg != "*"):
            self.letter_handle_keypress(msg, input_meethod)
            self.last_symbol = ""
        elif input_meethod == "123":
            self.numerals_handle_keypress(msg)
            self.last_symbol = ""
        elif (input_meethod == "ABC" or input_meethod == "abc") and (msg == "*"):
            self.symbol_handle_keypress()
            self.last_key = ""
        elif (input_meethod == "ABC" or input_meethod == "abc") and (msg == "0"):
            EventMesh.publish("btn_num_click", " ")
            self.last_symbol = ""
        elif (input_meethod == "拼音") and (msg != "1" and msg != "*"):
            EventMesh.publish("btn_num_click",msg)
            self.last_symbol = ""
        elif (input_meethod == "拼音") and (msg == "*"):
            EventMesh.publish("btn_symbol_click", "*")
            self.last_key = ""
        # elif (input_meethod == "拼音") and (msg == "0"):
        #     EventMesh.publish("btn_num_click", " ")
        #     self.last_symbol = ""
        else:
            self.last_key = ""
            self.last_symbol = ""
            print("input method error")
            return None

    # 英文字母
    def letter_handle_keypress(self, key, input_meethod):
        # 获取当前时间
        current_time = time.ticks_ms()
        if current_time - self.last_keypress_time > 1000:
            self.current_letter_index = 0
            if input_meethod == "abc":
                self.select_msg = self.keypad_letters[key][self.current_letter_index].lower()
            else:
                self.select_msg = self.keypad_letters[key][self.current_letter_index]
            self.last_key = key
            EventMesh.publish("btn_num_click", self.select_msg)
        else:
            # 否则，切换到下一个字母
            if self.last_key != key:
                self.current_letter_index = 0
                if input_meethod == "abc":
                    self.select_msg = self.keypad_letters[key][self.current_letter_index].lower()
                else:
                    self.select_msg = self.keypad_letters[key][self.current_letter_index]
                EventMesh.publish("btn_num_click", self.select_msg)
                self.last_key = key
                return
            self.last_key = key
            self.current_letter_index = (self.current_letter_index + 1) % len(self.keypad_letters[key])
            if input_meethod == "abc":
                self.select_msg = self.keypad_letters[key][self.current_letter_index].lower()
            else:
                self.select_msg = self.keypad_letters[key][self.current_letter_index]
            EventMesh.publish("del_char")
            EventMesh.publish("btn_num_click", self.select_msg)
        # 更新上一次按键的时间
        self.last_keypress_time = current_time

    # 数字
    def numerals_handle_keypress(self, key):
        EventMesh.publish("btn_num_click", key)

    # 符号
    def symbol_handle_keypress(self):
        if self.keypad_symbol_cur >= 7:
            self.keypad_symbol_cur = 0
        else:
            self.keypad_symbol_cur +=1
        current_time = time.ticks_ms()
        if current_time - self.last_keypress_time > 1000:
            self.keypad_symbol_cur = 0
            EventMesh.publish("btn_num_click", self.keypad_symbol[self.keypad_symbol_cur])
            self.last_symbol = self.keypad_symbol[self.keypad_symbol_cur]
        else:
            if self.last_symbol != "":
                EventMesh.publish("del_char")
                EventMesh.publish("btn_num_click", self.keypad_symbol[self.keypad_symbol_cur])
            else:
                self.keypad_symbol_cur = 0
                EventMesh.publish("btn_num_click", self.keypad_symbol[self.keypad_symbol_cur])
                self.last_symbol = self.keypad_symbol[self.keypad_symbol_cur]
        # 更新上一次按键的时间
        self.last_keypress_time = current_time

class KeypadEvent(object):
    NUMERIC_KEY = ["1", "2", "3","4", "5", "6","7", "8", "9", "0", "#", "*"]
    UP = "上"
    DOWN = "下"
    LEFT = "左"
    RIGHT = "右"
    FAST_1 = "快捷1"
    FAST_2 = "快捷2"
    FAST_3 = "快捷3"
    FAST_4 = "快捷4"
    HANDS_FREE = "免提"
    HEADSET = "耳麦"
    REDIAL = "重拨"
    CALL = "拨号"
    MENU = "菜单"
    BACK = "返回"

class keypadCallback(object):
    @staticmethod
    def keypad_event_handler(event):
        """
        矩阵键盘回调函数
        """
        if EventMesh.publish("get_IsgoTomain","0")==1:
            EventMesh.publish("key_event_manage", event)
        else:
            print("keypadCallback:power off no key")

    @staticmethod
    def pwk_event_handler(event):
        """power key 回调"""
        if event == 1:
            #按键音
            if EventMesh.publish("get_hook_state")==1:#免提状态
                EventMesh.publish("audio_enable")   
            else:
                pass
            EventMesh.publish("set_audio_volume", 1)
            EventMesh.publish("audio_key_tone",0x0a)
            EventMesh.publish("pwk_press_handle")
        elif event == 0:
            print("Power 抬起")
            EventMesh.publish("pwk_up_handle")

class KeypadManager(Abstract):
    """物理按键类,5*5矩阵键盘"""

    def __init__(self):
        # self.__keypad = KeyPad(6, 6)
        self.__pk = PowerKey()
        self.__key_handler = InputMethodHandler()
        self.__hook = None
        self.__hook_status = 0
        self.__speaker_status = 0
        self.key_info = ""
        self.__pwk_up_long_timer = osTimer()
        self.__num_up_long_timer = osTimer()
        self.__fast_up_long_timer = osTimer()
        self.__pwk_up_long_timer_flag = False
        self.__num_up_long_timer_flag = 0
        self.__fast_up_long_timer_flag = 0
        self.Audio=audio.Audio(0)
        self.i2c = I2C(I2C.I2C0, I2C.STANDARD_MODE)
        self.tm = TM1650(self.i2c, int_pin=29, event_cb=self.CALLFUN)
        self.keypad_list = [
            ["快捷4", "0", "#"],  # 0
            ["7", "8", "9", '免提'],  # 1
            ["4", "5", "6", '耳麦', '重拨'],  # 2
            ["拨号", "下", "右", "快捷1", "*"],  # 3
            ["1", "2", "3", "快捷3"],  # 4
            ["菜单", "左", "上", "返回", "快捷2"], # 5
        ]
        self.log = LogAdapter(self.__class__.__name__)

    def post_processor_after_instantiation(self):
        EventMesh.subscribe("key_event_manage", self.__key_event_manage)
        EventMesh.subscribe("pwk_press_handle", self.__pwk_up_press_handle)
        EventMesh.subscribe("pwk_up_handle", self.__pwk_up_up_handle)
        EventMesh.subscribe("get_hook_state", self.get_hook_state)
        EventMesh.subscribe("get_speaker_state", self.get_handfree_state)
        EventMesh.subscribe("set_handfree_state", self.set_handfree_state)
        # EventMesh.subscribe("audio_tone", self.__key_tone_handle)
        # self.__keypad.init()
        # self.__keypad.set_callback(keypadCallback.keypad_event_handler)
        self.__pk.powerKeyEventRegister(keypadCallback.pwk_event_handler)
        self.__hook = ExtInt(ExtInt.GPIO44, ExtInt.IRQ_RISING_FALLING, ExtInt.PULL_PU,self.__hook_callback)
        self.__hook.enable()

    def CALLFUN(self,args):
        # print("args:",args)
        # print('### key {} ###'.format(args))
        if EventMesh.publish("get_IsgoTomain","0")==1:
            EventMesh.publish("key_event_manage", args)
        else:
            print("keypadCallback:power off no key")
    
    def keypad_tone(self, topic=None, data=None):
        # 按键音
        if not EventMesh.publish("get_keypad_tone") and EventMesh.publish("get_speaker_state") == 1:
            EventMesh.publish("audio_tone") 

    def get_hook_state(self,topic,msg):
        """插簧状态 0手柄 1免提"""
        self.__hook_status = self.__hook.read_level()
        return self.__hook_status
    
    def __hook_callback(self,args):
        """插簧"""
        if EventMesh.publish("get_IsgoTomain","0")==1:
            self.__hook_status = self.__hook.read_level()
            print("__hook_callback >> __hook_status:",self.__hook_status)
            call_state = EventMesh.publish("get_call_state")
            if call_state == "0":
                EventMesh.publish("howler_tone_control",0)
                EventMesh.publish("set_handfree_state",0)  #无论拿起或者放下话筒都将免提状态重置为0
                print("免提状态重置为0")
                if self.__hook_status == 0:#手柄拿起
                    print("手柄拿起")
                    EventMesh.publish("audio_disable")
                    # EventMesh.publish("audio_tone")
                else:#手柄放下
                    print("手柄放下")
                    # EventMesh.publish("audio_tone_stop")
                    EventMesh.publish("audio_enable")
            
            EventMesh.publish("btn_hook_click",self.__hook_status) 

    def match_digit(self, row, column):
        digit = self.keypad_list[row][column]
        return digit

    def __key_event_manage(self, topic, event):
        """
        :param event:
        :return:
        """
        # state, row, column = event
        # key_info = self.match_digit(row, column)     
        key_info =event[0]
        key_event = event[1]
        state = 1
        # print(key_info,state)
        #按键音
        tem = 0
        vol = EventMesh.publish("persistent_config_get", "key_tone")
        if vol == None:
            tem = 7
        else:
            tem = int(vol)
        # print("tem",tem)
        if tem != 0:
            if EventMesh.publish("get_current_screen_name") in ["volumeselect","ringselect", "AlarmClockring","call_up","call_up_new"]:
                EventMesh.publish("audio_play_stop")
            if EventMesh.publish("get_speaker_state") == "0":       #如果处于通话状态，不走按键音，此处为了避免通话中按键会重置通话音量
                EventMesh.publish("set_audio_volume", 1)
            #按键音功放
            self.__hook_status = self.__hook.read_level()
            print("手柄状态：：",self.__hook_status)
            if self.__hook_status == 1 :    #手柄挂上
                if EventMesh.publish("audio_PA_status") == 0 : 
                    EventMesh.publish("audio_enable")
            if key_info in KeypadEvent.NUMERIC_KEY and state:
                if key_info == '*':
                    EventMesh.publish("audio_key_tone",15)
                elif key_info == '#':
                    EventMesh.publish("audio_key_tone",14)
                else:
                    EventMesh.publish("audio_key_tone",int(key_info))
            elif key_info != '免提' and state:
                EventMesh.publish("audio_key_tone",0x0a)

        self.key_info = key_info
        

        if key_info in KeypadEvent.NUMERIC_KEY:
            # if not state:
            #     self.__num_key_up_handle(key_info)
            # else: 
            #     self.__num_key_press_handle(key_info)
            # return
            if key_info == "#" and key_event=="ONCE":
                EventMesh.publish("btn_symbol_click", key_info)
            elif key_info != "#" and key_event=="LONG" :
                EventMesh.publish("btn_num_long_click",self.key_info)
            elif key_info != "#" and key_event=="ONCE" :
                self.__key_handler.input_info_handle(key_info)
        # 上 抬起
        elif key_info == KeypadEvent.UP and key_event=="ONCE":
            self.__up_key_up_handle(key_info)
        # 下 抬起
        elif key_info == KeypadEvent.DOWN and key_event=="ONCE":
            self.__down_key_up_handle(key_info)
        # 左 抬起
        elif key_info == KeypadEvent.LEFT and key_event=="ONCE":
            self.__left_key_up_handle(key_info)
        # 右 抬起
        elif key_info == KeypadEvent.RIGHT and key_event=="ONCE":
            self.__right_key_up_handle(key_info)
        # 快捷1 抬起
        elif key_info == KeypadEvent.FAST_1 :
            if key_event=="ONCE" :
                self.__fast_1_key_up_handle(key_info)
            else:
                self.__fast_key_press_handle(key_info)
        # 快捷2 抬起
        elif key_info == KeypadEvent.FAST_2:
            if key_event=="ONCE":
                self.__fast_2_key_up_handle(key_info)
            else:
                self.__fast_key_press_handle(key_info)
        # 快捷3 抬起
        elif key_info == KeypadEvent.FAST_3:
            if key_event=="ONCE":
                self.__fast_3_key_up_handle(key_info)
            else:
                self.__fast_key_press_handle(key_info)
        # 快捷4 抬起
        elif key_info == KeypadEvent.FAST_4:
            if key_event=="ONCE":
                self.__fast_4_key_up_handle(key_info)
            else:
                self.__fast_key_press_handle(key_info)
        # 免提 抬起
        elif key_info == KeypadEvent.HANDS_FREE and key_event=="ONCE":
            self.__hands_free_key_up_handle(key_info)
        # 耳麦 抬起
        elif key_info == KeypadEvent.HEADSET and key_event=="ONCE":
            self.__headset_key_up_handle(key_info)
        # 重拨 抬起
        elif key_info == KeypadEvent.REDIAL and key_event=="ONCE":
            self.__redial_key_up_handle(key_info)
        # 拨号 抬起
        elif key_info == KeypadEvent.CALL and key_event=="ONCE":
            self.__call_key_up_handle(key_info)
        # 菜单 抬起
        elif key_info == KeypadEvent.MENU and key_event=="ONCE":
            self.__menu_key_up_handle(key_info)
        # 返回 抬起
        elif key_info == KeypadEvent.BACK and key_event=="ONCE":
            self.__back_key_up_handle(key_info)
        else:
            pass

    def __num_key_up_handle(self, key_info):
        """0-9 * #键 抬起"""
        # self.log.info(key_info)
        if self.__num_up_long_timer_flag == 1:
            self.__num_up_long_timer_stop()
            self.__num_up_long_timer_flag = 0
        if key_info == "#":
            EventMesh.publish("btn_symbol_click", key_info)
        else:
            self.__key_handler.input_info_handle(key_info)
    
    def __num_key_press_handle(self, key_info):
        """0-9 * #键 按下"""
        if self.__num_up_long_timer_flag == 1:
            self.__num_up_long_timer_stop()
        self.__num_up_long_timer_flag = 1
        # if key_info == "#":
        #     EventMesh.publish("btn_symbol_click", key_info)
        # else:
        #     self.__key_handler.input_info_handle(key_info)
        self.__num_up_long_timer_start(key_info)

    def __num_up_long_timer_start(self,key_info):
        self.__num_up_long_timer.start(2000, 0, self.__num_up_long_handle)

    def __num_up_long_handle(self,args):
        print("数字{0} 长按3s".format(self.key_info))
        EventMesh.publish("btn_num_long_click",self.key_info)

    def __num_up_long_timer_stop(self):
        self.__num_up_long_timer.stop()

    def __fast_key_press_handle(self, key_info):
        """快捷键1-4 按下"""
        if self.__fast_up_long_timer_flag == 1:
            self.__fast_up_long_timer_stop()
        self.__fast_up_long_timer_flag = 1

        self.__fast_up_long_timer_start()
    
    def __fast_up_long_timer_start(self):
        self.__fast_up_long_timer.start(100, 0, self.__fast_up_long_handle)

    def __fast_up_long_handle(self,args):
        print("快捷键{0} 长按3s".format(self.key_info))
        EventMesh.publish("btn_fast_long_click",self.key_info)

    def __fast_up_long_timer_stop(self):
        self.__fast_up_long_timer.stop()

    def __up_key_up_handle(self, key_info):
        """方向按键 上 抬起"""
        # self.log.info(key_info)
        EventMesh.publish("btn_up_click")

    def __down_key_up_handle(self, key_info):
        """方向按键 下 抬起"""
        # self.log.info(key_info)
        EventMesh.publish("btn_down_click")

    def __left_key_up_handle(self, key_info):
        """方向按键 左 抬起"""
        # self.log.info(key_info)
        EventMesh.publish("btn_left_click")

    def __right_key_up_handle(self, key_info):
        """方向按键 右 抬起"""
        # self.log.info(key_info)
        EventMesh.publish("btn_right_click")

    def __fast_1_key_up_handle(self, key_info):
        """快捷1 抬起"""
        # self.log.info(key_info)
        if self.__fast_up_long_timer_flag == 1:
            self.__fast_up_long_timer_stop()
        self.__fast_up_long_timer_flag = 0
        EventMesh.publish("btn_fast_1_click")

    def __fast_2_key_up_handle(self, key_info):
        """快捷2 抬起"""
        # self.log.info(key_info)
        if self.__fast_up_long_timer_flag == 1:
            self.__fast_up_long_timer_stop()
        self.__fast_up_long_timer_flag = 0
        EventMesh.publish("btn_fast_2_click")

    def __fast_3_key_up_handle(self, key_info):
        """快捷3 抬起"""
        # self.log.info(key_info)
        if self.__fast_up_long_timer_flag == 1:
            self.__fast_up_long_timer_stop()
        self.__fast_up_long_timer_flag = 0
        EventMesh.publish("btn_fast_3_click")

    def __fast_4_key_up_handle(self, key_info):
        """快捷4 抬起"""
        # self.log.info(key_info)
        if self.__fast_up_long_timer_flag == 1:
            self.__fast_up_long_timer_stop()
        self.__fast_up_long_timer_flag = 0
        EventMesh.publish("btn_fast_4_click")

    def get_handfree_state(self,topic=None,msg=None):
        """免提状态 0手柄 1免提"""
        return self.__speaker_status
    
    def set_handfree_state(self,topic=None,msg=None):
        if msg == 0:
            EventMesh.publish("audio_disable")
        elif msg == 1:
            EventMesh.publish("audio_enable")
        self.__speaker_status = msg

        return self.__speaker_status
    
    def __hands_free_key_up_handle(self, key_info):
        """免提 抬起"""
        # self.log.info(key_info)
        print("__hands_free_key_up_handle speaker",self.__speaker_status)
        call_state = EventMesh.publish("get_call_state")
        if call_state == "0":
            if not self.__speaker_status :
                self.__speaker_status = 1
                print("免提抬起")
                # EventMesh.publish("set_audio_volume", 0)
                self.Audio.setVolume(11)
                EventMesh.publish("audio_enable")
                # EventMesh.publish("audio_tone")
            else:
                self.__speaker_status = 0
                # EventMesh.publish("audio_tone_stop")
                EventMesh.publish("audio_disable")
        elif  call_state == "3" or call_state == "4" or call_state == "9":
            self.__speaker_status = 1
            pa_pin = EventMesh.publish("audio_PA_status")
            EventMesh.publish("audio_enable")            
        EventMesh.publish("btn_hands_free_click",self.__speaker_status)

    def __headset_key_up_handle(self, key_info):
        """耳麦 抬起"""
        # self.log.info(key_info)
        EventMesh.publish("btn_headset_click")

    def __redial_key_up_handle(self, key_info):
        """重拨 抬起"""
        # self.log.info(key_info)
        EventMesh.publish("btn_redial_click")

    def __call_key_up_handle(self, key_info):
        """呼出 抬起"""
        # self.log.info(key_info)
        EventMesh.publish("btn_call_click")

    def __menu_key_up_handle(self, key_info):
        """菜单 or 确认键 抬起"""
        # self.log.info(key_info)
        EventMesh.publish("btn_menu_click")

    def __back_key_up_handle(self, key_info):
        """返回键 抬起"""
        # self.log.info(key_info)
        EventMesh.publish("btn_back_click")

    def __pwk_up_press_handle(self, topic=None, event=None):
        """挂断键/pwk 按下"""
        self.__pwk_up_long_timer_start()

    def __pwk_up_up_handle(self, topic=None, event=None):
        """hand_up键 抬起"""
        print("抬起电源键")
        self.__pwk_up_long_timer_stop()
        if self.__pwk_up_long_timer_flag:
            self.__pwk_up_long_timer_flag = False
            return
        print("pwk up 短按") 
        EventMesh.publish("btn_pwk_click")

    def __pwk_up_long_timer_start(self):
        print("开启3秒定时器")
        self.__pwk_up_long_timer.start(2000, 0, self.__pwk_up_long_handle)

    def __pwk_up_long_timer_stop(self):
        self.__pwk_up_long_timer.stop()

    def __pwk_up_long_handle(self, args):
        """hand_up键 长按"""
        self.set_pwk_up_long_timer_flag(flag=True)
        print("pwk up 长按3s")
        EventMesh.publish("btn_pwk_long_click")

    def set_pwk_up_long_timer_flag(self, topic=None, flag=None):
        self.__pwk_up_long_timer_flag = flag

    def start(self):
        self.post_processor_after_instantiation()


class TM1650:
    """ 按键事件处理 """
    class Event():
        # PRESSED = "PRESSED"
        # RELEASED = "RELEASED"
        ONCE_PRESSED = "ONCE"
        DOUBLE_PRESSED = "DOUBLE"
        LONG_PRESSED = "LONG"
        THREE_PRESSED = "THREE"
        FOUR_PRESSED = "FOUR"

    class Error(Exception):
        def __init__(self, value):
            self.value = value
        def __str__(self):
            return repr(self.value)

    def __init__(self,i2c_bus,int_pin = 29, event_cb = None):
        self.i2c_dev = i2c_bus
        self.r_data = bytearray(1)
        # 只有设置七段输出(P3=1)和开屏显示(P0=1)，DP引脚(中断)才会在按键按下时拉低，在读寄存器时拉高
        self.i2c_dev.write(0x24, bytearray(0x00), 0, bytearray([0x48]), len([0x48]))
        self.i2c_dev.write(0x24, bytearray(0x00), 0, bytearray([0x09]), len([0x09]))

        self.event_cb = event_cb
        self._int_pin = int_pin
        self._extint = ExtInt(ExtInt.GPIO29, ExtInt.IRQ_FALLING , ExtInt.PULL_PU, self.__extfun)
        self._extint.enable()

        # 按键按下时的输出值:(按键按下时 P6=1,松开时 P6=0)
        self.dict = {0x64:"拨号", 0x65:"下", 0x66:"右", 0x67:"快捷1", 0x5C:"1", 0x5D:"2", 0x5E:"3", 0x5F:"快捷3", 0x54:"4", 0x55:"5", 0x56:"6", 0x57:"耳麦", 0x4C:"7", 0x4D:"8", 0x4E:"9", 0x4F:"免提", 0x44:"快捷4", 0x45:"0", 0x46:"#", 0x47:"重拨", 0x6C:"菜单", 0x6D:"左", 0x6E:"上", 0x6F:"返回",0x75:"*",0x74:"快捷2",103:"快捷1"}

        self.timer1 = osTimer()# 判断单击的定时器
        self.period = 10 # 定时器时间参数,单位毫秒
        self.clicksnum = 0 # 点击次数
        self.key = 0 # 按键
        print("TM1650初始化")


    # 读按键输出值
    def tm1650_read(self):
        self.i2c_dev.read(0x24, bytearray(0x00), 0, self.r_data, 1, 1)
        return self.r_data

    # 单击
    def once_callback(self, args):
        self.clicksnum = 0
        event = self.Event.ONCE_PRESSED
        # key_evt_queue.put((self,self.key,event))
        list_push = [self.key,event]
        self.event_cb(list_push)
    # 双击
    def double_callback(self, args):
        self.clicksnum = 0
        event = self.Event.DOUBLE_PRESSED
        # key_evt_queue.put((self,self.key,event))
        list_push = [self.key,event]
        self.event_cb(list_push)
    # 三击
    def three_callback(self, args):
        self.clicksnum = 0
        event = self.Event.THREE_PRESSED
        # key_evt_queue.put((self,self.key,event))
        list_push = [self.key,event]
        self.event_cb(list_push)
    # 四击
    def four_callback(self):
        self.clicksnum = 0
        event = self.Event.FOUR_PRESSED
        # key_evt_queue.put((self,self.key,event))
        list_push = [self.key,event]
        self.event_cb(list_push)
    # 长按
    def long_callback(self):
        self.clicksnum = 0
        event = self.Event.LONG_PRESSED
        # key_evt_queue.put((self,self.key,event))
        list_push = [self.key,event]
        self.event_cb(list_push)


    def __extfun(self, args):
        self.timer1.stop()

        keynum = self.tm1650_read()
        pressed = keynum[0]
        # print("键值：：",pressed)
        # 当不是因为按键按下触发中断时return
        if pressed not in self.dict.keys():
            print(pressed,"找不到对应的键")
            return
        # 通过读取值找到对应的按键
        self.key = self.dict[pressed]
        # print('按键{} 按下'.format(self.key))
        # 开始计时
        start = utime.ticks_ms()
        # 按键释放 P6=0，阻塞等待按键释放
        while pressed & (1 << 6):
            keynum = self.tm1650_read()
            pressed = keynum[0]

        # 计算按下时间，判定单击双击长按
        speed = utime.ticks_diff(utime.ticks_ms(), start)
        if speed < 1000:
            self.timer1.start(self.period, 0, self.once_callback)
            return
        else:
            # print('{} 长按 {}ms'.format(self.key,speed))
            # 按下时间大于1000ms就判定为长按
            self.long_callback()

if __name__ == '__main__':
    key = KeypadManager()
    key.post_processor_after_instantiation()