Q(function)
Q(function)
Q(__SIZE__)
Q(__dereference__)
Q(__cast__)
Q(Blob)
Q(ptr_val)
Q(str_val)
Q(int_val)
Q(uint_val)
Q(short_val)
Q(ushort_val)
Q(char_val)
Q(uchar_val)
Q(ptr_val)
Q(str_val)
Q(int_val)
Q(uint_val)
Q(short_val)
Q(ushort_val)
Q(char_val)
Q(uchar_val)
Q(C_Pointer)
Q(ENUM_LV_DPI)
Q(TRACE)
Q(INFO)
Q(WARN)
Q(ERROR)
Q(USER)
Q(NONE)
Q(ENUM_LV_LOG_LEVEL)
Q(INV)
Q(OK)
Q(LV_RES)
Q(INFINITE)
Q(ENUM_LV_ANIM_REPEAT)
Q(TRANSP)
Q(_0)
Q(_10)
Q(_20)
Q(_30)
Q(_40)
Q(_50)
Q(_60)
Q(_70)
Q(_80)
Q(_90)
Q(_100)
Q(COVER)
Q(LV_OPA)
Q(MAX)
Q(MIN)
Q(ENUM_LV_COORD)
Q(DEFAULT)
Q(TOP_LEFT)
Q(TOP_MID)
Q(TOP_RIGHT)
Q(BOTTOM_LEFT)
Q(BOTTOM_MID)
Q(BOTTOM_RIGHT)
Q(LEFT_MID)
Q(RIGHT_MID)
Q(CENTER)
Q(OUT_TOP_LEFT)
Q(OUT_TOP_MID)
Q(OUT_TOP_RIGHT)
Q(OUT_BOTTOM_LEFT)
Q(OUT_BOTTOM_MID)
Q(OUT_BOTTOM_RIGHT)
Q(OUT_LEFT_TOP)
Q(OUT_LEFT_MID)
Q(OUT_LEFT_BOTTOM)
Q(OUT_RIGHT_TOP)
Q(OUT_RIGHT_MID)
Q(OUT_RIGHT_BOTTOM)
Q(LV_ALIGN)
Q(NONE)
Q(LEFT)
Q(RIGHT)
Q(TOP)
Q(BOTTOM)
Q(HOR)
Q(VER)
Q(ALL)
Q(LV_DIR)
Q(CONTENT)
Q(ENUM_LV_SIZE)
Q(UNKNOWN)
Q(RAW)
Q(RAW_ALPHA)
Q(RAW_CHROMA_KEYED)
Q(TRUE_COLOR)
Q(TRUE_COLOR_ALPHA)
Q(TRUE_COLOR_CHROMA_KEYED)
Q(INDEXED_1BIT)
Q(INDEXED_2BIT)
Q(INDEXED_4BIT)
Q(INDEXED_8BIT)
Q(ALPHA_1BIT)
Q(ALPHA_2BIT)
Q(ALPHA_4BIT)
Q(ALPHA_8BIT)
Q(RESERVED_15)
Q(RESERVED_16)
Q(RESERVED_17)
Q(RESERVED_18)
Q(RESERVED_19)
Q(RESERVED_20)
Q(RESERVED_21)
Q(RESERVED_22)
Q(RESERVED_23)
Q(USER_ENCODED_0)
Q(USER_ENCODED_1)
Q(USER_ENCODED_2)
Q(USER_ENCODED_3)
Q(USER_ENCODED_4)
Q(USER_ENCODED_5)
Q(USER_ENCODED_6)
Q(USER_ENCODED_7)
Q(LV_IMG_CF)
Q(NONE)
Q(HOR)
Q(VER)
Q(BOTH)
Q(LV_FONT_SUBPX)
Q(NONE)
Q(RECOLOR)
Q(EXPAND)
Q(FIT)
Q(LV_TEXT_FLAG)
Q(WAIT)
Q(PAR)
Q(IN)
Q(LV_TEXT_CMD_STATE)
Q(AUTO)
Q(LEFT)
Q(CENTER)
Q(RIGHT)
Q(LV_TEXT_ALIGN)
Q(LTR)
Q(RTL)
Q(AUTO)
Q(NEUTRAL)
Q(WEAK)
Q(LV_BASE_DIR)
Q(NONE)
Q(ENUM_LV_IMG_ZOOM)
Q(NORMAL)
Q(ADDITIVE)
Q(SUBTRACTIVE)
Q(LV_BLEND_MODE)
Q(NONE)
Q(UNDERLINE)
Q(STRIKETHROUGH)
Q(LV_TEXT_DECOR)
Q(NONE)
Q(BOTTOM)
Q(TOP)
Q(LEFT)
Q(RIGHT)
Q(FULL)
Q(INTERNAL)
Q(LV_BORDER_SIDE)
Q(NONE)
Q(VER)
Q(HOR)
Q(LV_GRAD_DIR)
Q(DEFAULT)
Q(CHECKED)
Q(FOCUSED)
Q(FOCUS_KEY)
Q(EDITED)
Q(HOVERED)
Q(PRESSED)
Q(SCROLLED)
Q(DISABLED)
Q(USER_1)
Q(USER_2)
Q(USER_3)
Q(USER_4)
Q(ANY)
Q(LV_STATE)
Q(MAIN)
Q(SCROLLBAR)
Q(INDICATOR)
Q(KNOB)
Q(SELECTED)
Q(ITEMS)
Q(TICKS)
Q(CURSOR)
Q(CUSTOM_FIRST)
Q(ANY)
Q(LV_PART)
Q(HIDDEN)
Q(CLICKABLE)
Q(CLICK_FOCUSABLE)
Q(CHECKABLE)
Q(SCROLLABLE)
Q(SCROLL_ELASTIC)
Q(SCROLL_MOMENTUM)
Q(SCROLL_ONE)
Q(SCROLL_CHAIN)
Q(SCROLL_ON_FOCUS)
Q(SNAPPABLE)
Q(PRESS_LOCK)
Q(EVENT_BUBBLE)
Q(GESTURE_BUBBLE)
Q(ADV_HITTEST)
Q(IGNORE_LAYOUT)
Q(FLOATING)
Q(LAYOUT_1)
Q(LAYOUT_2)
Q(WIDGET_1)
Q(WIDGET_2)
Q(USER_1)
Q(USER_2)
Q(USER_3)
Q(USER_4)
Q(LV_OBJ_FLAG)
Q(OFF)
Q(ON)
Q(ACTIVE)
Q(AUTO)
Q(LV_SCROLLBAR_MODE)
Q(NONE)
Q(START)
Q(END)
Q(CENTER)
Q(LV_SCROLL_SNAP)
Q(OK)
Q(HW_ERR)
Q(FS_ERR)
Q(NOT_EX)
Q(FULL)
Q(LOCKED)
Q(DENIED)
Q(BUSY)
Q(TOUT)
Q(NOT_IMP)
Q(OUT_OF_MEM)
Q(INV_PARAM)
Q(UNKNOWN)
Q(LV_FS_RES)
Q(WR)
Q(RD)
Q(LV_FS_MODE)
Q(VARIABLE)
Q(FILE)
Q(SYMBOL)
Q(UNKNOWN)
Q(LV_IMG_SRC)
Q(TRANSP)
Q(FULL_COVER)
Q(CHANGED)
Q(UNKNOWN)
Q(LV_DRAW_MASK_RES)
Q(LINE)
Q(ANGLE)
Q(RADIUS)
Q(FADE)
Q(MAP)
Q(LV_DRAW_MASK_TYPE)
Q(LEFT)
Q(RIGHT)
Q(TOP)
Q(BOTTOM)
Q(LV_DRAW_MASK_LINE_SIDE)
Q(CIRCLE)
Q(ENUM_LV_RADIUS)
Q(UP)
Q(DOWN)
Q(RIGHT)
Q(LEFT)
Q(ESC)
Q(DEL)
Q(BACKSPACE)
Q(ENTER)
Q(NEXT)
Q(PREV)
Q(HOME)
Q(END)
Q(LV_KEY)
Q(FORMAT0_FULL)
Q(SPARSE_FULL)
Q(FORMAT0_TINY)
Q(SPARSE_TINY)
Q(LV_FONT_FMT_TXT_CMAP)
Q(NORMAL)
Q(SYMMETRICAL)
Q(REVERSE)
Q(LV_ARC_MODE)
Q(VIRTUAL)
Q(REAL)
Q(LV_IMG_SIZE_MODE)
Q(NUM)
Q(ENUM_LV_LABEL_DOT)
Q(LAST)
Q(ENUM_LV_LABEL_POS)
Q(OFF)
Q(ENUM_LV_LABEL_TEXT_SELECTION)
Q(WRAP)
Q(DOT)
Q(SCROLL)
Q(SCROLL_CIRCULAR)
Q(CLIP)
Q(LV_LABEL_LONG)
Q(NONE)
Q(ENUM_LV_TABLE_CELL)
Q(MERGE_RIGHT)
Q(TEXT_CROP)
Q(CUSTOM_1)
Q(CUSTOM_2)
Q(CUSTOM_3)
Q(CUSTOM_4)
Q(LV_TABLE_CELL_CTRL)
Q(NORMAL)
Q(SYMMETRICAL)
Q(RANGE)
Q(LV_BAR_MODE)
Q(NORMAL)
Q(SYMMETRICAL)
Q(RANGE)
Q(LV_SLIDER_MODE)
Q(NONE)
Q(ENUM_LV_BTNMATRIX_BTN)
Q(HIDDEN)
Q(NO_REPEAT)
Q(DISABLED)
Q(CHECKABLE)
Q(CHECKED)
Q(CLICK_TRIG)
Q(RECOLOR)
Q(CUSTOM_1)
Q(CUSTOM_2)
Q(LV_BTNMATRIX_CTRL)
Q(LAST)
Q(ENUM_LV_DROPDOWN_POS)
Q(NORMAL)
Q(INFINITE)
Q(LV_ROLLER_MODE)
Q(LAST)
Q(ENUM_LV_TEXTAREA_CURSOR)
Q(PLACEHOLDER)
Q(LV_PART_TEXTAREA)
Q(MAIN)
Q(LV_ANIM_IMG_PART)
Q(NONE)
Q(ENUM_LV_CHART_POINT)
Q(NONE)
Q(LINE)
Q(BAR)
Q(SCATTER)
Q(LV_CHART_TYPE)
Q(SHIFT)
Q(CIRCULAR)
Q(LV_CHART_UPDATE_MODE)
Q(PRIMARY_Y)
Q(SECONDARY_Y)
Q(PRIMARY_X)
Q(SECONDARY_X)
Q(LV_CHART_AXIS)
Q(TEXT_LOWER)
Q(TEXT_UPPER)
Q(SPECIAL)
Q(NUMBER)
Q(LV_KEYBOARD_MODE)
Q(TRACK)
Q(ENUM_LV_OBJ_FLAG_FLEX_IN_NEW)
Q(HUE)
Q(SATURATION)
Q(VALUE)
Q(LV_COLORWHEEL_MODE)
Q(CLIP)
Q(ELLIPSIS)
Q(LV_SPAN_OVERFLOW)
Q(FIXED)
Q(EXPAND)
Q(BREAK)
Q(LV_SPAN_MODE)
Q(CONTENT)
Q(ENUM_LV_GRID)
Q(LAST)
Q(ENUM_LV_GRID_TEMPLATE)
Q(OFF)
Q(ON)
Q(LV_ANIM)
Q(RED)
Q(PINK)
Q(PURPLE)
Q(DEEP_PURPLE)
Q(INDIGO)
Q(BLUE)
Q(LIGHT_BLUE)
Q(CYAN)
Q(TEAL)
Q(GREEN)
Q(LIGHT_GREEN)
Q(LIME)
Q(YELLOW)
Q(AMBER)
Q(ORANGE)
Q(DEEP_ORANGE)
Q(BROWN)
Q(BLUE_GREY)
Q(GREY)
Q(NONE)
Q(LV_PALETTE)
Q(NONE)
Q(_90)
Q(_180)
Q(_270)
Q(LV_DISP_ROT)
Q(NONE)
Q(POINTER)
Q(KEYPAD)
Q(BUTTON)
Q(ENCODER)
Q(LV_INDEV_TYPE)
Q(RELEASED)
Q(PRESSED)
Q(LV_INDEV_STATE)
Q(PROP_INV)
Q(WIDTH)
Q(MIN_WIDTH)
Q(MAX_WIDTH)
Q(HEIGHT)
Q(MIN_HEIGHT)
Q(MAX_HEIGHT)
Q(X)
Q(Y)
Q(ALIGN)
Q(TRANSFORM_WIDTH)
Q(TRANSFORM_HEIGHT)
Q(TRANSLATE_X)
Q(TRANSLATE_Y)
Q(TRANSFORM_ZOOM)
Q(TRANSFORM_ANGLE)
Q(PAD_TOP)
Q(PAD_BOTTOM)
Q(PAD_LEFT)
Q(PAD_RIGHT)
Q(PAD_ROW)
Q(PAD_COLUMN)
Q(BG_COLOR)
Q(BG_OPA)
Q(BG_GRAD_COLOR)
Q(BG_GRAD_DIR)
Q(BG_MAIN_STOP)
Q(BG_GRAD_STOP)
Q(BG_IMG_SRC)
Q(BG_IMG_OPA)
Q(BG_IMG_RECOLOR)
Q(BG_IMG_RECOLOR_OPA)
Q(BG_IMG_TILED)
Q(BORDER_COLOR)
Q(BORDER_OPA)
Q(BORDER_WIDTH)
Q(BORDER_SIDE)
Q(BORDER_POST)
Q(OUTLINE_WIDTH)
Q(OUTLINE_COLOR)
Q(OUTLINE_OPA)
Q(OUTLINE_PAD)
Q(SHADOW_WIDTH)
Q(SHADOW_OFS_X)
Q(SHADOW_OFS_Y)
Q(SHADOW_SPREAD)
Q(SHADOW_COLOR)
Q(SHADOW_OPA)
Q(IMG_OPA)
Q(IMG_RECOLOR)
Q(IMG_RECOLOR_OPA)
Q(LINE_WIDTH)
Q(LINE_DASH_WIDTH)
Q(LINE_DASH_GAP)
Q(LINE_ROUNDED)
Q(LINE_COLOR)
Q(LINE_OPA)
Q(ARC_WIDTH)
Q(ARC_ROUNDED)
Q(ARC_COLOR)
Q(ARC_OPA)
Q(ARC_IMG_SRC)
Q(TEXT_COLOR)
Q(TEXT_OPA)
Q(TEXT_FONT)
Q(TEXT_LETTER_SPACE)
Q(TEXT_LINE_SPACE)
Q(TEXT_DECOR)
Q(TEXT_ALIGN)
Q(RADIUS)
Q(CLIP_CORNER)
Q(OPA)
Q(COLOR_FILTER_DSC)
Q(COLOR_FILTER_OPA)
Q(ANIM_TIME)
Q(ANIM_SPEED)
Q(TRANSITION)
Q(BLEND_MODE)
Q(LAYOUT)
Q(BASE_DIR)
Q(PROP_ANY)
Q(LV_STYLE)
Q(RECTANGLE)
Q(BORDER_POST)
Q(SCROLLBAR)
Q(LV_OBJ_DRAW_PART)
Q(NEXT)
Q(SKIP_CHILDREN)
Q(END)
Q(LV_OBJ_TREE_WALK)
Q(SET)
Q(CUR)
Q(END)
Q(LV_FS_SEEK)
Q(COVER)
Q(NOT_COVER)
Q(MASKED)
Q(LV_COVER_RES)
Q(INHERIT)
Q(TRUE)
Q(FALSE)
Q(LV_OBJ_CLASS_EDITABLE)
Q(INHERIT)
Q(TRUE)
Q(FALSE)
Q(LV_OBJ_CLASS_GROUP_DEF)
Q(ALL)
Q(PRESSED)
Q(PRESSING)
Q(PRESS_LOST)
Q(SHORT_CLICKED)
Q(LONG_PRESSED)
Q(LONG_PRESSED_REPEAT)
Q(CLICKED)
Q(RELEASED)
Q(SCROLL_BEGIN)
Q(SCROLL_END)
Q(SCROLL)
Q(GESTURE)
Q(KEY)
Q(FOCUSED)
Q(DEFOCUSED)
Q(LEAVE)
Q(HIT_TEST)
Q(COVER_CHECK)
Q(REFR_EXT_DRAW_SIZE)
Q(DRAW_MAIN_BEGIN)
Q(DRAW_MAIN)
Q(DRAW_MAIN_END)
Q(DRAW_POST_BEGIN)
Q(DRAW_POST)
Q(DRAW_POST_END)
Q(DRAW_PART_BEGIN)
Q(DRAW_PART_END)
Q(VALUE_CHANGED)
Q(INSERT)
Q(REFRESH)
Q(READY)
Q(CANCEL)
Q(DELETE)
Q(CHILD_CHANGED)
Q(SIZE_CHANGED)
Q(STYLE_CHANGED)
Q(LAYOUT_CHANGED)
Q(GET_SELF_SIZE)
Q(LV_EVENT)
Q(NEXT)
Q(PREV)
Q(LV_GROUP_REFOCUS_POLICY)
Q(NONE)
Q(OVER_LEFT)
Q(OVER_RIGHT)
Q(OVER_TOP)
Q(OVER_BOTTOM)
Q(MOVE_LEFT)
Q(MOVE_RIGHT)
Q(MOVE_TOP)
Q(MOVE_BOTTOM)
Q(FADE_ON)
Q(LV_SCR_LOAD_ANIM)
Q(PLAIN)
Q(COMPRESSED)
Q(COMPRESSED_NO_PREFILTER)
Q(LV_FONT_FMT_TXT)
Q(BACKGROUND)
Q(FOREGROUND)
Q(KNOB)
Q(LV_ARC_DRAW_PART)
Q(CELL)
Q(LV_TABLE_DRAW_PART)
Q(BOX)
Q(LV_CHECKBOX_DRAW_PART)
Q(INDICATOR)
Q(LV_BAR_DRAW_PART)
Q(KNOB)
Q(KNOB_LEFT)
Q(LV_SLIDER_DRAW_PART)
Q(BTN)
Q(LV_BTNMATRIX_DRAW_PART)
Q(DIV_LINE_INIT)
Q(DIV_LINE_HOR)
Q(DIV_LINE_VER)
Q(LINE_AND_POINT)
Q(BAR)
Q(CURSOR)
Q(TICK_LABEL)
Q(LV_CHART_DRAW_PART)
Q(START)
Q(END)
Q(CENTER)
Q(SPACE_EVENLY)
Q(SPACE_AROUND)
Q(SPACE_BETWEEN)
Q(LV_FLEX_ALIGN)
Q(ROW)
Q(COLUMN)
Q(ROW_WRAP)
Q(ROW_REVERSE)
Q(ROW_WRAP_REVERSE)
Q(COLUMN_WRAP)
Q(COLUMN_REVERSE)
Q(COLUMN_WRAP_REVERSE)
Q(LV_FLEX_FLOW)
Q(NEEDLE_IMG)
Q(NEEDLE_LINE)
Q(SCALE_LINES)
Q(ARC)
Q(LV_METER_INDICATOR_TYPE)
Q(ARC)
Q(NEEDLE_LINE)
Q(NEEDLE_IMG)
Q(TICK)
Q(LV_METER_DRAW_PART)
Q(RECTANGLE)
Q(LV_LED_DRAW_PART)
Q(RELEASED)
Q(PRESSED)
Q(DISABLED)
Q(CHECKED_RELEASED)
Q(CHECKED_PRESSED)
Q(CHECKED_DISABLED)
Q(LV_IMGBTN_STATE)
Q(START)
Q(CENTER)
Q(END)
Q(STRETCH)
Q(SPACE_EVENLY)
Q(SPACE_AROUND)
Q(SPACE_BETWEEN)
Q(LV_GRID_ALIGN)
Q(AUDIO)
Q(VIDEO)
Q(LIST)
Q(OK)
Q(CLOSE)
Q(POWER)
Q(SETTINGS)
Q(HOME)
Q(DOWNLOAD)
Q(DRIVE)
Q(REFRESH)
Q(MUTE)
Q(VOLUME_MID)
Q(VOLUME_MAX)
Q(IMAGE)
Q(EDIT)
Q(PREV)
Q(PLAY)
Q(PAUSE)
Q(STOP)
Q(NEXT)
Q(EJECT)
Q(LEFT)
Q(RIGHT)
Q(PLUS)
Q(MINUS)
Q(EYE_OPEN)
Q(EYE_CLOSE)
Q(WARNING)
Q(SHUFFLE)
Q(UP)
Q(DOWN)
Q(LOOP)
Q(DIRECTORY)
Q(UPLOAD)
Q(CALL)
Q(CUT)
Q(COPY)
Q(SAVE)
Q(CHARGE)
Q(PASTE)
Q(BELL)
Q(KEYBOARD)
Q(GPS)
Q(FILE)
Q(WIFI)
Q(BATTERY_FULL)
Q(BATTERY_3)
Q(BATTERY_2)
Q(BATTERY_1)
Q(BATTERY_EMPTY)
Q(USB)
Q(BLUETOOTH)
Q(TRASH)
Q(BACKSPACE)
Q(SD_CARD)
Q(NEW_LINE)
Q(DUMMY)
Q(BULLET)
Q(LV_SYMBOL)
Q(blue)
Q(green)
Q(red)
Q(alpha)
Q(blue)
Q(green)
Q(red)
Q(alpha)
Q(lv_color32_ch_t)
Q(ch)
Q(full)
Q(ch)
Q(full)
Q(lv_color32_t)
Q(filter_cb)
Q(lv_color_filter_dsc_t_filter_cb)
Q(user_data)
Q(filter_cb)
Q(lv_color_filter_dsc_t_filter_cb)
Q(user_data)
Q(lv_color_filter_dsc_t)
Q(var)
Q(exec_cb)
Q(lv_anim_t_exec_cb)
Q(start_cb)
Q(lv_anim_t_start_cb)
Q(ready_cb)
Q(lv_anim_t_ready_cb)
Q(get_value_cb)
Q(lv_anim_t_get_value_cb)
Q(user_data)
Q(path_cb)
Q(lv_anim_t_path_cb)
Q(start_value)
Q(current_value)
Q(end_value)
Q(time)
Q(act_time)
Q(playback_delay)
Q(playback_time)
Q(repeat_delay)
Q(repeat_cnt)
Q(early_apply)
Q(playback_now)
Q(run_round)
Q(start_cb_called)
Q(var)
Q(exec_cb)
Q(lv_anim_t_exec_cb)
Q(start_cb)
Q(lv_anim_t_start_cb)
Q(ready_cb)
Q(lv_anim_t_ready_cb)
Q(get_value_cb)
Q(lv_anim_t_get_value_cb)
Q(user_data)
Q(path_cb)
Q(lv_anim_t_path_cb)
Q(start_value)
Q(current_value)
Q(end_value)
Q(time)
Q(act_time)
Q(playback_delay)
Q(playback_time)
Q(repeat_delay)
Q(repeat_cnt)
Q(early_apply)
Q(playback_now)
Q(run_round)
Q(start_cb_called)
Q(lv_anim_t)
Q(props)
Q(user_data)
Q(path_xcb)
Q(lv_style_transition_dsc_t_path_xcb)
Q(time)
Q(delay)
Q(props)
Q(user_data)
Q(path_xcb)
Q(lv_style_transition_dsc_t_path_xcb)
Q(time)
Q(delay)
Q(lv_style_transition_dsc_t)
Q(adv_w)
Q(box_w)
Q(box_h)
Q(ofs_x)
Q(ofs_y)
Q(bpp)
Q(adv_w)
Q(box_w)
Q(box_h)
Q(ofs_x)
Q(ofs_y)
Q(bpp)
Q(lv_font_glyph_dsc_t)
Q(get_glyph_dsc)
Q(lv_font_t_get_glyph_dsc)
Q(get_glyph_bitmap)
Q(lv_font_t_get_glyph_bitmap)
Q(line_height)
Q(base_line)
Q(subpx)
Q(underline_position)
Q(underline_thickness)
Q(dsc)
Q(user_data)
Q(get_glyph_dsc)
Q(lv_font_t_get_glyph_dsc)
Q(get_glyph_bitmap)
Q(lv_font_t_get_glyph_bitmap)
Q(line_height)
Q(base_line)
Q(subpx)
Q(underline_position)
Q(underline_thickness)
Q(dsc)
Q(user_data)
Q(lv_font_t)
Q(x1)
Q(y1)
Q(x2)
Q(y2)
Q(x1)
Q(y1)
Q(x2)
Q(y2)
Q(lv_area_t)
Q(buf1)
Q(buf2)
Q(buf_act)
Q(size)
Q(flushing)
Q(flushing_last)
Q(last_area)
Q(last_part)
Q(buf1)
Q(buf2)
Q(buf_act)
Q(size)
Q(flushing)
Q(flushing_last)
Q(last_area)
Q(last_part)
Q(lv_disp_draw_buf_t)
Q(hor_res)
Q(ver_res)
Q(draw_buf)
Q(full_refresh)
Q(sw_rotate)
Q(antialiasing)
Q(rotated)
Q(screen_transp)
Q(dpi)
Q(flush_cb)
Q(lv_disp_drv_t_flush_cb)
Q(rounder_cb)
Q(lv_disp_drv_t_rounder_cb)
Q(set_px_cb)
Q(lv_disp_drv_t_set_px_cb)
Q(monitor_cb)
Q(lv_disp_drv_t_monitor_cb)
Q(wait_cb)
Q(lv_disp_drv_t_wait_cb)
Q(clean_dcache_cb)
Q(lv_disp_drv_t_clean_dcache_cb)
Q(drv_update_cb)
Q(lv_disp_drv_t_drv_update_cb)
Q(color_chroma_key)
Q(user_data)
Q(hor_res)
Q(ver_res)
Q(draw_buf)
Q(full_refresh)
Q(sw_rotate)
Q(antialiasing)
Q(rotated)
Q(screen_transp)
Q(dpi)
Q(flush_cb)
Q(lv_disp_drv_t_flush_cb)
Q(rounder_cb)
Q(lv_disp_drv_t_rounder_cb)
Q(set_px_cb)
Q(lv_disp_drv_t_set_px_cb)
Q(monitor_cb)
Q(lv_disp_drv_t_monitor_cb)
Q(wait_cb)
Q(lv_disp_drv_t_wait_cb)
Q(clean_dcache_cb)
Q(lv_disp_drv_t_clean_dcache_cb)
Q(drv_update_cb)
Q(lv_disp_drv_t_drv_update_cb)
Q(color_chroma_key)
Q(user_data)
Q(lv_disp_drv_t)
Q(period)
Q(last_run)
Q(timer_cb)
Q(lv_timer_t_timer_cb)
Q(user_data)
Q(repeat_count)
Q(paused)
Q(period)
Q(last_run)
Q(timer_cb)
Q(lv_timer_t_timer_cb)
Q(user_data)
Q(repeat_count)
Q(paused)
Q(lv_timer_t)
Q(apply_cb)
Q(lv_theme_t_apply_cb)
Q(parent)
Q(user_data)
Q(disp)
Q(color_primary)
Q(color_secondary)
Q(font_small)
Q(font_normal)
Q(font_large)
Q(flags)
Q(apply_cb)
Q(lv_theme_t_apply_cb)
Q(parent)
Q(user_data)
Q(disp)
Q(color_primary)
Q(color_secondary)
Q(font_small)
Q(font_normal)
Q(font_large)
Q(flags)
Q(lv_theme_t)
Q(driver)
Q(refr_timer)
Q(theme)
Q(screens)
Q(act_scr)
Q(prev_scr)
Q(scr_to_load)
Q(top_layer)
Q(sys_layer)
Q(screen_cnt)
Q(del_prev)
Q(bg_opa)
Q(bg_color)
Q(bg_img)
Q(inv_areas)
Q(inv_area_joined)
Q(inv_p)
Q(last_activity_time)
Q(driver)
Q(refr_timer)
Q(theme)
Q(screens)
Q(act_scr)
Q(prev_scr)
Q(scr_to_load)
Q(top_layer)
Q(sys_layer)
Q(screen_cnt)
Q(del_prev)
Q(bg_opa)
Q(bg_color)
Q(bg_img)
Q(inv_areas)
Q(inv_area_joined)
Q(inv_p)
Q(last_activity_time)
Q(lv_disp_t)
Q(lv_obj_tree_walk_cb)
Q(lv_obj_tree_walk_cb)
Q(x)
Q(y)
Q(x)
Q(y)
Q(lv_point_t)
Q(num)
Q(ptr)
Q(color)
Q(num)
Q(ptr)
Q(color)
Q(lv_style_value_t)
Q(prop)
Q(value)
Q(prop)
Q(value)
Q(lv_style_const_prop_t)
Q(value1)
Q(values_and_props)
Q(const_props)
Q(value1)
Q(values_and_props)
Q(const_props)
Q(lv_style_v_p_t)
Q(v_p)
Q(prop1)
Q(has_group)
Q(prop_cnt)
Q(v_p)
Q(prop1)
Q(has_group)
Q(prop_cnt)
Q(lv_style_t)
Q(radius)
Q(blend_mode)
Q(bg_color)
Q(bg_opa)
Q(bg_img_src)
Q(bg_img_symbol_font)
Q(bg_img_recolor)
Q(bg_img_opa)
Q(bg_img_recolor_opa)
Q(bg_img_tiled)
Q(border_color)
Q(border_width)
Q(border_opa)
Q(border_post)
Q(border_side)
Q(outline_color)
Q(outline_width)
Q(outline_pad)
Q(outline_opa)
Q(shadow_color)
Q(shadow_width)
Q(shadow_ofs_x)
Q(shadow_ofs_y)
Q(shadow_spread)
Q(shadow_opa)
Q(radius)
Q(blend_mode)
Q(bg_color)
Q(bg_opa)
Q(bg_img_src)
Q(bg_img_symbol_font)
Q(bg_img_recolor)
Q(bg_img_opa)
Q(bg_img_recolor_opa)
Q(bg_img_tiled)
Q(border_color)
Q(border_width)
Q(border_opa)
Q(border_post)
Q(border_side)
Q(outline_color)
Q(outline_width)
Q(outline_pad)
Q(outline_opa)
Q(shadow_color)
Q(shadow_width)
Q(shadow_ofs_x)
Q(shadow_ofs_y)
Q(shadow_spread)
Q(shadow_opa)
Q(lv_draw_rect_dsc_t)
Q(font)
Q(sel_start)
Q(sel_end)
Q(color)
Q(sel_color)
Q(sel_bg_color)
Q(line_space)
Q(letter_space)
Q(ofs_x)
Q(ofs_y)
Q(opa)
Q(bidi_dir)
Q(flag)
Q(align)
Q(decor)
Q(blend_mode)
Q(font)
Q(sel_start)
Q(sel_end)
Q(color)
Q(sel_color)
Q(sel_bg_color)
Q(line_space)
Q(letter_space)
Q(ofs_x)
Q(ofs_y)
Q(opa)
Q(bidi_dir)
Q(flag)
Q(align)
Q(decor)
Q(blend_mode)
Q(lv_draw_label_dsc_t)
Q(angle)
Q(zoom)
Q(pivot)
Q(recolor)
Q(recolor_opa)
Q(opa)
Q(blend_mode)
Q(frame_id)
Q(antialias)
Q(angle)
Q(zoom)
Q(pivot)
Q(recolor)
Q(recolor_opa)
Q(opa)
Q(blend_mode)
Q(frame_id)
Q(antialias)
Q(lv_draw_img_dsc_t)
Q(color)
Q(width)
Q(dash_width)
Q(dash_gap)
Q(opa)
Q(blend_mode)
Q(round_start)
Q(round_end)
Q(raw_end)
Q(color)
Q(width)
Q(dash_width)
Q(dash_gap)
Q(opa)
Q(blend_mode)
Q(round_start)
Q(round_end)
Q(raw_end)
Q(lv_draw_line_dsc_t)
Q(color)
Q(width)
Q(img_src)
Q(opa)
Q(blend_mode)
Q(rounded)
Q(color)
Q(width)
Q(img_src)
Q(opa)
Q(blend_mode)
Q(rounded)
Q(lv_draw_arc_dsc_t)
Q(target)
Q(current_target)
Q(code)
Q(user_data)
Q(param)
Q(prev)
Q(deleted)
Q(target)
Q(current_target)
Q(code)
Q(user_data)
Q(param)
Q(prev)
Q(deleted)
Q(lv_event_t)
Q(base_class)
Q(constructor_cb)
Q(lv_obj_class_t_constructor_cb)
Q(destructor_cb)
Q(lv_obj_class_t_destructor_cb)
Q(user_data)
Q(event_cb)
Q(lv_obj_class_t_event_cb)
Q(width_def)
Q(height_def)
Q(editable)
Q(group_def)
Q(instance_size)
Q(base_class)
Q(constructor_cb)
Q(lv_obj_class_t_constructor_cb)
Q(destructor_cb)
Q(lv_obj_class_t_destructor_cb)
Q(user_data)
Q(event_cb)
Q(lv_obj_class_t_event_cb)
Q(width_def)
Q(height_def)
Q(editable)
Q(group_def)
Q(instance_size)
Q(lv_obj_class_t)
Q(class_p)
Q(type)
Q(draw_area)
Q(rect_dsc)
Q(label_dsc)
Q(line_dsc)
Q(img_dsc)
Q(arc_dsc)
Q(p1)
Q(p2)
Q(text)
Q(part)
Q(id)
Q(radius)
Q(value)
Q(sub_part_ptr)
Q(class_p)
Q(type)
Q(draw_area)
Q(rect_dsc)
Q(label_dsc)
Q(line_dsc)
Q(img_dsc)
Q(arc_dsc)
Q(p1)
Q(p2)
Q(text)
Q(part)
Q(id)
Q(radius)
Q(value)
Q(sub_part_ptr)
Q(lv_obj_draw_part_dsc_t)
Q(lv_obj_add_event_cb_event_cb)
Q(lv_obj_add_event_cb_event_cb)
Q(lv_obj_t_event_cb)
Q(lv_obj_t_event_cb)
Q(center)
Q(remove_style_all)
Q(get_style_width)
Q(get_style_min_width)
Q(get_style_max_width)
Q(get_style_height)
Q(get_style_min_height)
Q(get_style_max_height)
Q(get_style_x)
Q(get_style_y)
Q(get_style_align)
Q(get_style_transform_width)
Q(get_style_transform_height)
Q(get_style_translate_x)
Q(get_style_translate_y)
Q(get_style_transform_zoom)
Q(get_style_transform_angle)
Q(get_style_pad_top)
Q(get_style_pad_bottom)
Q(get_style_pad_left)
Q(get_style_pad_right)
Q(get_style_pad_row)
Q(get_style_pad_column)
Q(get_style_radius)
Q(get_style_clip_corner)
Q(get_style_opa)
Q(get_style_color_filter_dsc)
Q(get_style_color_filter_opa)
Q(get_style_anim_time)
Q(get_style_anim_speed)
Q(get_style_transition)
Q(get_style_blend_mode)
Q(get_style_layout)
Q(get_style_base_dir)
Q(get_style_bg_color)
Q(get_style_bg_opa)
Q(get_style_bg_grad_color)
Q(get_style_bg_grad_dir)
Q(get_style_bg_main_stop)
Q(get_style_bg_grad_stop)
Q(get_style_bg_img_src)
Q(get_style_bg_img_opa)
Q(get_style_bg_img_recolor)
Q(get_style_bg_img_recolor_opa)
Q(get_style_bg_img_tiled)
Q(get_style_border_color)
Q(get_style_border_opa)
Q(get_style_border_width)
Q(get_style_border_side)
Q(get_style_border_post)
Q(get_style_text_color)
Q(get_style_text_opa)
Q(get_style_text_font)
Q(get_style_text_letter_space)
Q(get_style_text_line_space)
Q(get_style_text_decor)
Q(get_style_text_align)
Q(get_style_img_opa)
Q(get_style_img_recolor)
Q(get_style_img_recolor_opa)
Q(get_style_outline_width)
Q(get_style_outline_color)
Q(get_style_outline_opa)
Q(get_style_outline_pad)
Q(get_style_shadow_width)
Q(get_style_shadow_ofs_x)
Q(get_style_shadow_ofs_y)
Q(get_style_shadow_spread)
Q(get_style_shadow_color)
Q(get_style_shadow_opa)
Q(get_style_line_width)
Q(get_style_line_dash_width)
Q(get_style_line_dash_gap)
Q(get_style_line_rounded)
Q(get_style_line_color)
Q(get_style_line_opa)
Q(get_style_arc_width)
Q(get_style_arc_rounded)
Q(get_style_arc_color)
Q(get_style_arc_opa)
Q(get_style_arc_img_src)
Q(set_style_pad_all)
Q(set_style_pad_hor)
Q(set_style_pad_ver)
Q(set_style_pad_gap)
Q(set_style_size)
Q(set_user_data)
Q(get_user_data)
Q(dpx)
Q(get_style_flex_flow)
Q(get_style_flex_main_place)
Q(get_style_flex_cross_place)
Q(get_style_flex_track_place)
Q(get_style_flex_grow)
Q(get_style_grid_row_dsc_array)
Q(get_style_grid_column_dsc_array)
Q(get_style_grid_row_align)
Q(get_style_grid_column_align)
Q(get_style_grid_cell_column_pos)
Q(get_style_grid_cell_column_span)
Q(get_style_grid_cell_row_pos)
Q(get_style_grid_cell_row_span)
Q(get_style_grid_cell_x_align)
Q(get_style_grid_cell_y_align)
Q(delete)
Q(clean)
Q(del_delayed)
Q(del_anim_ready_cb)
Q(del_async)
Q(set_parent)
Q(move_foreground)
Q(move_background)
Q(get_screen)
Q(get_disp)
Q(get_parent)
Q(get_child)
Q(get_child_cnt)
Q(get_child_id)
Q(tree_walk)
Q(set_pos)
Q(set_x)
Q(set_y)
Q(set_size)
Q(refr_size)
Q(set_width)
Q(set_height)
Q(set_content_width)
Q(set_content_height)
Q(set_layout)
Q(is_layout_positioned)
Q(mark_layout_as_dirty)
Q(update_layout)
Q(set_align)
Q(align)
Q(align_to)
Q(get_coords)
Q(get_x)
Q(get_x2)
Q(get_y)
Q(get_y2)
Q(get_width)
Q(get_height)
Q(get_content_width)
Q(get_content_height)
Q(get_content_coords)
Q(get_self_width)
Q(get_self_height)
Q(refresh_self_size)
Q(refr_pos)
Q(move_to)
Q(move_children_by)
Q(invalidate_area)
Q(invalidate)
Q(area_is_visible)
Q(is_visible)
Q(set_ext_click_area)
Q(get_click_area)
Q(hit_test)
Q(set_scrollbar_mode)
Q(set_scroll_dir)
Q(set_scroll_snap_x)
Q(set_scroll_snap_y)
Q(get_scrollbar_mode)
Q(get_scroll_dir)
Q(get_scroll_snap_x)
Q(get_scroll_snap_y)
Q(get_scroll_x)
Q(get_scroll_y)
Q(get_scroll_top)
Q(get_scroll_bottom)
Q(get_scroll_left)
Q(get_scroll_right)
Q(get_scroll_end)
Q(scroll_by)
Q(scroll_to)
Q(scroll_to_x)
Q(scroll_to_y)
Q(scroll_to_view)
Q(scroll_to_view_recursive)
Q(is_scrolling)
Q(update_snap)
Q(get_scrollbar_area)
Q(scrollbar_invalidate)
Q(readjust_scroll)
Q(add_style)
Q(remove_style)
Q(report_style_change)
Q(refresh_style)
Q(enable_style_refresh)
Q(get_style_prop)
Q(set_local_style_prop)
Q(get_local_style_prop)
Q(remove_local_style_prop)
Q(fade_in)
Q(fade_out)
Q(style_get_selector_state)
Q(style_get_selector_part)
Q(set_style_width)
Q(set_style_min_width)
Q(set_style_max_width)
Q(set_style_height)
Q(set_style_min_height)
Q(set_style_max_height)
Q(set_style_x)
Q(set_style_y)
Q(set_style_align)
Q(set_style_transform_width)
Q(set_style_transform_height)
Q(set_style_translate_x)
Q(set_style_translate_y)
Q(set_style_transform_zoom)
Q(set_style_transform_angle)
Q(set_style_pad_top)
Q(set_style_pad_bottom)
Q(set_style_pad_left)
Q(set_style_pad_right)
Q(set_style_pad_row)
Q(set_style_pad_column)
Q(set_style_radius)
Q(set_style_clip_corner)
Q(set_style_opa)
Q(set_style_color_filter_dsc)
Q(set_style_color_filter_opa)
Q(set_style_anim_time)
Q(set_style_anim_speed)
Q(set_style_transition)
Q(set_style_blend_mode)
Q(set_style_layout)
Q(set_style_base_dir)
Q(set_style_bg_color)
Q(set_style_bg_opa)
Q(set_style_bg_grad_color)
Q(set_style_bg_grad_dir)
Q(set_style_bg_main_stop)
Q(set_style_bg_grad_stop)
Q(set_style_bg_img_src)
Q(set_style_bg_img_opa)
Q(set_style_bg_img_recolor)
Q(set_style_bg_img_recolor_opa)
Q(set_style_bg_img_tiled)
Q(set_style_border_color)
Q(set_style_border_opa)
Q(set_style_border_width)
Q(set_style_border_side)
Q(set_style_border_post)
Q(set_style_text_color)
Q(set_style_text_opa)
Q(set_style_text_font)
Q(set_style_text_letter_space)
Q(set_style_text_line_space)
Q(set_style_text_decor)
Q(set_style_text_align)
Q(set_style_img_opa)
Q(set_style_img_recolor)
Q(set_style_img_recolor_opa)
Q(set_style_outline_width)
Q(set_style_outline_color)
Q(set_style_outline_opa)
Q(set_style_outline_pad)
Q(set_style_shadow_width)
Q(set_style_shadow_ofs_x)
Q(set_style_shadow_ofs_y)
Q(set_style_shadow_spread)
Q(set_style_shadow_color)
Q(set_style_shadow_opa)
Q(set_style_line_width)
Q(set_style_line_dash_width)
Q(set_style_line_dash_gap)
Q(set_style_line_rounded)
Q(set_style_line_color)
Q(set_style_line_opa)
Q(set_style_arc_width)
Q(set_style_arc_rounded)
Q(set_style_arc_color)
Q(set_style_arc_opa)
Q(set_style_arc_img_src)
Q(init_draw_rect_dsc)
Q(init_draw_label_dsc)
Q(init_draw_img_dsc)
Q(init_draw_line_dsc)
Q(init_draw_arc_dsc)
Q(calculate_ext_draw_size)
Q(draw_dsc_init)
Q(draw_part_check_type)
Q(refresh_ext_draw_size)
Q(class_create_obj)
Q(class_init_obj)
Q(is_editable)
Q(is_group_def)
Q(event_base)
Q(add_event_cb)
Q(remove_event_cb)
Q(remove_event_dsc)
Q(add_flag)
Q(clear_flag)
Q(add_state)
Q(clear_state)
Q(has_flag)
Q(has_flag_any)
Q(get_state)
Q(has_state)
Q(get_group)
Q(allocate_spec_attr)
Q(check_type)
Q(has_class)
Q(get_class)
Q(is_valid)
Q(set_flex_flow)
Q(set_flex_align)
Q(set_flex_grow)
Q(set_style_flex_flow)
Q(set_style_flex_main_place)
Q(set_style_flex_cross_place)
Q(set_style_flex_track_place)
Q(set_style_flex_grow)
Q(set_tile)
Q(set_tile_id)
Q(set_grid_dsc_array)
Q(set_grid_align)
Q(set_grid_cell)
Q(set_style_grid_row_dsc_array)
Q(set_style_grid_column_dsc_array)
Q(set_style_grid_row_align)
Q(set_style_grid_column_align)
Q(set_style_grid_cell_column_pos)
Q(set_style_grid_cell_column_span)
Q(set_style_grid_cell_row_pos)
Q(set_style_grid_cell_row_span)
Q(set_style_grid_cell_x_align)
Q(set_style_grid_cell_y_align)
Q(FLAG)
Q(DRAW_PART)
Q(TREE_WALK)
Q(CLASS_EDITABLE)
Q(CLASS_GROUP_DEF)
Q(__cast__)
Q(obj)
Q(set_start_angle)
Q(set_end_angle)
Q(set_angles)
Q(set_bg_start_angle)
Q(set_bg_end_angle)
Q(set_bg_angles)
Q(set_rotation)
Q(set_mode)
Q(set_value)
Q(set_range)
Q(set_change_rate)
Q(get_angle_start)
Q(get_angle_end)
Q(get_bg_angle_start)
Q(get_bg_angle_end)
Q(get_value)
Q(get_min_value)
Q(get_max_value)
Q(get_mode)
Q(MODE)
Q(DRAW_PART)
Q(arc)
Q(btn)
Q(cf)
Q(always_zero)
Q(reserved)
Q(w)
Q(h)
Q(cf)
Q(always_zero)
Q(reserved)
Q(w)
Q(h)
Q(lv_img_header_t)
Q(header)
Q(data_size)
Q(data)
Q(header)
Q(data_size)
Q(data)
Q(lv_img_dsc_t)
Q(info_cb)
Q(lv_img_decoder_t_info_cb)
Q(open_cb)
Q(lv_img_decoder_t_open_cb)
Q(read_line_cb)
Q(lv_img_decoder_t_read_line_cb)
Q(close_cb)
Q(lv_img_decoder_t_close_cb)
Q(user_data)
Q(info_cb)
Q(lv_img_decoder_t_info_cb)
Q(open_cb)
Q(lv_img_decoder_t_open_cb)
Q(read_line_cb)
Q(lv_img_decoder_t_read_line_cb)
Q(close_cb)
Q(lv_img_decoder_t_close_cb)
Q(user_data)
Q(lv_img_decoder_t)
Q(decoder)
Q(src)
Q(color)
Q(frame_id)
Q(src_type)
Q(header)
Q(img_data)
Q(time_to_open)
Q(error_msg)
Q(user_data)
Q(decoder)
Q(src)
Q(color)
Q(frame_id)
Q(src_type)
Q(header)
Q(img_data)
Q(time_to_open)
Q(error_msg)
Q(user_data)
Q(lv_img_decoder_dsc_t)
Q(lv_img_decoder_t_info_cb)
Q(lv_img_decoder_t_info_cb)
Q(lv_img_decoder_t_open_cb)
Q(lv_img_decoder_t_open_cb)
Q(lv_img_decoder_t_read_line_cb)
Q(lv_img_decoder_t_read_line_cb)
Q(lv_img_decoder_t_close_cb)
Q(lv_img_decoder_t_close_cb)
Q(__name__)
Q(__LVGL__)
Q(buf_alloc)
Q(buf_get_px_color)
Q(buf_get_px_alpha)
Q(buf_set_px_color)
Q(buf_set_px_alpha)
Q(buf_set_palette)
Q(buf_free)
Q(buf_get_img_size)
Q(decoder_get_info)
Q(decoder_open)
Q(decoder_read_line)
Q(decoder_close)
Q(decoder_create)
Q(decoder_delete)
Q(decoder_set_info_cb)
Q(decoder_set_open_cb)
Q(decoder_set_read_line_cb)
Q(decoder_set_close_cb)
Q(decoder_built_in_info)
Q(decoder_built_in_open)
Q(decoder_built_in_read_line)
Q(decoder_built_in_close)
Q(cache_set_size)
Q(cache_invalidate_src)
Q(src_get_type)
Q(cf_get_px_size)
Q(cf_is_chroma_keyed)
Q(cf_has_alpha)
Q(set_src)
Q(set_src_change)
Q(set_offset_x)
Q(set_offset_y)
Q(set_angle)
Q(set_pivot)
Q(set_zoom)
Q(set_antialias)
Q(set_size_mode)
Q(get_src)
Q(get_offset_x)
Q(get_offset_y)
Q(get_angle)
Q(get_pivot)
Q(get_zoom)
Q(get_antialias)
Q(get_size_mode)
Q(CF)
Q(SRC)
Q(SIZE_MODE)
Q(img)
Q(set_text)
Q(set_text_static)
Q(set_long_mode)
Q(set_recolor)
Q(set_text_sel_start)
Q(set_text_sel_end)
Q(get_text)
Q(get_long_mode)
Q(get_recolor)
Q(get_letter_pos)
Q(get_letter_on)
Q(is_char_under_pos)
Q(get_text_selection_start)
Q(get_text_selection_end)
Q(ins_text)
Q(cut_text)
Q(LONG)
Q(label)
Q(__del__)
Q(set_points)
Q(set_y_invert)
Q(get_y_invert)
Q(line)
Q(set_cell_value)
Q(set_row_cnt)
Q(set_col_cnt)
Q(set_col_width)
Q(add_cell_ctrl)
Q(clear_cell_ctrl)
Q(get_cell_value)
Q(get_row_cnt)
Q(get_col_cnt)
Q(get_col_width)
Q(has_cell_ctrl)
Q(get_selected_cell)
Q(CELL_CTRL)
Q(DRAW_PART)
Q(table)
Q(set_text)
Q(set_text_static)
Q(get_text)
Q(DRAW_PART)
Q(checkbox)
Q(set_value)
Q(set_start_value)
Q(set_range)
Q(set_mode)
Q(get_value)
Q(get_start_value)
Q(get_min_value)
Q(get_max_value)
Q(get_mode)
Q(MODE)
Q(DRAW_PART)
Q(bar)
Q(set_value)
Q(set_left_value)
Q(set_range)
Q(set_mode)
Q(get_value)
Q(get_left_value)
Q(get_min_value)
Q(get_max_value)
Q(get_mode)
Q(is_dragged)
Q(MODE)
Q(DRAW_PART)
Q(slider)
Q(set_map)
Q(set_ctrl_map)
Q(set_selected_btn)
Q(set_btn_ctrl)
Q(clear_btn_ctrl)
Q(set_btn_ctrl_all)
Q(clear_btn_ctrl_all)
Q(set_btn_width)
Q(set_one_checked)
Q(get_map)
Q(get_selected_btn)
Q(get_btn_text)
Q(has_btn_ctrl)
Q(get_one_checked)
Q(CTRL)
Q(DRAW_PART)
Q(btnmatrix)
Q(set_text)
Q(set_options)
Q(set_options_static)
Q(add_option)
Q(clear_options)
Q(set_selected)
Q(set_dir)
Q(set_symbol)
Q(set_selected_highlight)
Q(get_list)
Q(get_text)
Q(get_options)
Q(get_selected)
Q(get_option_cnt)
Q(get_selected_str)
Q(get_symbol)
Q(get_selected_highlight)
Q(get_dir)
Q(open)
Q(close)
Q(dropdown)
Q(set_options)
Q(set_selected)
Q(set_visible_row_count)
Q(get_selected)
Q(get_selected_str)
Q(get_options)
Q(get_option_cnt)
Q(MODE)
Q(roller)
Q(add_char)
Q(add_text)
Q(del_char)
Q(del_char_forward)
Q(set_text)
Q(set_placeholder_text)
Q(set_cursor_pos)
Q(set_cursor_click_pos)
Q(set_password_mode)
Q(set_one_line)
Q(set_accepted_chars)
Q(set_max_length)
Q(set_insert_replace)
Q(set_text_selection)
Q(set_password_show_time)
Q(set_align)
Q(get_text)
Q(get_placeholder_text)
Q(get_label)
Q(get_cursor_pos)
Q(get_cursor_click_pos)
Q(get_password_mode)
Q(get_one_line)
Q(get_accepted_chars)
Q(get_max_length)
Q(text_is_selected)
Q(get_text_selection)
Q(get_password_show_time)
Q(clear_selection)
Q(cursor_right)
Q(cursor_left)
Q(cursor_down)
Q(cursor_up)
Q(textarea)
Q(set_buffer)
Q(set_px)
Q(set_palette)
Q(get_px)
Q(get_img)
Q(copy_buf)
Q(transform)
Q(blur_hor)
Q(blur_ver)
Q(fill_bg)
Q(draw_rect)
Q(draw_text)
Q(draw_img)
Q(draw_line)
Q(draw_polygon)
Q(draw_arc)
Q(canvas)
Q(switch)
Q(set_src)
Q(start)
Q(set_duration)
Q(set_repeat_count)
Q(animimg)
Q(year)
Q(month)
Q(day)
Q(year)
Q(month)
Q(day)
Q(lv_calendar_date_t)
Q(set_today_date)
Q(set_showed_date)
Q(set_highlighted_dates)
Q(set_day_names)
Q(get_today_date)
Q(get_showed_date)
Q(get_highlighted_dates)
Q(get_highlighted_dates_num)
Q(get_pressed_date)
Q(calendar)
Q(calendar_header_arrow)
Q(calendar_header_dropdown)
Q(x_points)
Q(y_points)
Q(color)
Q(start_point)
Q(hidden)
Q(x_ext_buf_assigned)
Q(y_ext_buf_assigned)
Q(x_axis_sec)
Q(y_axis_sec)
Q(x_points)
Q(y_points)
Q(color)
Q(start_point)
Q(hidden)
Q(x_ext_buf_assigned)
Q(y_ext_buf_assigned)
Q(x_axis_sec)
Q(y_axis_sec)
Q(lv_chart_series_t)
Q(pos)
Q(point_id)
Q(color)
Q(ser)
Q(dir)
Q(pos_set)
Q(pos)
Q(point_id)
Q(color)
Q(ser)
Q(dir)
Q(pos_set)
Q(lv_chart_cursor_t)
Q(set_type)
Q(set_point_count)
Q(set_range)
Q(set_update_mode)
Q(set_div_line_count)
Q(set_zoom_x)
Q(set_zoom_y)
Q(get_zoom_x)
Q(get_zoom_y)
Q(set_axis_tick)
Q(get_type)
Q(get_point_count)
Q(get_x_start_point)
Q(get_point_pos_by_id)
Q(refresh)
Q(add_series)
Q(remove_series)
Q(hide_series)
Q(set_series_color)
Q(set_x_start_point)
Q(get_series_next)
Q(add_cursor)
Q(set_cursor_pos)
Q(set_cursor_point)
Q(get_cursor_point)
Q(set_all_value)
Q(set_next_value)
Q(set_next_value2)
Q(set_value_by_id)
Q(set_value_by_id2)
Q(set_ext_y_array)
Q(set_ext_x_array)
Q(get_y_array)
Q(get_x_array)
Q(get_pressed_point)
Q(TYPE)
Q(UPDATE_MODE)
Q(AXIS)
Q(DRAW_PART)
Q(chart)
Q(get_map_array)
Q(set_textarea)
Q(set_mode)
Q(set_map)
Q(get_textarea)
Q(get_mode)
Q(def_event_cb)
Q(MODE)
Q(keyboard)
Q(add_text)
Q(add_btn)
Q(get_btn_text)
Q(list)
Q(get_title)
Q(get_close_btn)
Q(get_text)
Q(get_btns)
Q(get_active_btn_text)
Q(close)
Q(msgbox)
Q(tick_color)
Q(tick_cnt)
Q(tick_length)
Q(tick_width)
Q(tick_major_color)
Q(tick_major_nth)
Q(tick_major_length)
Q(tick_major_width)
Q(label_gap)
Q(label_color)
Q(min)
Q(max)
Q(r_mod)
Q(angle_range)
Q(rotation)
Q(tick_color)
Q(tick_cnt)
Q(tick_length)
Q(tick_width)
Q(tick_major_color)
Q(tick_major_nth)
Q(tick_major_length)
Q(tick_major_width)
Q(label_gap)
Q(label_color)
Q(min)
Q(max)
Q(r_mod)
Q(angle_range)
Q(rotation)
Q(lv_meter_scale_t)
Q(src)
Q(pivot)
Q(src)
Q(pivot)
Q(lv_meter_indicator_type_data_needle_img_t)
Q(width)
Q(r_mod)
Q(color)
Q(width)
Q(r_mod)
Q(color)
Q(lv_meter_indicator_type_data_needle_line_t)
Q(width)
Q(src)
Q(color)
Q(r_mod)
Q(width)
Q(src)
Q(color)
Q(r_mod)
Q(lv_meter_indicator_type_data_arc_t)
Q(width_mod)
Q(color_start)
Q(color_end)
Q(local_grad)
Q(width_mod)
Q(color_start)
Q(color_end)
Q(local_grad)
Q(lv_meter_indicator_type_data_scale_lines_t)
Q(needle_img)
Q(needle_line)
Q(arc)
Q(scale_lines)
Q(needle_img)
Q(needle_line)
Q(arc)
Q(scale_lines)
Q(lv_meter_indicator_type_data_t)
Q(scale)
Q(type)
Q(opa)
Q(start_value)
Q(end_value)
Q(type_data)
Q(scale)
Q(type)
Q(opa)
Q(start_value)
Q(end_value)
Q(type_data)
Q(lv_meter_indicator_t)
Q(add_scale)
Q(set_scale_ticks)
Q(set_scale_major_ticks)
Q(set_scale_range)
Q(add_needle_line)
Q(add_needle_img)
Q(add_arc)
Q(add_scale_lines)
Q(set_indicator_value)
Q(set_indicator_start_value)
Q(set_indicator_end_value)
Q(INDICATOR_TYPE)
Q(DRAW_PART)
Q(meter)
Q(set_value)
Q(set_rollover)
Q(set_digit_format)
Q(set_step)
Q(set_range)
Q(set_pos)
Q(get_rollover)
Q(get_value)
Q(get_step)
Q(step_next)
Q(step_prev)
Q(increment)
Q(decrement)
Q(spinbox)
Q(spinner)
Q(add_tab)
Q(get_content)
Q(get_tab_btns)
Q(set_act)
Q(get_tab_act)
Q(tabview)
Q(add_tile)
Q(get_tile_act)
Q(tileview)
Q(add_title)
Q(add_btn)
Q(get_header)
Q(get_content)
Q(win)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(K26)
Q(K9)
Q(LV_IME_PINYIN_MODE)
Q(Struct)
Q(py)
Q(py_mb)
Q(lv_pinyin_dict_t)
Q(pinyin_set_keyboard)
Q(pinyin_set_dict)
Q(pinyin_set_mode)
Q(pinyin_get_kb)
Q(pinyin_get_cand_panel)
Q(pinyin_get_comb_panel)
Q(pinyin_get_dict)
Q(pinyin_get_btn_id)
Q(pinyin_set_btn_id)
Q(PINYIN_MODE)
Q(ime_pinyin)
Q(h)
Q(s)
Q(v)
Q(h)
Q(s)
Q(v)
Q(lv_color_hsv_t)
Q(set_hsv)
Q(set_rgb)
Q(set_mode)
Q(set_mode_fixed)
Q(get_hsv)
Q(get_rgb)
Q(get_color_mode)
Q(get_color_mode_fixed)
Q(MODE)
Q(colorwheel)
Q(set_color)
Q(set_brightness)
Q(on)
Q(off)
Q(toggle)
Q(get_brightness)
Q(DRAW_PART)
Q(led)
Q(set_src)
Q(get_src_left)
Q(get_src_middle)
Q(get_src_right)
Q(STATE)
Q(imgbtn)
Q(txt)
Q(spangroup)
Q(style)
Q(static_flag)
Q(txt)
Q(spangroup)
Q(style)
Q(static_flag)
Q(lv_span_t)
Q(new_span)
Q(del_span)
Q(set_align)
Q(set_overflow)
Q(set_indent)
Q(set_mode)
Q(get_child)
Q(get_child_cnt)
Q(get_align)
Q(get_overflow)
Q(get_indent)
Q(get_mode)
Q(get_max_line_h)
Q(get_expand_width)
Q(get_expand_height)
Q(refr_mode)
Q(spangroup)
Q(value)
Q(value)
Q(_lv_mp_int_wrapper)
Q(total_size)
Q(free_cnt)
Q(free_size)
Q(free_biggest_size)
Q(used_cnt)
Q(max_used)
Q(used_pct)
Q(frag_pct)
Q(total_size)
Q(free_cnt)
Q(free_size)
Q(free_biggest_size)
Q(used_cnt)
Q(max_used)
Q(used_pct)
Q(frag_pct)
Q(lv_mem_monitor_t)
Q(point)
Q(key)
Q(btn_id)
Q(enc_diff)
Q(state)
Q(continue_reading)
Q(point)
Q(key)
Q(btn_id)
Q(enc_diff)
Q(state)
Q(continue_reading)
Q(lv_indev_data_t)
Q(type)
Q(read_cb)
Q(lv_indev_drv_t_read_cb)
Q(feedback_cb)
Q(lv_indev_drv_t_feedback_cb)
Q(user_data)
Q(disp)
Q(read_timer)
Q(scroll_limit)
Q(scroll_throw)
Q(gesture_min_velocity)
Q(gesture_limit)
Q(long_press_time)
Q(long_press_repeat_time)
Q(type)
Q(read_cb)
Q(lv_indev_drv_t_read_cb)
Q(feedback_cb)
Q(lv_indev_drv_t_feedback_cb)
Q(user_data)
Q(disp)
Q(read_timer)
Q(scroll_limit)
Q(scroll_throw)
Q(gesture_min_velocity)
Q(gesture_limit)
Q(long_press_time)
Q(long_press_repeat_time)
Q(lv_indev_drv_t)
Q(act_point)
Q(last_point)
Q(last_raw_point)
Q(vect)
Q(scroll_sum)
Q(scroll_throw_vect)
Q(scroll_throw_vect_ori)
Q(act_obj)
Q(last_obj)
Q(scroll_obj)
Q(last_pressed)
Q(scroll_area)
Q(gesture_sum)
Q(scroll_dir)
Q(gesture_dir)
Q(gesture_sent)
Q(act_point)
Q(last_point)
Q(last_raw_point)
Q(vect)
Q(scroll_sum)
Q(scroll_throw_vect)
Q(scroll_throw_vect_ori)
Q(act_obj)
Q(last_obj)
Q(scroll_obj)
Q(last_pressed)
Q(scroll_area)
Q(gesture_sum)
Q(scroll_dir)
Q(gesture_dir)
Q(gesture_sent)
Q(_lv_indev_proc_types_pointer_t)
Q(last_state)
Q(last_key)
Q(last_state)
Q(last_key)
Q(_lv_indev_proc_types_keypad_t)
Q(pointer)
Q(keypad)
Q(pointer)
Q(keypad)
Q(_lv_indev_proc_types_t)
Q(state)
Q(long_pr_sent)
Q(reset_query)
Q(disabled)
Q(wait_until_release)
Q(types)
Q(pr_timestamp)
Q(longpr_rep_timestamp)
Q(state)
Q(long_pr_sent)
Q(reset_query)
Q(disabled)
Q(wait_until_release)
Q(types)
Q(pr_timestamp)
Q(longpr_rep_timestamp)
Q(_lv_indev_proc_t)
Q(n_size)
Q(head)
Q(tail)
Q(n_size)
Q(head)
Q(tail)
Q(lv_ll_t)
Q(obj_ll)
Q(obj_focus)
Q(focus_cb)
Q(lv_group_t_focus_cb)
Q(user_data)
Q(frozen)
Q(editing)
Q(refocus_policy)
Q(wrap)
Q(obj_ll)
Q(obj_focus)
Q(focus_cb)
Q(lv_group_t_focus_cb)
Q(user_data)
Q(frozen)
Q(editing)
Q(refocus_policy)
Q(wrap)
Q(lv_group_t)
Q(driver)
Q(proc)
Q(cursor)
Q(group)
Q(btn_points)
Q(driver)
Q(proc)
Q(cursor)
Q(group)
Q(btn_points)
Q(lv_indev_t)
Q(letter)
Q(ready_cb)
Q(lv_fs_drv_t_ready_cb)
Q(open_cb)
Q(lv_fs_drv_t_open_cb)
Q(close_cb)
Q(lv_fs_drv_t_close_cb)
Q(read_cb)
Q(lv_fs_drv_t_read_cb)
Q(write_cb)
Q(lv_fs_drv_t_write_cb)
Q(seek_cb)
Q(lv_fs_drv_t_seek_cb)
Q(tell_cb)
Q(lv_fs_drv_t_tell_cb)
Q(dir_open_cb)
Q(lv_fs_drv_t_dir_open_cb)
Q(dir_read_cb)
Q(lv_fs_drv_t_dir_read_cb)
Q(dir_close_cb)
Q(lv_fs_drv_t_dir_close_cb)
Q(user_data)
Q(letter)
Q(ready_cb)
Q(lv_fs_drv_t_ready_cb)
Q(open_cb)
Q(lv_fs_drv_t_open_cb)
Q(close_cb)
Q(lv_fs_drv_t_close_cb)
Q(read_cb)
Q(lv_fs_drv_t_read_cb)
Q(write_cb)
Q(lv_fs_drv_t_write_cb)
Q(seek_cb)
Q(lv_fs_drv_t_seek_cb)
Q(tell_cb)
Q(lv_fs_drv_t_tell_cb)
Q(dir_open_cb)
Q(lv_fs_drv_t_dir_open_cb)
Q(dir_read_cb)
Q(lv_fs_drv_t_dir_read_cb)
Q(dir_close_cb)
Q(lv_fs_drv_t_dir_close_cb)
Q(user_data)
Q(lv_fs_drv_t)
Q(file_d)
Q(drv)
Q(file_d)
Q(drv)
Q(lv_fs_file_t)
Q(dir_d)
Q(drv)
Q(dir_d)
Q(drv)
Q(lv_fs_dir_t)
Q(cb)
Q(_lv_draw_mask_common_dsc_t_cb)
Q(type)
Q(cb)
Q(_lv_draw_mask_common_dsc_t_cb)
Q(type)
Q(_lv_draw_mask_common_dsc_t)
Q(p1)
Q(p2)
Q(side)
Q(p1)
Q(p2)
Q(side)
Q(lv_draw_mask_line_param_cfg_t)
Q(dsc)
Q(cfg)
Q(origo)
Q(xy_steep)
Q(yx_steep)
Q(steep)
Q(spx)
Q(flat)
Q(inv)
Q(dsc)
Q(cfg)
Q(origo)
Q(xy_steep)
Q(yx_steep)
Q(steep)
Q(spx)
Q(flat)
Q(inv)
Q(lv_draw_mask_line_param_t)
Q(vertex_p)
Q(start_angle)
Q(end_angle)
Q(vertex_p)
Q(start_angle)
Q(end_angle)
Q(lv_draw_mask_angle_param_cfg_t)
Q(dsc)
Q(cfg)
Q(start_line)
Q(end_line)
Q(delta_deg)
Q(dsc)
Q(cfg)
Q(start_line)
Q(end_line)
Q(delta_deg)
Q(lv_draw_mask_angle_param_t)
Q(rect)
Q(radius)
Q(outer)
Q(rect)
Q(radius)
Q(outer)
Q(lv_draw_mask_radius_param_cfg_t)
Q(i)
Q(f)
Q(i)
Q(f)
Q(lv_sqrt_res_t)
Q(dsc)
Q(cfg)
Q(dsc)
Q(cfg)
Q(lv_draw_mask_radius_param_t)
Q(coords)
Q(y_top)
Q(y_bottom)
Q(opa_top)
Q(opa_bottom)
Q(coords)
Q(y_top)
Q(y_bottom)
Q(opa_top)
Q(opa_bottom)
Q(lv_draw_mask_fade_param_cfg_t)
Q(dsc)
Q(cfg)
Q(dsc)
Q(cfg)
Q(lv_draw_mask_fade_param_t)
Q(coords)
Q(map)
Q(coords)
Q(map)
Q(lv_draw_mask_map_param_cfg_t)
Q(dsc)
Q(cfg)
Q(dsc)
Q(cfg)
Q(lv_draw_mask_map_param_t)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(lv_color_filter_dsc_t_cb)
Q(lv_color_filter_dsc_t_cb)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(init)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(color_to1)
Q(color_to8)
Q(color_to16)
Q(color_to32)
Q(color_mix)
Q(color_premult)
Q(color_mix_with_alpha)
Q(color_brightness)
Q(color_fill)
Q(color_lighten)
Q(color_darken)
Q(color_change_lightness)
Q(color_to_hsv)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(lv_style_transition_dsc_init_path_cb)
Q(lv_style_transition_dsc_init_path_cb)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(init)
Q(lv_anim_t_exec_cb)
Q(lv_anim_t_exec_cb)
Q(lv_anim_t_path_cb)
Q(lv_anim_t_path_cb)
Q(lv_anim_t_start_cb)
Q(lv_anim_t_start_cb)
Q(lv_anim_t_get_value_cb)
Q(lv_anim_t_get_value_cb)
Q(lv_anim_t_exec_cb)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(set_var)
Q(set_exec_cb)
Q(set_time)
Q(set_delay)
Q(set_values)
Q(set_custom_exec_cb)
Q(set_path_cb)
Q(set_start_cb)
Q(set_get_value_cb)
Q(set_ready_cb)
Q(set_playback_time)
Q(set_playback_delay)
Q(set_repeat_count)
Q(set_repeat_delay)
Q(set_early_apply)
Q(get_delay)
Q(custom_del)
Q(init)
Q(start)
Q(path_linear)
Q(path_ease_in)
Q(path_ease_out)
Q(path_ease_in_out)
Q(path_overshoot)
Q(path_bounce)
Q(path_step)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(get_line_height)
Q(get_glyph_bitmap)
Q(get_glyph_dsc)
Q(get_glyph_width)
Q(free)
Q(get_bitmap_fmt_txt)
Q(get_glyph_dsc_fmt_txt)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(dpx)
Q(drv_update)
Q(remove)
Q(set_default)
Q(get_hor_res)
Q(get_ver_res)
Q(get_antialiasing)
Q(get_dpi)
Q(set_rotation)
Q(get_rotation)
Q(get_next)
Q(get_draw_buf)
Q(get_scr_act)
Q(get_scr_prev)
Q(get_layer_top)
Q(get_layer_sys)
Q(set_theme)
Q(get_theme)
Q(set_bg_color)
Q(set_bg_image)
Q(set_bg_opa)
Q(get_inactive_time)
Q(trig_activity)
Q(clean_dcache)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(init)
Q(register)
Q(flush_ready)
Q(flush_is_last)
Q(use_generic_set_px_cb)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(init)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(copy)
Q(get_width)
Q(get_height)
Q(set)
Q(set_width)
Q(set_height)
Q(get_size)
Q(increase)
Q(move)
Q(align)
Q(lv_timer_t_timer_cb)
Q(lv_timer_t_timer_cb)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(_del)
Q(pause)
Q(resume)
Q(set_cb)
Q(set_period)
Q(ready)
Q(set_repeat_count)
Q(reset)
Q(get_next)
Q(lv_theme_t_apply_cb)
Q(lv_theme_t_apply_cb)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(set_parent)
Q(set_apply_cb)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(get_prop_inlined)
Q(set_pad_all)
Q(set_pad_hor)
Q(set_pad_ver)
Q(set_pad_gap)
Q(set_size)
Q(init)
Q(reset)
Q(remove_prop)
Q(set_prop)
Q(get_prop)
Q(is_empty)
Q(set_width)
Q(set_min_width)
Q(set_max_width)
Q(set_height)
Q(set_min_height)
Q(set_max_height)
Q(set_x)
Q(set_y)
Q(set_align)
Q(set_transform_width)
Q(set_transform_height)
Q(set_translate_x)
Q(set_translate_y)
Q(set_transform_zoom)
Q(set_transform_angle)
Q(set_pad_top)
Q(set_pad_bottom)
Q(set_pad_left)
Q(set_pad_right)
Q(set_pad_row)
Q(set_pad_column)
Q(set_radius)
Q(set_clip_corner)
Q(set_opa)
Q(set_color_filter_dsc)
Q(set_color_filter_opa)
Q(set_anim_time)
Q(set_anim_speed)
Q(set_transition)
Q(set_blend_mode)
Q(set_layout)
Q(set_base_dir)
Q(set_bg_color)
Q(set_bg_opa)
Q(set_bg_grad_color)
Q(set_bg_grad_dir)
Q(set_bg_main_stop)
Q(set_bg_grad_stop)
Q(set_bg_img_src)
Q(set_bg_img_opa)
Q(set_bg_img_recolor)
Q(set_bg_img_recolor_opa)
Q(set_bg_img_tiled)
Q(set_border_color)
Q(set_border_opa)
Q(set_border_width)
Q(set_border_side)
Q(set_border_post)
Q(set_text_color)
Q(set_text_opa)
Q(set_text_font)
Q(set_text_font_v2)
Q(set_text_letter_space)
Q(set_text_line_space)
Q(set_text_decor)
Q(set_text_align)
Q(set_img_opa)
Q(set_img_recolor)
Q(set_img_recolor_opa)
Q(set_outline_width)
Q(set_outline_color)
Q(set_outline_opa)
Q(set_outline_pad)
Q(set_shadow_width)
Q(set_shadow_ofs_x)
Q(set_shadow_ofs_y)
Q(set_shadow_spread)
Q(set_shadow_color)
Q(set_shadow_opa)
Q(set_line_width)
Q(set_line_dash_width)
Q(set_line_dash_gap)
Q(set_line_rounded)
Q(set_line_color)
Q(set_line_opa)
Q(set_arc_width)
Q(set_arc_rounded)
Q(set_arc_color)
Q(set_arc_opa)
Q(set_flex_flow)
Q(set_flex_main_place)
Q(set_flex_cross_place)
Q(set_flex_track_place)
Q(set_flex_grow)
Q(set_grid_row_dsc_array)
Q(set_grid_column_dsc_array)
Q(set_grid_row_align)
Q(set_grid_column_align)
Q(set_grid_cell_column_pos)
Q(set_grid_cell_column_span)
Q(set_grid_cell_row_pos)
Q(set_grid_cell_row_span)
Q(set_grid_cell_x_align)
Q(set_grid_cell_y_align)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(init)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(init)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(init)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(init)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(init)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(dsc_init)
Q(check_type)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(create_obj)
Q(event_base)
Q(point)
Q(res)
Q(point)
Q(res)
Q(lv_hit_test_info_t)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(get_target)
Q(get_current_target)
Q(get_code)
Q(get_param)
Q(get_user_data)
Q(get_indev)
Q(get_draw_part_dsc)
Q(get_old_size)
Q(get_key)
Q(get_scroll_anim)
Q(set_ext_draw_size)
Q(get_self_size_info)
Q(get_hit_test_info)
Q(get_cover_area)
Q(set_cover_res)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(buf_get_px_color)
Q(buf_get_px_alpha)
Q(buf_set_px_color)
Q(buf_set_px_alpha)
Q(buf_set_palette)
Q(buf_free)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(open)
Q(read_line)
Q(close)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(delete)
Q(set_info_cb)
Q(set_open_cb)
Q(set_read_line_cb)
Q(set_close_cb)
Q(built_in_info)
Q(built_in_open)
Q(built_in_read_line)
Q(built_in_close)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(set_text)
Q(set_text_static)
Q(__SIZE__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(monitor)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(init)
Q(register)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(drv_update)
Q(get_next)
Q(enable)
Q(get_type)
Q(reset)
Q(reset_long_press)
Q(set_cursor)
Q(set_group)
Q(set_button_points)
Q(get_point)
Q(get_gesture_dir)
Q(get_key)
Q(get_scroll_dir)
Q(get_scroll_obj)
Q(get_vect)
Q(wait_release)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(lv_group_t_focus_cb)
Q(lv_group_t_focus_cb)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(_del)
Q(set_default)
Q(add_obj)
Q(remove_all_objs)
Q(focus_next)
Q(focus_prev)
Q(focus_freeze)
Q(send_data)
Q(set_focus_cb)
Q(set_refocus_policy)
Q(set_editing)
Q(set_wrap)
Q(get_focused)
Q(get_focus_cb)
Q(get_editing)
Q(get_wrap)
Q(get_obj_count)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(init)
Q(register)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(open)
Q(close)
Q(read)
Q(write)
Q(seek)
Q(tell)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(open)
Q(read)
Q(close)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(points_init)
Q(angle_init)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(init)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(init)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(init)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(init)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(__name__)
Q(__LVGL__)
Q(lv_log_print_g_cb_t_print_cb)
Q(lv_log_print_g_cb_t_print_cb)
Q(lv_timer_create_timer_xcb)
Q(lv_timer_create_timer_xcb)
Q(lv_async_call_async_xcb)
Q(lv_async_call_async_xcb)
Q(lv_layout_register_cb)
Q(lv_layout_register_cb)
Q(line_start)
Q(y)
Q(coord_y)
Q(line_start)
Q(y)
Q(coord_y)
Q(lv_draw_label_hint_t)
Q(__SIZE__)
Q(__cast__)
Q(__cast_instance__)
Q(__dereference__)
Q(lv_color_filter_dsc_t_filter_cb)
Q(lv_anim_t_ready_cb)
Q(lv_style_transition_dsc_t_path_xcb)
Q(lv_font_t_get_glyph_dsc)
Q(lv_font_t_get_glyph_bitmap)
Q(lv_disp_drv_t_rounder_cb)
Q(lv_disp_drv_t_set_px_cb)
Q(lv_disp_drv_t_monitor_cb)
Q(lv_disp_drv_t_wait_cb)
Q(lv_disp_drv_t_clean_dcache_cb)
Q(lv_disp_drv_t_gpu_wait_cb)
Q(lv_disp_drv_t_drv_update_cb)
Q(lv_disp_drv_t_gpu_fill_cb)
Q(lv_obj_class_t_constructor_cb)
Q(lv_obj_class_t_destructor_cb)
Q(lv_obj_class_t_event_cb)
Q(lv_indev_drv_t_read_cb)
Q(lv_indev_drv_t_feedback_cb)
Q(lv_fs_drv_t_ready_cb)
Q(lv_fs_drv_t_open_cb)
Q(lv_fs_drv_t_close_cb)
Q(lv_fs_drv_t_read_cb)
Q(lv_fs_drv_t_write_cb)
Q(lv_fs_drv_t_seek_cb)
Q(lv_fs_drv_t_tell_cb)
Q(lv_fs_drv_t_dir_open_cb)
Q(lv_fs_drv_t_dir_read_cb)
Q(lv_fs_drv_t_dir_close_cb)
Q(__name__)
Q(lvgl)
Q(__qpy_module_deinit__)
Q(obj)
Q(arc)
Q(btn)
Q(img)
Q(label)
Q(line)
Q(table)
Q(checkbox)
Q(bar)
Q(slider)
Q(btnmatrix)
Q(dropdown)
Q(roller)
Q(textarea)
Q(canvas)
Q(switch)
Q(animimg)
Q(calendar)
Q(calendar_header_arrow)
Q(calendar_header_dropdown)
Q(chart)
Q(keyboard)
Q(list)
Q(msgbox)
Q(meter)
Q(spinbox)
Q(spinner)
Q(tabview)
Q(tileview)
Q(win)
Q(ime_pinyin)
Q(colorwheel)
Q(led)
Q(imgbtn)
Q(spangroup)
Q(memcpy_small)
Q(trigo_cos)
Q(color_mix_premult)
Q(color_make)
Q(color_hex)
Q(color_hex3)
Q(color_chroma_key)
Q(color_white)
Q(color_black)
Q(pct)
Q(font_default)
Q(scr_act)
Q(layer_top)
Q(layer_sys)
Q(scr_load)
Q(dpx)
Q(task_handler)
Q(grid_fr)
Q(version_major)
Q(version_minor)
Q(version_patch)
Q(version_info)
Q(log_register_print_cb)
Q(log)
Q(mem_init)
Q(mem_deinit)
Q(mem_alloc)
Q(mem_free)
Q(mem_realloc)
Q(mem_test)
Q(mem_buf_get)
Q(mem_buf_release)
Q(mem_buf_free_all)
Q(memcpy)
Q(memset)
Q(memset_00)
Q(memset_ff)
Q(timer_handler)
Q(timer_create_basic)
Q(timer_create)
Q(timer_enable)
Q(timer_get_idle)
Q(trigo_sin)
Q(bezier3)
Q(atan2)
Q(sqrt)
Q(pow)
Q(map)
Q(rand)
Q(async_call)
Q(anim_del)
Q(anim_del_all)
Q(anim_get)
Q(anim_count_running)
Q(anim_speed_to_time)
Q(anim_refr_now)
Q(anim_timeline_create)
Q(anim_timeline_del)
Q(anim_timeline_add)
Q(anim_timeline_start)
Q(anim_timeline_set_reverse)
Q(anim_timeline_set_progress)
Q(anim_timeline_get_playtime)
Q(anim_timeline_get_reverse)
Q(color_hsv_to_rgb)
Q(color_rgb_to_hsv)
Q(palette_main)
Q(palette_lighten)
Q(palette_darken)
Q(disp_get_default)
Q(autoSleep)
Q(tick_inc)
Q(tick_get)
Q(tick_elaps)
Q(txt_get_size)
Q(txt_get_width)
Q(style_register_prop)
Q(style_prop_get_default)
Q(layout_register)
Q(clamp_width)
Q(clamp_height)
Q(fs_get_drv)
Q(fs_is_ready)
Q(fs_get_letters)
Q(fs_get_ext)
Q(fs_up)
Q(fs_get_last)
Q(draw_mask_add)
Q(draw_mask_apply)
Q(draw_mask_remove_id)
Q(draw_mask_remove_custom)
Q(draw_mask_get_cnt)
Q(draw_rect)
Q(draw_label)
Q(draw_letter)
Q(draw_img)
Q(draw_line)
Q(draw_triangle)
Q(draw_polygon)
Q(draw_arc)
Q(draw_arc_get_area)
Q(event_send)
Q(event_register_id)
Q(group_create)
Q(group_get_default)
Q(group_remove_obj)
Q(group_focus_obj)
Q(init)
Q(png_init)
Q(sjpeg_init)
Q(indev_read_timer_cb)
Q(indev_get_act)
Q(indev_get_obj_act)
Q(indev_get_read_timer)
Q(indev_search_obj)
Q(refr_now)
Q(theme_get_from_obj)
Q(theme_apply)
Q(theme_get_font_small)
Q(theme_get_font_normal)
Q(theme_get_font_large)
Q(theme_get_color_primary)
Q(theme_get_color_secondary)
Q(disp_load_scr)
Q(scr_load_anim)
Q(font_load)
Q(extra_init)
Q(flex_init)
Q(grid_init)
Q(theme_default_init)
Q(theme_default_is_inited)
Q(theme_mono_init)
Q(theme_basic_init)
Q(snapshot_take)
Q(snapshot_free)
Q(snapshot_buf_size_needed)
Q(snapshot_take_to_buf)
Q(DPI)
Q(LOG_LEVEL)
Q(RES)
Q(ANIM_REPEAT)
Q(OPA)
Q(COORD)
Q(ALIGN)
Q(DIR)
Q(SIZE)
Q(FONT_SUBPX)
Q(TEXT_FLAG)
Q(TEXT_CMD_STATE)
Q(TEXT_ALIGN)
Q(BASE_DIR)
Q(IMG_ZOOM)
Q(BLEND_MODE)
Q(TEXT_DECOR)
Q(BORDER_SIDE)
Q(GRAD_DIR)
Q(STATE)
Q(PART)
Q(SCROLLBAR_MODE)
Q(SCROLL_SNAP)
Q(FS_RES)
Q(FS_MODE)
Q(DRAW_MASK_RES)
Q(DRAW_MASK_TYPE)
Q(DRAW_MASK_LINE_SIDE)
Q(RADIUS)
Q(KEY)
Q(FONT_FMT_TXT_CMAP)
Q(LABEL_DOT)
Q(LABEL_POS)
Q(LABEL_TEXT_SELECTION)
Q(TABLE_CELL)
Q(BTNMATRIX_BTN)
Q(DROPDOWN_POS)
Q(TEXTAREA_CURSOR)
Q(PART_TEXTAREA)
Q(ANIM_IMG_PART)
Q(CHART_POINT)
Q(OBJ_FLAG_FLEX_IN_NEW)
Q(SPAN_OVERFLOW)
Q(SPAN_MODE)
Q(GRID)
Q(GRID_TEMPLATE)
Q(ANIM)
Q(PALETTE)
Q(DISP_ROT)
Q(INDEV_TYPE)
Q(INDEV_STATE)
Q(STYLE)
Q(FS_SEEK)
Q(COVER_RES)
Q(EVENT)
Q(GROUP_REFOCUS_POLICY)
Q(SCR_LOAD_ANIM)
Q(FONT_FMT_TXT)
Q(FLEX_ALIGN)
Q(FLEX_FLOW)
Q(GRID_ALIGN)
Q(SYMBOL)
Q(C_Pointer)
Q(color_filter_dsc_t)
Q(color32_t)
Q(color32_ch_t)
Q(style_transition_dsc_t)
Q(anim_t)
Q(font_t)
Q(font_glyph_dsc_t)
Q(disp_t)
Q(disp_drv_t)
Q(disp_draw_buf_t)
Q(area_t)
Q(timer_t)
Q(theme_t)
Q(point_t)
Q(style_t)
Q(style_v_p_t)
Q(style_value_t)
Q(style_const_prop_t)
Q(draw_rect_dsc_t)
Q(draw_label_dsc_t)
Q(draw_img_dsc_t)
Q(draw_line_dsc_t)
Q(draw_arc_dsc_t)
Q(obj_draw_part_dsc_t)
Q(obj_class_t)
Q(event_t)
Q(img_dsc_t)
Q(img_header_t)
Q(img_decoder_dsc_t)
Q(img_decoder_t)
Q(calendar_date_t)
Q(chart_series_t)
Q(chart_cursor_t)
Q(meter_scale_t)
Q(meter_indicator_t)
Q(meter_indicator_type_data_t)
Q(meter_indicator_type_data_needle_img_t)
Q(meter_indicator_type_data_needle_line_t)
Q(meter_indicator_type_data_arc_t)
Q(meter_indicator_type_data_scale_lines_t)
Q(color_hsv_t)
Q(span_t)
Q(pinyin_dict_t)
Q(_lv_mp_int_wrapper)
Q(mem_monitor_t)
Q(indev_drv_t)
Q(indev_data_t)
Q(indev_t)
Q(_lv_indev_proc_t)
Q(_lv_indev_proc_types_t)
Q(_lv_indev_proc_types_pointer_t)
Q(_lv_indev_proc_types_keypad_t)
Q(group_t)
Q(ll_t)
Q(fs_drv_t)
Q(fs_file_t)
Q(fs_dir_t)
Q(draw_mask_line_param_t)
Q(_lv_draw_mask_common_dsc_t)
Q(draw_mask_line_param_cfg_t)
Q(draw_mask_angle_param_t)
Q(draw_mask_angle_param_cfg_t)
Q(draw_mask_radius_param_t)
Q(draw_mask_radius_param_cfg_t)
Q(sqrt_res_t)
Q(draw_mask_fade_param_t)
Q(draw_mask_fade_param_cfg_t)
Q(draw_mask_map_param_t)
Q(draw_mask_map_param_cfg_t)
Q(hit_test_info_t)
Q(draw_label_hint_t)
Q(color_t)
Q(font_montserrat_14)
Q(font_06_songti)
Q(font_09_songti)
Q(font_18_songti)
Q(font_20_songti)
Q(obj_class)
Q(arc_class)
Q(btn_class)
Q(img_class)
Q(label_class)
Q(line_class)
Q(table_class)
Q(checkbox_class)
Q(bar_class)
Q(slider_class)
Q(btnmatrix_class)
Q(dropdown_class)
Q(dropdownlist_class)
Q(roller_class)
Q(textarea_class)
Q(canvas_class)
Q(switch_class)
Q(animimg_class)
Q(calendar_class)
Q(calendar_header_arrow_class)
Q(calendar_header_dropdown_class)
Q(chart_class)
Q(keyboard_class)
Q(LAYOUT_FLEX)
Q(STYLE_FLEX_FLOW)
Q(STYLE_FLEX_MAIN_PLACE)
Q(STYLE_FLEX_CROSS_PLACE)
Q(STYLE_FLEX_TRACK_PLACE)
Q(STYLE_FLEX_GROW)
Q(list_class)
Q(list_text_class)
Q(list_btn_class)
Q(msgbox_class)
Q(meter_class)
Q(spinbox_class)
Q(spinner_class)
Q(tabview_class)
Q(tileview_class)
Q(tileview_tile_class)
Q(win_class)
Q(ime_pinyin_class)
Q(colorwheel_class)
Q(led_class)
Q(imgbtn_class)
Q(spangroup_class)
Q(LAYOUT_GRID)
Q(STYLE_GRID_COLUMN_DSC_ARRAY)
Q(STYLE_GRID_COLUMN_ALIGN)
Q(STYLE_GRID_ROW_DSC_ARRAY)
Q(STYLE_GRID_ROW_ALIGN)
Q(STYLE_GRID_CELL_COLUMN_POS)
Q(STYLE_GRID_CELL_COLUMN_SPAN)
Q(STYLE_GRID_CELL_X_ALIGN)
Q(STYLE_GRID_CELL_ROW_POS)
Q(STYLE_GRID_CELL_ROW_SPAN)
Q(STYLE_GRID_CELL_Y_ALIGN)
