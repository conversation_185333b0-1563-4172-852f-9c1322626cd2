# from usr.microVoIP.VoIP import VoIPPhone, InvalidStateError, VoIPEvent
import EventMesh,ui
from common import Abstract
import osTimer,_thread,dataCall,utime
import usocket
import sip
import gc
import _thread

class EachSipServer:
    """SIP服务器配置信息"""

    HL = {
        "server": "*************",
        "port": "5068",
        "user": "2210",
        "password": "GDhlkj123",
        "domain": "7258801.dyqx.com",
        "displayName": None,
    }  # 宏联 号码前加拨0

    TY = {
        "server": "************",
        "port": "7066",
        "user": "9997",
        "password": "2302",
        "domain": "hexiaotong.cn",
        "displayName": None,
    }  # 泰岳  A0`号码`*053901487*123456

    TYS = {
        "server": "*************",
        "port": "2080",
        "user": "10004",
        "password": "2302",
        "domain": None,
        "displayName": None,
    }  # 泰岳

    YM = {
        "server": "*************",
        "port": "5068",
        "user": "2210",
        "password": "GDhlkj123",
        "domain": "7258801.dyqx.com",
        "displayName": None,
    }  # 橡木

    HLS = {
        "server": "************",
        "port": "5068",
        "user": "9398",
        "password": "Qyzh2203",
        "domain": "880001.dyqx.com",
        "displayName": None,
    }  # 宏联

    ST = {
        "server": "14.29.16.98",
        "port": "6068",
        "user": "21196218",
        "password": "5vtv5rig",
        "domain": None,
        "displayName": None,
    }  # 盛通

    XK = {
        "server": "121.42.46.92",
        "port": "65510",
        "user": "10372001753",
        "password": "!dfDgF4535",
        "domain": None,
        "displayName": None,
    }  # 鑫考

    HT = {
        "server": "124.128.53.59",
        "port": "2060",
        "user": "053285872186",
        "password": "101102",
        "domain": None,
        "displayName": None,
    }  # 海腾

    YS = {
        "server": "124.128.53.59",
        "port": "2060",
        "user": "05363287964",
        "password": "101102",
        "domain": None,
        "displayName": None,
    }  # 寅时 号码前加拨0

    YS1 = {
        "server": "124.128.53.53",
        "port": "5060",
        "user": "053187946063",
        "password": "101102",
        "domain": None,
        "displayName": None,
    }  # 寅时 号码前加拨0

    YNIMS = {
        "server": "183.224.72.112",
        "port": "5060",
        "user": "8687164878703",
        "password": "JKSRvudp8884920",
        "domain": "ims.yn.chinamobile.com",
        "displayName": None,
    }  # 云南IMS 

    JLIMS = {
        "server": "************",
        "port": "5060",
        "user": "+8643181790410",
        "password": "NJ6hJ2xRzSJ3srr",
        "domain": "ims.jl.chinamobile.com",
        "displayName": None,
    }  # 吉林IMS 

    CCIMS = {
        "server": "************",
        "port": "5060",
        "user": "+8643181080888",
        "password": "8xjyY3bPpjh4tdj",
        "domain": "ims.jl.chinamobile.com",
        "displayName": None,
    }  # 长春IMS 

    JNHK = {
        "server": "**************",
        "port": "7060",
        "user": "40003",
        "password": "KaHk40003",
        "domain": None,
        "displayName": None,
    }  # 长春IMS 

class SipCallManager(Abstract):

    def __init__(
        self, server=None, port=None, user=None, password=None, domain=None, displayName=None,callback_fun=None
    ):
        if callback_fun != None:
            print("callback_fun init")
            self.voip_event = callback_fun
        self.__call_state = "0"
        self.__call_time_cnt = 0
        self.__timer_run_flag = 0
        self.__on_call_time = "00:00:00"
        self.__on_call_timer = osTimer()
        self.__on_call_phone = None
        # self.aud = audio.Audio(0)
        # self.volum = audio.Audio.PCM(1, 1, 8000, 2, 1)
        self.__local_ip =""
        self.call = None
        self.mysip = None
        self.delay_start_timer = osTimer()
        self.sip_server = None
        self.__sip_watch_thread = None
        self.__sip_event_thread = None
        self.sip_registered = 0
        self.sip_call_state = 0 #0-挂机 1-来电 2- 通话中  3-呼出中
        self.dialing = 0
        self.cancel_sta=0
        self.phoneNum = ''
        # self.SIP_NET_registere()

    def post_processor_after_instantiation(self):

        EventMesh.subscribe("SIP_NET_registere", self.SIP_NET_registere)#注册SIP服务器
        EventMesh.subscribe("call_start_voip", self.call_start_voip)#呼出
        EventMesh.subscribe("call_answer_voip", self.call_answer_voip)#接听
        EventMesh.subscribe("call_end_voip", self.call_end_voip)#通话中挂断
        EventMesh.subscribe("call_failed_voip", self.call_failed_voip)#通话中挂断
        EventMesh.subscribe("call_bye_voip", self.call_bye_voip)#通话中挂断
        EventMesh.subscribe("call_cancel_voip", self.call_cancel_voip)#取消打电话
        EventMesh.subscribe("call_mic_mute_voip", self.call_mic_mute_voip)
        EventMesh.subscribe("call_send_dtmf_voip", self.call_send_dtmf_voip)
        # EventMesh.subscribe("pcm_play_voip", self.pcm_play_voip)
        EventMesh.subscribe("oncall_timer_start_voip", self.oncall_timer_start)
        EventMesh.subscribe("oncall_timer_stop_voip", self.oncall_timer_stop)
        EventMesh.subscribe("get_SIP_call_state", self.get_SIP_call_state)#获取SIP电话状态 0 挂断状态 1 来电中，2 通话中 3 呼出中   
        EventMesh.subscribe("set_SIP_call_state", self.set_SIP_call_state)#设置SIP电话状态 0 挂断状态 1 来电中，2 通话中 3 呼出中   
        EventMesh.subscribe("get_sip_registered", self.get_sip_registered)
        EventMesh.subscribe("set_sip_registered", self.set_sip_registered)
        EventMesh.subscribe("Stop_SIP_NET_registere", self.Stop_SIP_NET_registere)#停止注册服务器线程
        print("SipCallManager.post_processor_after_instantiation")
        EventMesh.subscribe("get_cancel_sta", self.get_cancel_sta)
        EventMesh.subscribe("set_cancel_sta", self.set_cancel_sta)
        EventMesh.subscribe("after_handle", self.after_handle)
        # _thread.stack_size(11000)
        # self.__sip_watch_thread = _thread.start_new_thread(self.sip_watch_thread,())
        # _thread.stack_size(8448)
        # print("激活流量")
        # dataCall.activate(1)
        self.delay_start_timer.start(10000, 0, self.after_handle)#5s后处理
    def get_cancel_sta(self, topic=None, msg=None):
        return self.cancel_sta
    def set_cancel_sta(self, topic=None, msg=None):
        self.cancel_sta=0
    def after_handle(self,msg):
        print('SIP!!!!!!!!!!!!!!!!!!!!!!!')
        if self.__sip_watch_thread == None and  self._should_register_sip():
            print("准备连接SIP")
            self.SIP_NET_registere()
        
    def gc_free(self):
        while True:
            print('gc free: {}, heap free: {}'.format(gc.mem_free(), _thread.get_heap_size()))
            utime.sleep(1)
    #SIP注册      
    def SIP_NET_registere(self, topic=None, msg=None):
        if self.__sip_watch_thread:
            print("停止注册线程")
            _thread.stop_thread(self.__sip_watch_thread)
        self.__sip_watch_thread = None
        print("准备连接SIP")
        _thread.stack_size(32*1024)
        self.__sip_watch_thread = _thread.start_new_thread(self.sip_watch_thread,())
        _thread.stack_size(8*1024)

    def Stop_SIP_NET_registere(self, topic=None, msg=None): 
        if self.__sip_watch_thread!=None:
            print("停止注册线程")
            try:
                _thread.stop_thread(self.__sip_watch_thread)
            except Exception as e:
                print("线程已停止:",e)
            self.__sip_watch_thread=None  
            if self.mysip!=None:  
                try:
                    self.mysip.release()
                except Exception as e:
                    print("停止出错",e)
                    self.sip_call_state=0
                self.mysip=None
                return 1
        else:
            return 0

    def get_SIP_call_state(self, topic=None, msg=None):
        print("display self.sip_call_state==", self.sip_call_state)
        return self.sip_call_state
    
    def set_SIP_call_state(self, topic=None, msg=None):
        print("SipCallManager.set_SIP_call_state:",msg)
        self.sip_call_state = msg
    
    def get_sip_registered(self, topic=None, msg=None):#获取注册状态
        return self.sip_registered
    
    def set_sip_registered(self, topic=None, msg=None):#获取注册状态
        self.sip_registered = -1
    
    def call_start_voip(self, topic=None, msg=None):#呼出
        print("SipCallManager.call_start_voip")
        print("DEBUG: 开始呼叫流程")
        print("DEBUG: 目标号码:", msg)
        print("DEBUG: 当前SIP注册状态:", self.sip_registered)
        print("DEBUG: 当前呼叫状态:", self.sip_call_state)

        # 检查SIP注册状态
        if self.sip_registered != 1:
            print("ERROR: SIP未注册，无法呼出。注册状态:", self.sip_registered)
            return -1

        # 检查当前呼叫状态
        if self.sip_call_state != 0:
            print("ERROR: 当前非空闲状态，无法呼出。呼叫状态:", self.sip_call_state)
            return -1

        # 检查mysip对象
        if self.mysip is None:
            print("ERROR: SIP对象未初始化")
            return -1

        # 检查是否是重复呼叫同一号码
        target_number = msg[0] if isinstance(msg, list) and len(msg) > 0 else str(msg)
        if hasattr(self, 'last_call_number') and self.last_call_number == target_number:
            import utime
            current_time = utime.ticks_ms()
            if hasattr(self, 'last_call_end_time'):
                time_diff = utime.ticks_diff(current_time, self.last_call_end_time)
                if time_diff < 2000:  # 2秒内的重复呼叫
                    print("DEBUG: 检测到2秒内重复呼叫同一号码，添加延迟")
                    remaining_delay = 2000 - time_diff
                    print("DEBUG: 等待 {}ms 后再呼叫".format(remaining_delay))
                    utime.sleep_ms(remaining_delay)

        # 记录本次呼叫信息
        self.last_call_number = target_number

        print("DEBUG: 所有检查通过，开始呼叫线程")
        _thread.stack_size(32*1024)
        self.dialing = 0
        _thread.start_new_thread(self.event_call_out_thread,(msg,))
        _thread.stack_size(8*1024)
        return 0
   
        
    def call_answer_voip(self, topic=None, phonenum=None):#接听
        print("SipCallManager.call_answer_voip")
        self.mysip.answer()
        # self.call.startAudioService()
        
        EventMesh.publish("set_call_state","7")
        self.sip_call_state=2

        phoneNum = self.mysip.get_answer_number()
        EventMesh.publish("oncall_timer_start_voip",0)
        phone_name = EventMesh.publish("get_phone_number_name", phoneNum)
        self.info ={"name":phone_name,"number":phoneNum,"flag":"0"}
        EventMesh.publish("load_screen", {"screen": "on_call", "meta_info": self.info})

    def call_end_voip(self, topic=None, phonenum=None):#主动通话中挂断
        print("[SIP Interfface]call_end_voip")
        print("DEBUG: 主动挂断呼叫，当前状态:", self.sip_call_state)
        try:
            if self.mysip is not None:
                self.mysip.hangup()
                print("DEBUG: SIP hangup调用成功")
        except Exception as e :
            print("挂断异常",e)

        # 完整的呼叫清理
        self._complete_call_cleanup()
        print("SipCallManager.call_end_voip 完成")
        # EventMesh.publish("caller_hanging_up_toarm")

    def call_failed_voip(self, topic=None, phonenum=None):#呼叫失败处理
        print("[SIP Interfface]call_failed_voip")
        print("DEBUG: 呼叫失败，当前状态:", self.sip_call_state)

        # 完整的呼叫清理
        self._complete_call_cleanup()
        print("SipCallManager.call_failed_voip 完成")
        # EventMesh.publish("caller_hanging_up_toarm")

    def call_bye_voip(self, topic=None, phonenum=None):#未接挂断
        print("[SIP Interfface]call_bye_voip")
        print("DEBUG: 拒接呼叫，当前状态:", self.sip_call_state)
        try:
            print("decline\n")
            if self.mysip is not None:
                self.mysip.reject()
                print("DEBUG: SIP reject调用成功")
        except Exception as e :
            try:
                if self.mysip is not None:
                    self.mysip.hangup()
                    print("DEBUG: 备用hangup调用成功")
            except:
                pass
            print("挂断异常",e)

        # 完整的呼叫清理
        self._complete_call_cleanup()
        print("SipCallManager.call_bye_voip 完成")
        
    def call_cancel_voip(self, topic=None, phonenum=None):#取消
        print("取消打电话")
        self.cancel_sta=1
        self.event_call_cancel_thread()
        
    def event_call_cancel_thread(self):
        if self.sip_call_state==3:
            self.sip_call_state=0
            EventMesh.publish("set_call_state","0")
            print("SipCallManager.call_cancel_voip")
            EventMesh.publish("stop_ring_back_tone")
            EventMesh.publish("audio_disable")
            _thread.start_new_thread(self.cancel_thread,())
            # self.mysip.cancel()
            # print("取消成功")
            # EventMesh.publish("caller_hanging_up_toarm")
    def cancel_thread(self):
        sleep_time_count=0
        while True:
            if self.mysip.get_call_status()==3 and self.dialing == 1:
                self.dialing = 0
                self.mysip.cancel()
                self.cancel_sta=0
                print("取消成功")
                break
            else:
                if sleep_time_count >=100:
                    self.dialing = 0
                    self.mysip.cancel()
                    self.cancel_sta=0
                    print("强制取消呼叫")
                    break
                sleep_time_count = sleep_time_count+1
                print("1")
                utime.sleep_ms(50)


    def call_mic_mute_voip(self, topic=None, mute=None):
        if self.mysip != None:
            self.mysip.mic_mute(mute)
        print("SipCallManager.call_mic_mute_voip")

    def call_send_dtmf_voip(self, topic=None, phonenum=None):
        print("SipCallManager.call_send_dtmf_voip phonenum:",phonenum)
        if self.mysip != None:
            self.mysip.send_dtmf(phonenum)
            
    # def pcm_play_voip(self, topic=None, data=None):
    #     print("SipCallManager.pcm_play_voip data:",data)
    #     if self.mysip != None:
    #         self.mysip.set_pcm_play(data)

    def voip_event(self,args):
        print("voip_event",type(args))
        event = args
        print('----------call.state = {}--------'.format(event))
        print("DEBUG: 收到VOIP事件:", event)
        print("DEBUG: 当前呼叫状态:", self.sip_call_state)

        if event == self.mysip.VOIP_EVENT_REGISTERED:
            self.sip_registered = 1
            EventMesh.publish("update_sip_img",1)
            operator = "SIP Registered"
            EventMesh.publish("display_sip_operator_info",operator)
            print('::::::VOIP_EVENT_REGISTERED entry')

            # 检查是否在呼叫过程中收到注册事件
            if self.sip_call_state == 3:
                print("WARNING: 在呼叫过程中收到REGISTERED事件")
                print("DEBUG: 这可能表明呼叫请求没有被正确处理")
                print("DEBUG: 建议检查SIP服务器配置和网络连接")
                # 启动呼叫超时检查
                import _thread
                _thread.start_new_thread(self.call_timeout_check, ())
            
        elif event == self.mysip.VOIP_EVENT_ERROR:
            EventMesh.publish("update_sip_img",0)
            self.sip_registered = 0
            print('::::::VOIP_EVENT_ERROR 重新注册')
            print("DEBUG: SIP发生错误，开始重新注册")
            self.SIP_NET_registere()

        elif event == self.mysip.VOIP_EVENT_DIALING:
            print('::::::VOIP_EVENT_DIALING 呼出开始响铃')
            print("DEBUG: 呼叫开始拨号，设置dialing=1")
            self.dialing = 1

        elif event == self.mysip.VOIP_EVENT_RINGING:         #来电响铃
            print('::::::VOIP_EVENT_RINGING entry self.sip_call_state:',self.sip_call_state)
            
            phone_num = self.mysip.get_answer_number()
            phone_name = EventMesh.publish("get_phone_number_name", phone_num)
            white_ret = EventMesh.publish("whitelist_handle",phone_num)
            black_ret = EventMesh.publish("blacklist_handle",phone_num)
            if white_ret == 1 or black_ret == 1:
                self.mysip.hangup()
                print("黑白名单挂断")
                return 
            
            EventMesh.publish("wake_up")
            # self.call.startAudioService()
            # EventMesh.publish("incoming_call_to_arm")
            self.sip_call_state=1
            call_state = EventMesh.publish("get_call_state")#获取当前通话状态
            print("VOIP_EVENT_RINGING call_state=", call_state)
            if call_state=="0":
                # 挂机状态下来电
                phone = self.mysip.get_answer_number()
                print("VOIP_EVENT_RINGING phone=", phone)
                EventMesh.publish("set_call_state","3")
                EventMesh.publish("load_screen", {"screen": "call_up", "meta_info": [phone_name, phone]}) 
                EventMesh.publish("SIP_ringtone")#电话响铃
            elif call_state!="3":#挂机
                EventMesh.publish("call_end_voip")#挂断电话 
                
        elif event == self.mysip.VOIP_EVENT_CANCELED:            #取消
            print('::::::VOIP_EVENT_CANCELED entry self.sip_call_state:',self.sip_call_state)
            if self.sip_call_state != 3:
                _thread.stack_size(20*1024)
                _thread.start_new_thread(self.event_cancel_thread,())
                _thread.stack_size(8*1024)

            # self.sip_call_state=0
            # EventMesh.publish("set_call_state","0")
            # EventMesh.publish("ignore_comingcall")#关闭铃声
            # print("VOIP_EVENT_CANCELED")
            # EventMesh.publish("load_screen", {"screen": "main"})
            # call.stopAudioService()
            
        elif event == self.mysip.VOIP_EVENT_BYE:          #挂断
            print('::::::VOIP_EVENT_BYE entry 10K self.sip_call_state:',self.sip_call_state)
            _thread.stack_size(20*1024)
            _thread.start_new_thread(self.event_handle_thread,())
            _thread.stack_size(8*1024)

        elif event == self.mysip.VOIP_EVENT_ENDED:
            print('::::::VOIP_EVENT_ENDED entry self.sip_call_state:',self.sip_call_state)
            print("DEBUG: 呼叫会话结束，开始完整清理")
            self._complete_call_cleanup()
            # call.stopAudioService()
            
        elif event == self.mysip.VOIP_EVENT_ANSWERED:#对方电话接听
            print('::::::VOIP_EVENT_ANSWERED entry'," self.sip_call_state:",self.sip_call_state)
            # self.call.startAudioService()
            print("self.sip_call_state:",self.sip_call_state)
            if self.sip_call_state != 2:  #挂机 来电或者呼出中
                #话单记录
                call_state = EventMesh.publish("get_call_state")#获取当前通话状态
                EventMesh.publish("set_call_state","2")
                self.sip_call_state=2

                phoneNum = self.mysip.get_call_number()
                EventMesh.publish("oncall_timer_start",0)
                phone_name = EventMesh.publish("get_phone_number_name", phoneNum)
                self.info ={"name":phone_name,"number":phoneNum,"flag":"0"}
                EventMesh.publish("load_screen", {"screen": "on_call", "meta_info": self.info})
        elif event == self.mysip.VOIP_EVENT_FAILED:
            print('::::::VOIP_EVENT_FAILED 呼叫失败')
            print("DEBUG: 呼叫失败，当前状态:", self.sip_call_state)
            self.sip_call_state=0
            EventMesh.publish("set_call_state","0")
            EventMesh.publish("stop_ring_back_tone")
            EventMesh.publish("audio_disable")
            print("ERROR: 呼叫失败，请检查网络和SIP配置")
            EventMesh.publish("load_screen", {"screen": "main"})



        elif event == self.mysip.VOIP_EVENT_DEREGISTERED:
            print('::::::VOIP_EVENT_DEREGISTERED SIP注销')
            print("DEBUG: SIP注销，当前状态:", self.sip_call_state)
            self.sip_registered = 0
            EventMesh.publish("update_sip_img",0)
            operator = "SIP Not Registered"
            EventMesh.publish("display_sip_operator_info",operator)

        else:
            print('::::::else entry - 未知事件:', event)
            print("DEBUG: 收到未处理的VOIP事件:", event)
            # 列出所有已知事件供参考
            print("DEBUG: 已知事件列表:")
            print("  VOIP_EVENT_REGISTERED =", self.mysip.VOIP_EVENT_REGISTERED)
            print("  VOIP_EVENT_DEREGISTERED =", self.mysip.VOIP_EVENT_DEREGISTERED)
            print("  VOIP_EVENT_DIALING =", self.mysip.VOIP_EVENT_DIALING)
            print("  VOIP_EVENT_RINGING =", self.mysip.VOIP_EVENT_RINGING)
            print("  VOIP_EVENT_CANCELED =", self.mysip.VOIP_EVENT_CANCELED)
            print("  VOIP_EVENT_ANSWERED =", self.mysip.VOIP_EVENT_ANSWERED)
            print("  VOIP_EVENT_BYE =", self.mysip.VOIP_EVENT_BYE)
            print("  VOIP_EVENT_ENDED =", self.mysip.VOIP_EVENT_ENDED)
            print("  VOIP_EVENT_ERROR =", self.mysip.VOIP_EVENT_ERROR)
            print("  VOIP_EVENT_FAILED =", self.mysip.VOIP_EVENT_FAILED)

    def event_call_out_thread(self, msg):
        print("DEBUG: 进入呼叫线程")
        print("DEBUG: 呼叫参数:", msg)
        print("DEBUG: mysip对象状态:", self.mysip)

        try:
            # 检查SIP对象状态
            if self.mysip is None:
                print("ERROR: mysip对象为空，无法呼叫")
                self.sip_call_state=4
                return -1

            # 检查SIP注册状态
            reg_status = self.mysip.get_reg_status()
            print("DEBUG: SIP注册状态检查:", reg_status)
            if reg_status != self.mysip.SIP_REGISTERED:
                print("ERROR: SIP未注册，注册状态:", reg_status)
                self.sip_call_state=4
                return -1

            # 检查网络状态
            net_status = EventMesh.publish("sim_card_net_status")
            print("DEBUG: 网络状态:", net_status)
            if net_status != 1:
                print("ERROR: 网络未连接，网络状态:", net_status)
                self.sip_call_state=4
                return -1

            print("DEBUG: 开始执行呼叫")
            self.sip_call_state=3
            call_result = self.mysip.call(msg[0])
            print("DEBUG: 呼叫API返回值:", call_result)

            self.phoneNum = msg[0]
            print("呼出中。。。。。")
            print("DEBUG: 呼叫线程执行完成")
            return 1
        except Exception as e :
            print("呼出异常详细信息:",e)
            print("DEBUG: 异常类型:", type(e))
            import sys
            print("DEBUG: 异常详细:", sys.print_exception(e))
            self.sip_call_state=4
            return -1

    def event_handle_thread(self):
        print('event_handle_thread start self.sip_call_state:',self.sip_call_state)
        print("DEBUG: BYE事件处理线程启动")
        if self.sip_call_state != 0:
            EventMesh.publish("ignore_comingcall")#关闭铃声
            if EventMesh.publish("get_current_screen_name") == "oncallselete":
                EventMesh.publish("oncallselete_return")
            EventMesh.publish("btn_pwk_click")
            print("BYE hang up")
            EventMesh.publish("hanging_up_to_arm")

            # 使用完整清理方法
            self._complete_call_cleanup()
            # print("VoIPEvent.VOIP_EVENT_BYE 1")
            # call.stopAudioService()
            # print("VoIPEvent.VOIP_EVENT_BYE 2")
        print('event_handle_thread terminate')

    def event_cancel_thread(self):
        print("DEBUG: CANCEL事件处理线程启动")
        EventMesh.publish("ignore_comingcall")#关闭铃声
        print("VOIP_EVENT_CANCELED")

        # 使用完整清理方法
        self._complete_call_cleanup()

    def sip_watch_thread(self):
        while True:
            if self._should_register_sip():
                print("获取IP信息:", self.sip_server)
                self.__local_ip = dataCall.getInfo(1, 0)[2][2]
                print("self.__local_ip:", self.__local_ip)
                print("server:", self.sip_server["server"])
                print("port:", self.sip_server["port"])
                print("user:", self.sip_server["user"])
                print("password:", self.sip_server["password"])
                print("domain:", self.sip_server["domain"])
                print("displayName:", self.sip_server["displayName"])
                self._register_sip()
            utime.sleep(1)
      
    def _should_register_sip(self):
        """判断是否应该注册SIP服务器"""
        sip_info = EventMesh.publish("persistent_config_get", "sip_info")
        if sip_info == None:
            self.sip_server = None
            print("sip_server is None")
            # self.sip_server = EachSipServer.YS1
        else:
            # 检查必要字段是否存在
            if not all([sip_info[2], sip_info[3], sip_info[0], sip_info[1]]):
                self.sip_server = None
                print("缺少必要的SIP配置参数")
            else:
                self.sip_server = {
                    "server": sip_info[2],
                    "port": sip_info[3],
                    "user": sip_info[0],
                    "password": sip_info[1],
                    "domain": sip_info[4],
                    "displayName": sip_info[5],
                }
        print(EventMesh.publish("sim_card_net_status"))
        return (
            self.sip_server
            and EventMesh.publish("sim_card_net_status") == 1
            and dataCall.getInfo(1, 0)[2][2] != "0.0.0.0"
        )
      
    def _register_sip(self):
        print("_register_sip start")
        self.__local_ip = dataCall.getInfo(1, 0)[2][2]
        try:
            operator = "Registering SIP"
            self.sip_registered = 2
            EventMesh.publish("display_sip_operator_info",operator)
            password = self.sip_server['password'].decode('utf-8')
            self.mysip = sip.init(self.sip_server["server"],int(self.sip_server["port"]),self.sip_server["user"],password,self.sip_server["domain"],self.sip_server["displayName"],self.voip_event)
            if self.mysip == None:
                print("sip init error")
                utime.sleep(30)
                operator = "SIP Not Registered"
                EventMesh.publish("display_sip_operator_info",operator)
                return
        except Exception as e:
            operator = "SIP Not Registered"
            EventMesh.publish("display_sip_operator_info",operator)
            print("sip init error->{}".format(e))
            utime.sleep(30)
            return
        i = 0
        while self.mysip.get_reg_status() != self.mysip.SIP_REGISTERED:
            i += 1
            print("waiting for register {}".format(i))
            utime.sleep(1)
            if i > 5:
                print("register timeout")
                operator = "SIP Not Registered"
                EventMesh.publish("display_sip_operator_info",operator)
                print("注册失败")
                EventMesh.publish("update_sip_img",0)
                return
        self._monitor_network_status()      
    
    def _complete_call_cleanup(self):
        """完整的呼叫清理，确保所有资源都被正确释放"""
        print("DEBUG: 开始完整呼叫清理")

        # 记录清理前的状态
        old_state = self.sip_call_state
        print("DEBUG: 清理前状态:", old_state)

        # 记录呼叫结束时间（用于重复呼叫检测）
        import utime
        self.last_call_end_time = utime.ticks_ms()
        print("DEBUG: 记录呼叫结束时间:", self.last_call_end_time)

        # 重置所有呼叫相关状态
        self.sip_call_state = 0
        self.dialing = 0
        self.cancel_sta = 0
        self.phoneNum = ""

        # 发布状态更新事件
        EventMesh.publish("set_call_state", "0")
        EventMesh.publish("stop_ring_back_tone")
        EventMesh.publish("audio_disable")

        # 返回主界面
        EventMesh.publish("load_screen", {"screen": "main"})

        # 添加短暂延迟，确保SIP底层有时间清理会话
        utime.sleep_ms(500)  # 500ms延迟

        print("DEBUG: 呼叫清理完成，状态:", self.sip_call_state)

    def call_timeout_check(self):
        """呼叫超时检查，如果10秒内没有收到DIALING事件则认为呼叫失败"""
        import utime
        print("DEBUG: 启动呼叫超时检查")

        # 等待10秒
        for i in range(100):  # 10秒，每100ms检查一次
            utime.sleep_ms(100)
            # 如果状态改变了（收到了DIALING或其他事件），退出检查
            if self.sip_call_state != 3:
                print("DEBUG: 呼叫状态已改变，退出超时检查")
                return

        # 10秒后仍然是呼出中状态，认为呼叫失败
        if self.sip_call_state == 3:
            print("ERROR: 呼叫超时！10秒内未收到DIALING事件")
            print("ERROR: 可能的原因：")
            print("  1. SIP服务器拒绝呼叫请求")
            print("  2. 网络连接不稳定")
            print("  3. 目标号码格式错误")
            print("  4. SIP账号没有呼出权限")
            print("  5. 前一次呼叫会话未完全清理")

            # 使用完整清理方法
            self._complete_call_cleanup()

    def ring_back_tone_handle(self,en):
        print("ring_back_tone_handle",en)
        if en == True:
            EventMesh.publish("play_ring_back_tone")
        else:
            EventMesh.publish("stop_ring_back_tone")

    def _monitor_network_status(self):
        self.mysip.set_ring_back_tone_cb(self.ring_back_tone_handle)
        reg_sip_info = EventMesh.publish("persistent_config_get","sip_info")
        while True:
            utime.sleep(30)
            # if EventMesh.publish("sim_card_net_status") != 1:
            #     print(EventMesh.publish("sim_card_net_status"))
            #     break
            if EventMesh.publish("persistent_config_get","sip_info") != reg_sip_info:
                break
            # if dataCall.getInfo(1, 0)[2][2] != self.__local_ip:
            #     print(dataCall.getInfo(1, 0)[2][2])
            #     break
        EventMesh.publish("update_sip_img",0)
        operator = "SIP Not Registered"
        EventMesh.publish("display_sip_operator_info",operator)
        self.sip_registered = -1
        # self._stop_phone()
        
    def _stop_phone(self):
        if self.mysip != None:
            try:
                self.mysip.release()
            except Exception as e:
                print("[{}]停止出错{}".format(self._time_now(), e))
                self.sip_call_state = 0
        self.mysip = None
          
    def _time_now(self):
        return EventMesh.publish("screen_get_time_2")

    def oncall_timer_start(self, topic=None, data=None):
        EventMesh.publish("set_call_type",1)
        if self.__timer_run_flag == 0:
            self.__on_call_timer.start(1000, 1, self.call_timer_task)
        self.__call_time_cnt = 0
        self.__timer_run_flag =1

    def oncall_timer_stop(self, topic=None, data=None):
        self.__call_time_cnt = 0
        self.__timer_run_flag =0
        self.__on_call_time = "00:00:00"
        if self.__timer_run_flag == 0:
            # EventMesh.publish("audio_disable")
            # EventMesh.publish("set_speaker_state_open")
            self.__on_call_timer.stop()

    def call_timer_task(self, args):
        if self.__timer_run_flag == 1:
            self.__call_time_cnt += 1 
            self.__on_call_time = "{0:02d}:{1:02d}:{2:02d}".format(int(self.__call_time_cnt/3600), int(self.__call_time_cnt//60%60), int(self.__call_time_cnt%60))
            EventMesh.publish("oncall_update_time", self.__on_call_time)

