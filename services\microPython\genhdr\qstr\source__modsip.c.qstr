Q(__del__)
Q(release)
Q(get_reg_status)
Q(call)
Q(answer)
Q(hangup)
Q(cancel)
Q(reject)
Q(mic_mute)
Q(send_dtmf)
Q(get_call_status)
Q(get_answer_number)
Q(get_call_number)
Q(set_ring_back_tone_cb)
Q(set_local_ip)
Q(get_sip_module_version)
Q(SIP_ERR_NOT_REGISTERED)
Q(SIP_ERR_NOT_SUPPORTED)
Q(SIP_ERR_INVALID_INPUT)
Q(SIP_ERR_PARAMETER_INVALID)
Q(SIP_ERR_CREATE)
Q(SIP_ERR_THREAD_CREATION)
Q(SIP_ERR_TIMEOUT)
Q(SIP_ERR_UNKNOWN_ERROR)
Q(SIP_ERR_SUCCESS)
Q(CALL_STATE_NULL)
Q(CALL_STATE_INCOMING)
Q(CALL_STATE_ANSWERD)
Q(CALL_STATE_CALLING)
Q(CALL_STATE_ABNORMAL)
Q(SIP_REGISTERED)
Q(SIP_NO_REGISTERED)
Q(SIP_ERR_REGISTERED)
Q(VOIP_EVENT_REGISTERED)
Q(VOIP_EVENT_DEREGISTERED)
Q(VOIP_EVENT_DIALING)
Q(VOIP_EVENT_RINGING)
Q(VOIP_EVENT_CANCELED)
Q(VOIP_EVENT_ANSWERED)
Q(VOIP_EVENT_BYE)
Q(VOIP_EVENT_ENDED)
Q(VOIP_EVENT_ERROR)
Q(VOIP_EVENT_FAILED)
Q(init)
Q(__name__)
Q(sip)
Q(__init__)
Q(init)
