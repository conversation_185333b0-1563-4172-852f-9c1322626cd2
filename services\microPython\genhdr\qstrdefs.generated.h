// This file was automatically generated by makeqstrdata.py

QDEF(MP_QSTRnull, (const byte*)"\x00\x00" "")
QDEF(MP_QSTR_, (const byte*)"\x05\x00" "")
QDEF(MP_QSTR___dir__, (const byte*)"\x7a\x07" "__dir__")
QDEF(MP_QSTR__0x0a_, (const byte*)"\xaf\x01" "\x0a")
QDEF(MP_QSTR__space_, (const byte*)"\x85\x01" " ")
QDEF(MP_QSTR__star_, (const byte*)"\x8f\x01" "*")
QDEF(MP_QSTR__slash_, (const byte*)"\x8a\x01" "/")
QDEF(MP_QSTR__lt_module_gt_, (const byte*)"\xbd\x08" "<module>")
QDEF(MP_QSTR__, (const byte*)"\xfa\x01" "_")
QDEF(MP_QSTR___call__, (const byte*)"\xa7\x08" "__call__")
QDEF(MP_QSTR___class__, (const byte*)"\x2b\x09" "__class__")
QDEF(MP_QSTR___delitem__, (const byte*)"\xfd\x0b" "__delitem__")
QDEF(MP_QSTR___enter__, (const byte*)"\x6d\x09" "__enter__")
QDEF(MP_QSTR___exit__, (const byte*)"\x45\x08" "__exit__")
QDEF(MP_QSTR___getattr__, (const byte*)"\x40\x0b" "__getattr__")
QDEF(MP_QSTR___getitem__, (const byte*)"\x26\x0b" "__getitem__")
QDEF(MP_QSTR___hash__, (const byte*)"\xf7\x08" "__hash__")
QDEF(MP_QSTR___init__, (const byte*)"\x5f\x08" "__init__")
QDEF(MP_QSTR___int__, (const byte*)"\x16\x07" "__int__")
QDEF(MP_QSTR___iter__, (const byte*)"\xcf\x08" "__iter__")
QDEF(MP_QSTR___len__, (const byte*)"\xe2\x07" "__len__")
QDEF(MP_QSTR___main__, (const byte*)"\x8e\x08" "__main__")
QDEF(MP_QSTR___module__, (const byte*)"\xff\x0a" "__module__")
QDEF(MP_QSTR___name__, (const byte*)"\xe2\x08" "__name__")
QDEF(MP_QSTR___new__, (const byte*)"\x79\x07" "__new__")
QDEF(MP_QSTR___next__, (const byte*)"\x02\x08" "__next__")
QDEF(MP_QSTR___qualname__, (const byte*)"\x6b\x0c" "__qualname__")
QDEF(MP_QSTR___repr__, (const byte*)"\x10\x08" "__repr__")
QDEF(MP_QSTR___setitem__, (const byte*)"\x32\x0b" "__setitem__")
QDEF(MP_QSTR___str__, (const byte*)"\xd0\x07" "__str__")
QDEF(MP_QSTR_ArithmeticError, (const byte*)"\x2d\x0f" "ArithmeticError")
QDEF(MP_QSTR_AssertionError, (const byte*)"\x97\x0e" "AssertionError")
QDEF(MP_QSTR_AttributeError, (const byte*)"\x21\x0e" "AttributeError")
QDEF(MP_QSTR_BaseException, (const byte*)"\x07\x0d" "BaseException")
QDEF(MP_QSTR_EOFError, (const byte*)"\x91\x08" "EOFError")
QDEF(MP_QSTR_Ellipsis, (const byte*)"\xf0\x08" "Ellipsis")
QDEF(MP_QSTR_Exception, (const byte*)"\xf2\x09" "Exception")
QDEF(MP_QSTR_GeneratorExit, (const byte*)"\x16\x0d" "GeneratorExit")
QDEF(MP_QSTR_ImportError, (const byte*)"\x20\x0b" "ImportError")
QDEF(MP_QSTR_IndentationError, (const byte*)"\x5c\x10" "IndentationError")
QDEF(MP_QSTR_IndexError, (const byte*)"\x83\x0a" "IndexError")
QDEF(MP_QSTR_KeyError, (const byte*)"\xea\x08" "KeyError")
QDEF(MP_QSTR_KeyboardInterrupt, (const byte*)"\xaf\x11" "KeyboardInterrupt")
QDEF(MP_QSTR_LookupError, (const byte*)"\xff\x0b" "LookupError")
QDEF(MP_QSTR_MemoryError, (const byte*)"\xdc\x0b" "MemoryError")
QDEF(MP_QSTR_NameError, (const byte*)"\xba\x09" "NameError")
QDEF(MP_QSTR_NoneType, (const byte*)"\x17\x08" "NoneType")
QDEF(MP_QSTR_NotImplementedError, (const byte*)"\xc6\x13" "NotImplementedError")
QDEF(MP_QSTR_OSError, (const byte*)"\xa1\x07" "OSError")
QDEF(MP_QSTR_OverflowError, (const byte*)"\x81\x0d" "OverflowError")
QDEF(MP_QSTR_RuntimeError, (const byte*)"\x61\x0c" "RuntimeError")
QDEF(MP_QSTR_StopIteration, (const byte*)"\xea\x0d" "StopIteration")
QDEF(MP_QSTR_SyntaxError, (const byte*)"\x94\x0b" "SyntaxError")
QDEF(MP_QSTR_SystemExit, (const byte*)"\x20\x0a" "SystemExit")
QDEF(MP_QSTR_TypeError, (const byte*)"\x25\x09" "TypeError")
QDEF(MP_QSTR_ValueError, (const byte*)"\x96\x0a" "ValueError")
QDEF(MP_QSTR_ZeroDivisionError, (const byte*)"\xb6\x11" "ZeroDivisionError")
QDEF(MP_QSTR_abs, (const byte*)"\x95\x03" "abs")
QDEF(MP_QSTR_all, (const byte*)"\x44\x03" "all")
QDEF(MP_QSTR_any, (const byte*)"\x13\x03" "any")
QDEF(MP_QSTR_append, (const byte*)"\x6b\x06" "append")
QDEF(MP_QSTR_args, (const byte*)"\xc2\x04" "args")
QDEF(MP_QSTR_bool, (const byte*)"\xeb\x04" "bool")
QDEF(MP_QSTR_builtins, (const byte*)"\xf7\x08" "builtins")
QDEF(MP_QSTR_bytearray, (const byte*)"\x76\x09" "bytearray")
QDEF(MP_QSTR_bytecode, (const byte*)"\x22\x08" "bytecode")
QDEF(MP_QSTR_bytes, (const byte*)"\x5c\x05" "bytes")
QDEF(MP_QSTR_callable, (const byte*)"\x0d\x08" "callable")
QDEF(MP_QSTR_chr, (const byte*)"\xdc\x03" "chr")
QDEF(MP_QSTR_classmethod, (const byte*)"\xb4\x0b" "classmethod")
QDEF(MP_QSTR_clear, (const byte*)"\x7c\x05" "clear")
QDEF(MP_QSTR_close, (const byte*)"\x33\x05" "close")
QDEF(MP_QSTR_const, (const byte*)"\xc0\x05" "const")
QDEF(MP_QSTR_copy, (const byte*)"\xe0\x04" "copy")
QDEF(MP_QSTR_count, (const byte*)"\xa6\x05" "count")
QDEF(MP_QSTR_dict, (const byte*)"\x3f\x04" "dict")
QDEF(MP_QSTR_dir, (const byte*)"\xfa\x03" "dir")
QDEF(MP_QSTR_divmod, (const byte*)"\xb8\x06" "divmod")
QDEF(MP_QSTR_end, (const byte*)"\x0a\x03" "end")
QDEF(MP_QSTR_endswith, (const byte*)"\x1b\x08" "endswith")
QDEF(MP_QSTR_eval, (const byte*)"\x9b\x04" "eval")
QDEF(MP_QSTR_exec, (const byte*)"\x1e\x04" "exec")
QDEF(MP_QSTR_extend, (const byte*)"\x63\x06" "extend")
QDEF(MP_QSTR_find, (const byte*)"\x01\x04" "find")
QDEF(MP_QSTR_format, (const byte*)"\x26\x06" "format")
QDEF(MP_QSTR_from_bytes, (const byte*)"\x35\x0a" "from_bytes")
QDEF(MP_QSTR_get, (const byte*)"\x33\x03" "get")
QDEF(MP_QSTR_getattr, (const byte*)"\xc0\x07" "getattr")
QDEF(MP_QSTR_globals, (const byte*)"\x9d\x07" "globals")
QDEF(MP_QSTR_hasattr, (const byte*)"\x8c\x07" "hasattr")
QDEF(MP_QSTR_hash, (const byte*)"\xb7\x04" "hash")
QDEF(MP_QSTR_id, (const byte*)"\x28\x02" "id")
QDEF(MP_QSTR_index, (const byte*)"\x7b\x05" "index")
QDEF(MP_QSTR_insert, (const byte*)"\x12\x06" "insert")
QDEF(MP_QSTR_int, (const byte*)"\x16\x03" "int")
QDEF(MP_QSTR_isalpha, (const byte*)"\xeb\x07" "isalpha")
QDEF(MP_QSTR_isdigit, (const byte*)"\xa8\x07" "isdigit")
QDEF(MP_QSTR_isinstance, (const byte*)"\xb6\x0a" "isinstance")
QDEF(MP_QSTR_islower, (const byte*)"\xfc\x07" "islower")
QDEF(MP_QSTR_isspace, (const byte*)"\x5b\x07" "isspace")
QDEF(MP_QSTR_issubclass, (const byte*)"\xb5\x0a" "issubclass")
QDEF(MP_QSTR_isupper, (const byte*)"\xdd\x07" "isupper")
QDEF(MP_QSTR_items, (const byte*)"\xe3\x05" "items")
QDEF(MP_QSTR_iter, (const byte*)"\x8f\x04" "iter")
QDEF(MP_QSTR_join, (const byte*)"\xa7\x04" "join")
QDEF(MP_QSTR_key, (const byte*)"\x32\x03" "key")
QDEF(MP_QSTR_keys, (const byte*)"\x01\x04" "keys")
QDEF(MP_QSTR_len, (const byte*)"\x62\x03" "len")
QDEF(MP_QSTR_list, (const byte*)"\x27\x04" "list")
QDEF(MP_QSTR_little, (const byte*)"\x89\x06" "little")
QDEF(MP_QSTR_locals, (const byte*)"\x3b\x06" "locals")
QDEF(MP_QSTR_lower, (const byte*)"\xc6\x05" "lower")
QDEF(MP_QSTR_lstrip, (const byte*)"\xe5\x06" "lstrip")
QDEF(MP_QSTR_main, (const byte*)"\xce\x04" "main")
QDEF(MP_QSTR_map, (const byte*)"\xb9\x03" "map")
QDEF(MP_QSTR_micropython, (const byte*)"\x0b\x0b" "micropython")
QDEF(MP_QSTR_next, (const byte*)"\x42\x04" "next")
QDEF(MP_QSTR_object, (const byte*)"\x90\x06" "object")
QDEF(MP_QSTR_open, (const byte*)"\xd1\x04" "open")
QDEF(MP_QSTR_ord, (const byte*)"\x1c\x03" "ord")
QDEF(MP_QSTR_pop, (const byte*)"\x2a\x03" "pop")
QDEF(MP_QSTR_popitem, (const byte*)"\xbf\x07" "popitem")
QDEF(MP_QSTR_pow, (const byte*)"\x2d\x03" "pow")
QDEF(MP_QSTR_print, (const byte*)"\x54\x05" "print")
QDEF(MP_QSTR_range, (const byte*)"\x1a\x05" "range")
QDEF(MP_QSTR_read, (const byte*)"\xb7\x04" "read")
QDEF(MP_QSTR_readinto, (const byte*)"\x4b\x08" "readinto")
QDEF(MP_QSTR_readline, (const byte*)"\xf9\x08" "readline")
QDEF(MP_QSTR_remove, (const byte*)"\x63\x06" "remove")
QDEF(MP_QSTR_replace, (const byte*)"\x49\x07" "replace")
QDEF(MP_QSTR_repr, (const byte*)"\xd0\x04" "repr")
QDEF(MP_QSTR_reverse, (const byte*)"\x25\x07" "reverse")
QDEF(MP_QSTR_rfind, (const byte*)"\xd2\x05" "rfind")
QDEF(MP_QSTR_rindex, (const byte*)"\xe9\x06" "rindex")
QDEF(MP_QSTR_round, (const byte*)"\xe7\x05" "round")
QDEF(MP_QSTR_rsplit, (const byte*)"\xa5\x06" "rsplit")
QDEF(MP_QSTR_rstrip, (const byte*)"\x3b\x06" "rstrip")
QDEF(MP_QSTR_self, (const byte*)"\x79\x04" "self")
QDEF(MP_QSTR_send, (const byte*)"\xb9\x04" "send")
QDEF(MP_QSTR_sep, (const byte*)"\x23\x03" "sep")
QDEF(MP_QSTR_set, (const byte*)"\x27\x03" "set")
QDEF(MP_QSTR_setattr, (const byte*)"\xd4\x07" "setattr")
QDEF(MP_QSTR_setdefault, (const byte*)"\x6c\x0a" "setdefault")
QDEF(MP_QSTR_sort, (const byte*)"\xbf\x04" "sort")
QDEF(MP_QSTR_sorted, (const byte*)"\x5e\x06" "sorted")
QDEF(MP_QSTR_split, (const byte*)"\xb7\x05" "split")
QDEF(MP_QSTR_start, (const byte*)"\x85\x05" "start")
QDEF(MP_QSTR_startswith, (const byte*)"\x74\x0a" "startswith")
QDEF(MP_QSTR_staticmethod, (const byte*)"\x62\x0c" "staticmethod")
QDEF(MP_QSTR_step, (const byte*)"\x57\x04" "step")
QDEF(MP_QSTR_stop, (const byte*)"\x9d\x04" "stop")
QDEF(MP_QSTR_str, (const byte*)"\x50\x03" "str")
QDEF(MP_QSTR_strip, (const byte*)"\x29\x05" "strip")
QDEF(MP_QSTR_sum, (const byte*)"\x2e\x03" "sum")
QDEF(MP_QSTR_super, (const byte*)"\xc4\x05" "super")
QDEF(MP_QSTR_throw, (const byte*)"\xb3\x05" "throw")
QDEF(MP_QSTR_to_bytes, (const byte*)"\xd8\x08" "to_bytes")
QDEF(MP_QSTR_tuple, (const byte*)"\xfd\x05" "tuple")
QDEF(MP_QSTR_type, (const byte*)"\x9d\x04" "type")
QDEF(MP_QSTR_update, (const byte*)"\xb4\x06" "update")
QDEF(MP_QSTR_upper, (const byte*)"\x27\x05" "upper")
QDEF(MP_QSTR_utf_hyphen_8, (const byte*)"\xb7\x05" "utf-8")
QDEF(MP_QSTR_value, (const byte*)"\x4e\x05" "value")
QDEF(MP_QSTR_values, (const byte*)"\x7d\x06" "values")
QDEF(MP_QSTR_write, (const byte*)"\x98\x05" "write")
QDEF(MP_QSTR_zip, (const byte*)"\xe6\x03" "zip")
QDEF(MP_QSTR___LVGL__, (const byte*)"\x74\x08" "__LVGL__")
QDEF(MP_QSTR___SIZE__, (const byte*)"\xa0\x08" "__SIZE__")
QDEF(MP_QSTR___abs__, (const byte*)"\x95\x07" "__abs__")
QDEF(MP_QSTR___add__, (const byte*)"\xc4\x07" "__add__")
QDEF(MP_QSTR___aenter__, (const byte*)"\x4c\x0a" "__aenter__")
QDEF(MP_QSTR___aexit__, (const byte*)"\xc4\x09" "__aexit__")
QDEF(MP_QSTR___aiter__, (const byte*)"\x4e\x09" "__aiter__")
QDEF(MP_QSTR___and__, (const byte*)"\x0e\x07" "__and__")
QDEF(MP_QSTR___anext__, (const byte*)"\x83\x09" "__anext__")
QDEF(MP_QSTR___bases__, (const byte*)"\x03\x09" "__bases__")
QDEF(MP_QSTR___bool__, (const byte*)"\x2b\x08" "__bool__")
QDEF(MP_QSTR___build_class__, (const byte*)"\x42\x0f" "__build_class__")
QDEF(MP_QSTR___cast__, (const byte*)"\x80\x08" "__cast__")
QDEF(MP_QSTR___cast_instance__, (const byte*)"\xd6\x11" "__cast_instance__")
QDEF(MP_QSTR___contains__, (const byte*)"\xc6\x0c" "__contains__")
QDEF(MP_QSTR___del__, (const byte*)"\x68\x07" "__del__")
QDEF(MP_QSTR___dereference__, (const byte*)"\x2f\x0f" "__dereference__")
QDEF(MP_QSTR___dict__, (const byte*)"\x7f\x08" "__dict__")
QDEF(MP_QSTR___divmod__, (const byte*)"\x78\x0a" "__divmod__")
QDEF(MP_QSTR___eq__, (const byte*)"\x71\x06" "__eq__")
QDEF(MP_QSTR___file__, (const byte*)"\x03\x08" "__file__")
QDEF(MP_QSTR___floordiv__, (const byte*)"\x46\x0c" "__floordiv__")
QDEF(MP_QSTR___ge__, (const byte*)"\xa7\x06" "__ge__")
QDEF(MP_QSTR___gt__, (const byte*)"\xb6\x06" "__gt__")
QDEF(MP_QSTR___iadd__, (const byte*)"\x6d\x08" "__iadd__")
QDEF(MP_QSTR___import__, (const byte*)"\x38\x0a" "__import__")
QDEF(MP_QSTR___invert__, (const byte*)"\xf7\x0a" "__invert__")
QDEF(MP_QSTR___isub__, (const byte*)"\x08\x08" "__isub__")
QDEF(MP_QSTR___le__, (const byte*)"\xcc\x06" "__le__")
QDEF(MP_QSTR___lshift__, (const byte*)"\x09\x0a" "__lshift__")
QDEF(MP_QSTR___lt__, (const byte*)"\x5d\x06" "__lt__")
QDEF(MP_QSTR___matmul__, (const byte*)"\x49\x0a" "__matmul__")
QDEF(MP_QSTR___mod__, (const byte*)"\x63\x07" "__mod__")
QDEF(MP_QSTR___mul__, (const byte*)"\x31\x07" "__mul__")
QDEF(MP_QSTR___ne__, (const byte*)"\x0e\x06" "__ne__")
QDEF(MP_QSTR___neg__, (const byte*)"\x69\x07" "__neg__")
QDEF(MP_QSTR___or__, (const byte*)"\x38\x06" "__or__")
QDEF(MP_QSTR___path__, (const byte*)"\xc8\x08" "__path__")
QDEF(MP_QSTR___pos__, (const byte*)"\x29\x07" "__pos__")
QDEF(MP_QSTR___pow__, (const byte*)"\x2d\x07" "__pow__")
QDEF(MP_QSTR___qpy_module_deinit__, (const byte*)"\x5c\x15" "__qpy_module_deinit__")
QDEF(MP_QSTR___repl_print__, (const byte*)"\x01\x0e" "__repl_print__")
QDEF(MP_QSTR___reversed__, (const byte*)"\x61\x0c" "__reversed__")
QDEF(MP_QSTR___rshift__, (const byte*)"\x57\x0a" "__rshift__")
QDEF(MP_QSTR___sub__, (const byte*)"\x21\x07" "__sub__")
QDEF(MP_QSTR___traceback__, (const byte*)"\x4f\x0d" "__traceback__")
QDEF(MP_QSTR___truediv__, (const byte*)"\x88\x0b" "__truediv__")
QDEF(MP_QSTR___xor__, (const byte*)"\x20\x07" "__xor__")
QDEF(MP_QSTR__percent__hash_o, (const byte*)"\x6c\x03" "%#o")
QDEF(MP_QSTR__percent__hash_x, (const byte*)"\x7b\x03" "%#x")
QDEF(MP_QSTR__brace_open__colon__hash_b_brace_close_, (const byte*)"\x58\x05" "{:#b}")
QDEF(MP_QSTR_maximum_space_recursion_space_depth_space_exceeded, (const byte*)"\x73\x20" "maximum recursion depth exceeded")
QDEF(MP_QSTR__lt_lambda_gt_, (const byte*)"\x80\x08" "<lambda>")
QDEF(MP_QSTR__lt_listcomp_gt_, (const byte*)"\xd4\x0a" "<listcomp>")
QDEF(MP_QSTR__lt_dictcomp_gt_, (const byte*)"\xcc\x0a" "<dictcomp>")
QDEF(MP_QSTR__lt_setcomp_gt_, (const byte*)"\x54\x09" "<setcomp>")
QDEF(MP_QSTR__lt_genexpr_gt_, (const byte*)"\x34\x09" "<genexpr>")
QDEF(MP_QSTR__lt_string_gt_, (const byte*)"\x52\x08" "<string>")
QDEF(MP_QSTR__lt_stdin_gt_, (const byte*)"\xe3\x07" "<stdin>")
QDEF(MP_QSTR_5803W, (const byte*)"\x5c\x05" "5803W")
QDEF(MP_QSTR_ACTIVE, (const byte*)"\xa9\x06" "ACTIVE")
QDEF(MP_QSTR_ADC, (const byte*)"\x63\x03" "ADC")
QDEF(MP_QSTR_ADC0, (const byte*)"\xf3\x04" "ADC0")
QDEF(MP_QSTR_ADC1, (const byte*)"\xf2\x04" "ADC1")
QDEF(MP_QSTR_ADC2, (const byte*)"\xf1\x04" "ADC2")
QDEF(MP_QSTR_ADC3, (const byte*)"\xf0\x04" "ADC3")
QDEF(MP_QSTR_ADDITIVE, (const byte*)"\xa3\x08" "ADDITIVE")
QDEF(MP_QSTR_ADV_HITTEST, (const byte*)"\x4a\x0b" "ADV_HITTEST")
QDEF(MP_QSTR_AF_INET, (const byte*)"\xeb\x07" "AF_INET")
QDEF(MP_QSTR_AF_INET6, (const byte*)"\x7d\x08" "AF_INET6")
QDEF(MP_QSTR_ALIGN, (const byte*)"\xc8\x05" "ALIGN")
QDEF(MP_QSTR_ALL, (const byte*)"\x64\x03" "ALL")
QDEF(MP_QSTR_ALPHA_1BIT, (const byte*)"\xe0\x0a" "ALPHA_1BIT")
QDEF(MP_QSTR_ALPHA_2BIT, (const byte*)"\x03\x0a" "ALPHA_2BIT")
QDEF(MP_QSTR_ALPHA_4BIT, (const byte*)"\x45\x0a" "ALPHA_4BIT")
QDEF(MP_QSTR_ALPHA_8BIT, (const byte*)"\xc9\x0a" "ALPHA_8BIT")
QDEF(MP_QSTR_AMBER, (const byte*)"\xbc\x05" "AMBER")
QDEF(MP_QSTR_ANGLE, (const byte*)"\xe4\x05" "ANGLE")
QDEF(MP_QSTR_ANIM, (const byte*)"\xce\x04" "ANIM")
QDEF(MP_QSTR_ANIM_IMG_PART, (const byte*)"\xda\x0d" "ANIM_IMG_PART")
QDEF(MP_QSTR_ANIM_REPEAT, (const byte*)"\x66\x0b" "ANIM_REPEAT")
QDEF(MP_QSTR_ANIM_SPEED, (const byte*)"\x56\x0a" "ANIM_SPEED")
QDEF(MP_QSTR_ANIM_TIME, (const byte*)"\xa4\x09" "ANIM_TIME")
QDEF(MP_QSTR_ANY, (const byte*)"\x33\x03" "ANY")
QDEF(MP_QSTR_ARC, (const byte*)"\xb5\x03" "ARC")
QDEF(MP_QSTR_ARC_COLOR, (const byte*)"\xd7\x09" "ARC_COLOR")
QDEF(MP_QSTR_ARC_IMG_SRC, (const byte*)"\x94\x0b" "ARC_IMG_SRC")
QDEF(MP_QSTR_ARC_OPA, (const byte*)"\xd4\x07" "ARC_OPA")
QDEF(MP_QSTR_ARC_ROUNDED, (const byte*)"\x49\x0b" "ARC_ROUNDED")
QDEF(MP_QSTR_ARC_WIDTH, (const byte*)"\xac\x09" "ARC_WIDTH")
QDEF(MP_QSTR_ARRAY, (const byte*)"\x5c\x05" "ARRAY")
QDEF(MP_QSTR_ASR5803W, (const byte*)"\x1c\x08" "ASR5803W")
QDEF(MP_QSTR_AUDIO, (const byte*)"\x33\x05" "AUDIO")
QDEF(MP_QSTR_AUTO, (const byte*)"\xea\x04" "AUTO")
QDEF(MP_QSTR_AXIS, (const byte*)"\xc6\x04" "AXIS")
QDEF(MP_QSTR_Audio, (const byte*)"\x73\x05" "Audio")
QDEF(MP_QSTR_BACKGROUND, (const byte*)"\xab\x0a" "BACKGROUND")
QDEF(MP_QSTR_BACKSPACE, (const byte*)"\x8a\x09" "BACKSPACE")
QDEF(MP_QSTR_BAR, (const byte*)"\x14\x03" "BAR")
QDEF(MP_QSTR_BASE_DIR, (const byte*)"\xf0\x08" "BASE_DIR")
QDEF(MP_QSTR_BATTERY_1, (const byte*)"\x86\x09" "BATTERY_1")
QDEF(MP_QSTR_BATTERY_2, (const byte*)"\x85\x09" "BATTERY_2")
QDEF(MP_QSTR_BATTERY_3, (const byte*)"\x84\x09" "BATTERY_3")
QDEF(MP_QSTR_BATTERY_EMPTY, (const byte*)"\x42\x0d" "BATTERY_EMPTY")
QDEF(MP_QSTR_BATTERY_FULL, (const byte*)"\xc4\x0c" "BATTERY_FULL")
QDEF(MP_QSTR_BELL, (const byte*)"\x02\x04" "BELL")
QDEF(MP_QSTR_BFINT16, (const byte*)"\x95\x07" "BFINT16")
QDEF(MP_QSTR_BFINT32, (const byte*)"\x53\x07" "BFINT32")
QDEF(MP_QSTR_BFINT8, (const byte*)"\x4a\x06" "BFINT8")
QDEF(MP_QSTR_BFUINT16, (const byte*)"\x40\x08" "BFUINT16")
QDEF(MP_QSTR_BFUINT32, (const byte*)"\x06\x08" "BFUINT32")
QDEF(MP_QSTR_BFUINT8, (const byte*)"\xbf\x07" "BFUINT8")
QDEF(MP_QSTR_BF_LEN, (const byte*)"\x19\x06" "BF_LEN")
QDEF(MP_QSTR_BF_POS, (const byte*)"\x52\x06" "BF_POS")
QDEF(MP_QSTR_BG_COLOR, (const byte*)"\xa2\x08" "BG_COLOR")
QDEF(MP_QSTR_BG_GRAD_COLOR, (const byte*)"\x6d\x0d" "BG_GRAD_COLOR")
QDEF(MP_QSTR_BG_GRAD_DIR, (const byte*)"\xcf\x0b" "BG_GRAD_DIR")
QDEF(MP_QSTR_BG_GRAD_STOP, (const byte*)"\xa8\x0c" "BG_GRAD_STOP")
QDEF(MP_QSTR_BG_IMG_OPA, (const byte*)"\x7d\x0a" "BG_IMG_OPA")
QDEF(MP_QSTR_BG_IMG_RECOLOR, (const byte*)"\xc9\x0e" "BG_IMG_RECOLOR")
QDEF(MP_QSTR_BG_IMG_RECOLOR_OPA, (const byte*)"\x68\x12" "BG_IMG_RECOLOR_OPA")
QDEF(MP_QSTR_BG_IMG_SRC, (const byte*)"\x21\x0a" "BG_IMG_SRC")
QDEF(MP_QSTR_BG_IMG_TILED, (const byte*)"\x33\x0c" "BG_IMG_TILED")
QDEF(MP_QSTR_BG_MAIN_STOP, (const byte*)"\x93\x0c" "BG_MAIN_STOP")
QDEF(MP_QSTR_BG_OPA, (const byte*)"\xe1\x06" "BG_OPA")
QDEF(MP_QSTR_BIG_ENDIAN, (const byte*)"\xff\x0a" "BIG_ENDIAN")
QDEF(MP_QSTR_BLEND_MODE, (const byte*)"\x98\x0a" "BLEND_MODE")
QDEF(MP_QSTR_BLINK_OFF, (const byte*)"\xd7\x09" "BLINK_OFF")
QDEF(MP_QSTR_BLINK_ON, (const byte*)"\x79\x08" "BLINK_ON")
QDEF(MP_QSTR_BLOCK, (const byte*)"\x0c\x05" "BLOCK")
QDEF(MP_QSTR_BLUE, (const byte*)"\x3b\x04" "BLUE")
QDEF(MP_QSTR_BLUETOOTH, (const byte*)"\x73\x09" "BLUETOOTH")
QDEF(MP_QSTR_BLUE_GREY, (const byte*)"\xcd\x09" "BLUE_GREY")
QDEF(MP_QSTR_BORDER_COLOR, (const byte*)"\x0b\x0c" "BORDER_COLOR")
QDEF(MP_QSTR_BORDER_OPA, (const byte*)"\x08\x0a" "BORDER_OPA")
QDEF(MP_QSTR_BORDER_POST, (const byte*)"\x2e\x0b" "BORDER_POST")
QDEF(MP_QSTR_BORDER_SIDE, (const byte*)"\x2d\x0b" "BORDER_SIDE")
QDEF(MP_QSTR_BORDER_WIDTH, (const byte*)"\xf0\x0c" "BORDER_WIDTH")
QDEF(MP_QSTR_BOTH, (const byte*)"\x14\x04" "BOTH")
QDEF(MP_QSTR_BOTTOM, (const byte*)"\x6a\x06" "BOTTOM")
QDEF(MP_QSTR_BOTTOM_LEFT, (const byte*)"\xee\x0b" "BOTTOM_LEFT")
QDEF(MP_QSTR_BOTTOM_MID, (const byte*)"\xf5\x0a" "BOTTOM_MID")
QDEF(MP_QSTR_BOTTOM_RIGHT, (const byte*)"\x35\x0c" "BOTTOM_RIGHT")
QDEF(MP_QSTR_BOX, (const byte*)"\xd0\x03" "BOX")
QDEF(MP_QSTR_BREAK, (const byte*)"\x1a\x05" "BREAK")
QDEF(MP_QSTR_BROWN, (const byte*)"\xc3\x05" "BROWN")
QDEF(MP_QSTR_BTN, (const byte*)"\xbd\x03" "BTN")
QDEF(MP_QSTR_BTNMATRIX_BTN, (const byte*)"\x41\x0d" "BTNMATRIX_BTN")
QDEF(MP_QSTR_BULLET, (const byte*)"\xa3\x06" "BULLET")
QDEF(MP_QSTR_BUSY, (const byte*)"\xf8\x04" "BUSY")
QDEF(MP_QSTR_BUTTON, (const byte*)"\xf3\x06" "BUTTON")
QDEF(MP_QSTR_Blob, (const byte*)"\x86\x04" "Blob")
QDEF(MP_QSTR_BytesIO, (const byte*)"\x1a\x07" "BytesIO")
QDEF(MP_QSTR_CALL, (const byte*)"\xa7\x04" "CALL")
QDEF(MP_QSTR_CALL_STATE_ABNORMAL, (const byte*)"\xe0\x13" "CALL_STATE_ABNORMAL")
QDEF(MP_QSTR_CALL_STATE_ANSWERD, (const byte*)"\xc8\x12" "CALL_STATE_ANSWERD")
QDEF(MP_QSTR_CALL_STATE_CALLING, (const byte*)"\x52\x12" "CALL_STATE_CALLING")
QDEF(MP_QSTR_CALL_STATE_INCOMING, (const byte*)"\x96\x13" "CALL_STATE_INCOMING")
QDEF(MP_QSTR_CALL_STATE_NULL, (const byte*)"\x6b\x0f" "CALL_STATE_NULL")
QDEF(MP_QSTR_CANCEL, (const byte*)"\x03\x06" "CANCEL")
QDEF(MP_QSTR_CELL, (const byte*)"\xa3\x04" "CELL")
QDEF(MP_QSTR_CELL_CTRL, (const byte*)"\xb5\x09" "CELL_CTRL")
QDEF(MP_QSTR_CENTER, (const byte*)"\x8e\x06" "CENTER")
QDEF(MP_QSTR_CF, (const byte*)"\xe0\x02" "CF")
QDEF(MP_QSTR_CHANGED, (const byte*)"\x07\x07" "CHANGED")
QDEF(MP_QSTR_CHARGE, (const byte*)"\xff\x06" "CHARGE")
QDEF(MP_QSTR_CHART_POINT, (const byte*)"\xfa\x0b" "CHART_POINT")
QDEF(MP_QSTR_CHECKABLE, (const byte*)"\xe9\x09" "CHECKABLE")
QDEF(MP_QSTR_CHECKED, (const byte*)"\xe2\x07" "CHECKED")
QDEF(MP_QSTR_CHECKED_DISABLED, (const byte*)"\x6d\x10" "CHECKED_DISABLED")
QDEF(MP_QSTR_CHECKED_PRESSED, (const byte*)"\x5b\x0f" "CHECKED_PRESSED")
QDEF(MP_QSTR_CHECKED_RELEASED, (const byte*)"\xb0\x10" "CHECKED_RELEASED")
QDEF(MP_QSTR_CHILD_CHANGED, (const byte*)"\xb2\x0d" "CHILD_CHANGED")
QDEF(MP_QSTR_CIRCLE, (const byte*)"\xf7\x06" "CIRCLE")
QDEF(MP_QSTR_CIRCULAR, (const byte*)"\xb4\x08" "CIRCULAR")
QDEF(MP_QSTR_CLASS_EDITABLE, (const byte*)"\x82\x0e" "CLASS_EDITABLE")
QDEF(MP_QSTR_CLASS_GROUP_DEF, (const byte*)"\x53\x0f" "CLASS_GROUP_DEF")
QDEF(MP_QSTR_CLICKABLE, (const byte*)"\x61\x09" "CLICKABLE")
QDEF(MP_QSTR_CLICKED, (const byte*)"\x6a\x07" "CLICKED")
QDEF(MP_QSTR_CLICK_FOCUSABLE, (const byte*)"\x92\x0f" "CLICK_FOCUSABLE")
QDEF(MP_QSTR_CLICK_TRIG, (const byte*)"\x9c\x0a" "CLICK_TRIG")
QDEF(MP_QSTR_CLIP, (const byte*)"\x93\x04" "CLIP")
QDEF(MP_QSTR_CLIP_CORNER, (const byte*)"\x6b\x0b" "CLIP_CORNER")
QDEF(MP_QSTR_CLOSE, (const byte*)"\x53\x05" "CLOSE")
QDEF(MP_QSTR_COLOR_FILTER_DSC, (const byte*)"\xcc\x10" "COLOR_FILTER_DSC")
QDEF(MP_QSTR_COLOR_FILTER_OPA, (const byte*)"\xa6\x10" "COLOR_FILTER_OPA")
QDEF(MP_QSTR_COLUMN, (const byte*)"\xd3\x06" "COLUMN")
QDEF(MP_QSTR_COLUMN_REVERSE, (const byte*)"\x4c\x0e" "COLUMN_REVERSE")
QDEF(MP_QSTR_COLUMN_WRAP, (const byte*)"\x78\x0b" "COLUMN_WRAP")
QDEF(MP_QSTR_COLUMN_WRAP_REVERSE, (const byte*)"\xe7\x13" "COLUMN_WRAP_REVERSE")
QDEF(MP_QSTR_COMPRESSED, (const byte*)"\x42\x0a" "COMPRESSED")
QDEF(MP_QSTR_COMPRESSED_NO_PREFILTER, (const byte*)"\x44\x17" "COMPRESSED_NO_PREFILTER")
QDEF(MP_QSTR_CONTENT, (const byte*)"\x4c\x07" "CONTENT")
QDEF(MP_QSTR_COORD, (const byte*)"\x90\x05" "COORD")
QDEF(MP_QSTR_COPY, (const byte*)"\x20\x04" "COPY")
QDEF(MP_QSTR_COVER, (const byte*)"\xe8\x05" "COVER")
QDEF(MP_QSTR_COVER_CHECK, (const byte*)"\xb1\x0b" "COVER_CHECK")
QDEF(MP_QSTR_COVER_RES, (const byte*)"\x73\x09" "COVER_RES")
QDEF(MP_QSTR_CTRL, (const byte*)"\x2c\x04" "CTRL")
QDEF(MP_QSTR_CUR, (const byte*)"\x01\x03" "CUR")
QDEF(MP_QSTR_CURSOR, (const byte*)"\xcf\x06" "CURSOR")
QDEF(MP_QSTR_CUSTOM_1, (const byte*)"\x78\x08" "CUSTOM_1")
QDEF(MP_QSTR_CUSTOM_2, (const byte*)"\x7b\x08" "CUSTOM_2")
QDEF(MP_QSTR_CUSTOM_3, (const byte*)"\x7a\x08" "CUSTOM_3")
QDEF(MP_QSTR_CUSTOM_4, (const byte*)"\x7d\x08" "CUSTOM_4")
QDEF(MP_QSTR_CUSTOM_FIRST, (const byte*)"\x93\x0c" "CUSTOM_FIRST")
QDEF(MP_QSTR_CUT, (const byte*)"\x07\x03" "CUT")
QDEF(MP_QSTR_CYAN, (const byte*)"\x10\x04" "CYAN")
QDEF(MP_QSTR_C_Pointer, (const byte*)"\x02\x09" "C_Pointer")
QDEF(MP_QSTR_CancelledError, (const byte*)"\xf6\x0e" "CancelledError")
QDEF(MP_QSTR_DEEP_ORANGE, (const byte*)"\xbe\x0b" "DEEP_ORANGE")
QDEF(MP_QSTR_DEEP_PURPLE, (const byte*)"\x80\x0b" "DEEP_PURPLE")
QDEF(MP_QSTR_DEFAULT, (const byte*)"\x2e\x07" "DEFAULT")
QDEF(MP_QSTR_DEFOCUSED, (const byte*)"\x29\x09" "DEFOCUSED")
QDEF(MP_QSTR_DEL, (const byte*)"\x88\x03" "DEL")
QDEF(MP_QSTR_DELETE, (const byte*)"\x1c\x06" "DELETE")
QDEF(MP_QSTR_DENIED, (const byte*)"\x22\x06" "DENIED")
QDEF(MP_QSTR_DIR, (const byte*)"\x1a\x03" "DIR")
QDEF(MP_QSTR_DIRECTORY, (const byte*)"\xac\x09" "DIRECTORY")
QDEF(MP_QSTR_DISABLED, (const byte*)"\x95\x08" "DISABLED")
QDEF(MP_QSTR_DISP_ROT, (const byte*)"\xfd\x08" "DISP_ROT")
QDEF(MP_QSTR_DIV_LINE_HOR, (const byte*)"\xc5\x0c" "DIV_LINE_HOR")
QDEF(MP_QSTR_DIV_LINE_INIT, (const byte*)"\x6a\x0d" "DIV_LINE_INIT")
QDEF(MP_QSTR_DIV_LINE_VER, (const byte*)"\x11\x0c" "DIV_LINE_VER")
QDEF(MP_QSTR_DOT, (const byte*)"\x5a\x03" "DOT")
QDEF(MP_QSTR_DOWN, (const byte*)"\x37\x04" "DOWN")
QDEF(MP_QSTR_DOWNLOAD, (const byte*)"\x91\x08" "DOWNLOAD")
QDEF(MP_QSTR_DPI, (const byte*)"\x38\x03" "DPI")
QDEF(MP_QSTR_DRAW_MAIN, (const byte*)"\x91\x09" "DRAW_MAIN")
QDEF(MP_QSTR_DRAW_MAIN_BEGIN, (const byte*)"\x29\x0f" "DRAW_MAIN_BEGIN")
QDEF(MP_QSTR_DRAW_MAIN_END, (const byte*)"\xe1\x0d" "DRAW_MAIN_END")
QDEF(MP_QSTR_DRAW_MASK_LINE_SIDE, (const byte*)"\x5b\x13" "DRAW_MASK_LINE_SIDE")
QDEF(MP_QSTR_DRAW_MASK_RES, (const byte*)"\x55\x0d" "DRAW_MASK_RES")
QDEF(MP_QSTR_DRAW_MASK_TYPE, (const byte*)"\x09\x0e" "DRAW_MASK_TYPE")
QDEF(MP_QSTR_DRAW_PART, (const byte*)"\x4d\x09" "DRAW_PART")
QDEF(MP_QSTR_DRAW_PART_BEGIN, (const byte*)"\xf5\x0f" "DRAW_PART_BEGIN")
QDEF(MP_QSTR_DRAW_PART_END, (const byte*)"\xbd\x0d" "DRAW_PART_END")
QDEF(MP_QSTR_DRAW_POST, (const byte*)"\x22\x09" "DRAW_POST")
QDEF(MP_QSTR_DRAW_POST_BEGIN, (const byte*)"\x5a\x0f" "DRAW_POST_BEGIN")
QDEF(MP_QSTR_DRAW_POST_END, (const byte*)"\xd2\x0d" "DRAW_POST_END")
QDEF(MP_QSTR_DRIVE, (const byte*)"\xa9\x05" "DRIVE")
QDEF(MP_QSTR_DROPDOWN_POS, (const byte*)"\xcd\x0c" "DROPDOWN_POS")
QDEF(MP_QSTR_DUMMY, (const byte*)"\x2d\x05" "DUMMY")
QDEF(MP_QSTR_DecompIO, (const byte*)"\x93\x08" "DecompIO")
QDEF(MP_QSTR_EACCES, (const byte*)"\x37\x06" "EACCES")
QDEF(MP_QSTR_EADDRINUSE, (const byte*)"\x17\x0a" "EADDRINUSE")
QDEF(MP_QSTR_EAGAIN, (const byte*)"\x20\x06" "EAGAIN")
QDEF(MP_QSTR_EALREADY, (const byte*)"\x46\x08" "EALREADY")
QDEF(MP_QSTR_EBADF, (const byte*)"\x61\x05" "EBADF")
QDEF(MP_QSTR_ECONNABORTED, (const byte*)"\x27\x0c" "ECONNABORTED")
QDEF(MP_QSTR_ECONNREFUSED, (const byte*)"\x3a\x0c" "ECONNREFUSED")
QDEF(MP_QSTR_ECONNRESET, (const byte*)"\x19\x0a" "ECONNRESET")
QDEF(MP_QSTR_EDIT, (const byte*)"\x59\x04" "EDIT")
QDEF(MP_QSTR_EDITED, (const byte*)"\xf8\x06" "EDITED")
QDEF(MP_QSTR_EEXIST, (const byte*)"\x53\x06" "EEXIST")
QDEF(MP_QSTR_EHOSTUNREACH, (const byte*)"\x86\x0c" "EHOSTUNREACH")
QDEF(MP_QSTR_EINPROGRESS, (const byte*)"\x9a\x0b" "EINPROGRESS")
QDEF(MP_QSTR_EINVAL, (const byte*)"\x5c\x06" "EINVAL")
QDEF(MP_QSTR_EIO, (const byte*)"\x86\x03" "EIO")
QDEF(MP_QSTR_EISDIR, (const byte*)"\xa5\x06" "EISDIR")
QDEF(MP_QSTR_EJECT, (const byte*)"\x18\x05" "EJECT")
QDEF(MP_QSTR_ELLIPSIS, (const byte*)"\x10\x08" "ELLIPSIS")
QDEF(MP_QSTR_ENCODER, (const byte*)"\x31\x07" "ENCODER")
QDEF(MP_QSTR_END, (const byte*)"\x2a\x03" "END")
QDEF(MP_QSTR_ENOBUFS, (const byte*)"\xe3\x07" "ENOBUFS")
QDEF(MP_QSTR_ENODEV, (const byte*)"\xb6\x06" "ENODEV")
QDEF(MP_QSTR_ENOENT, (const byte*)"\x5e\x06" "ENOENT")
QDEF(MP_QSTR_ENOMEM, (const byte*)"\xa4\x06" "ENOMEM")
QDEF(MP_QSTR_ENOTCONN, (const byte*)"\x79\x08" "ENOTCONN")
QDEF(MP_QSTR_ENTER, (const byte*)"\x4d\x05" "ENTER")
QDEF(MP_QSTR_ENUM_LV_ANIM_REPEAT, (const byte*)"\x2f\x13" "ENUM_LV_ANIM_REPEAT")
QDEF(MP_QSTR_ENUM_LV_BTNMATRIX_BTN, (const byte*)"\x88\x15" "ENUM_LV_BTNMATRIX_BTN")
QDEF(MP_QSTR_ENUM_LV_CHART_POINT, (const byte*)"\xb3\x13" "ENUM_LV_CHART_POINT")
QDEF(MP_QSTR_ENUM_LV_COORD, (const byte*)"\xd9\x0d" "ENUM_LV_COORD")
QDEF(MP_QSTR_ENUM_LV_DPI, (const byte*)"\xf1\x0b" "ENUM_LV_DPI")
QDEF(MP_QSTR_ENUM_LV_DROPDOWN_POS, (const byte*)"\x64\x14" "ENUM_LV_DROPDOWN_POS")
QDEF(MP_QSTR_ENUM_LV_GRID, (const byte*)"\x34\x0c" "ENUM_LV_GRID")
QDEF(MP_QSTR_ENUM_LV_GRID_TEMPLATE, (const byte*)"\x9b\x15" "ENUM_LV_GRID_TEMPLATE")
QDEF(MP_QSTR_ENUM_LV_IMG_ZOOM, (const byte*)"\x47\x10" "ENUM_LV_IMG_ZOOM")
QDEF(MP_QSTR_ENUM_LV_LABEL_DOT, (const byte*)"\xea\x11" "ENUM_LV_LABEL_DOT")
QDEF(MP_QSTR_ENUM_LV_LABEL_POS, (const byte*)"\xf9\x11" "ENUM_LV_LABEL_POS")
QDEF(MP_QSTR_ENUM_LV_LABEL_TEXT_SELECTION, (const byte*)"\x37\x1c" "ENUM_LV_LABEL_TEXT_SELECTION")
QDEF(MP_QSTR_ENUM_LV_LOG_LEVEL, (const byte*)"\x61\x11" "ENUM_LV_LOG_LEVEL")
QDEF(MP_QSTR_ENUM_LV_OBJ_FLAG_FLEX_IN_NEW, (const byte*)"\xab\x1c" "ENUM_LV_OBJ_FLAG_FLEX_IN_NEW")
QDEF(MP_QSTR_ENUM_LV_RADIUS, (const byte*)"\xd4\x0e" "ENUM_LV_RADIUS")
QDEF(MP_QSTR_ENUM_LV_SIZE, (const byte*)"\xc9\x0c" "ENUM_LV_SIZE")
QDEF(MP_QSTR_ENUM_LV_TABLE_CELL, (const byte*)"\xeb\x12" "ENUM_LV_TABLE_CELL")
QDEF(MP_QSTR_ENUM_LV_TEXTAREA_CURSOR, (const byte*)"\xf3\x17" "ENUM_LV_TEXTAREA_CURSOR")
QDEF(MP_QSTR_EOPNOTSUPP, (const byte*)"\xac\x0a" "EOPNOTSUPP")
QDEF(MP_QSTR_EPERM, (const byte*)"\xea\x05" "EPERM")
QDEF(MP_QSTR_EPIPE, (const byte*)"\x2c\x05" "EPIPE")
QDEF(MP_QSTR_ERROR, (const byte*)"\x9d\x05" "ERROR")
QDEF(MP_QSTR_ESC, (const byte*)"\x50\x03" "ESC")
QDEF(MP_QSTR_ETIMEDOUT, (const byte*)"\xff\x09" "ETIMEDOUT")
QDEF(MP_QSTR_EVENT, (const byte*)"\x29\x05" "EVENT")
QDEF(MP_QSTR_EVENT_BUBBLE, (const byte*)"\x08\x0c" "EVENT_BUBBLE")
QDEF(MP_QSTR_EXPAND, (const byte*)"\x23\x06" "EXPAND")
QDEF(MP_QSTR_EYE_CLOSE, (const byte*)"\xd5\x09" "EYE_CLOSE")
QDEF(MP_QSTR_EYE_OPEN, (const byte*)"\x57\x08" "EYE_OPEN")
QDEF(MP_QSTR_ExtInt, (const byte*)"\x7f\x06" "ExtInt")
QDEF(MP_QSTR_FADE, (const byte*)"\x83\x04" "FADE")
QDEF(MP_QSTR_FADE_ON, (const byte*)"\x9d\x07" "FADE_ON")
QDEF(MP_QSTR_FALSE, (const byte*)"\x78\x05" "FALSE")
QDEF(MP_QSTR_FAST_MODE, (const byte*)"\x79\x09" "FAST_MODE")
QDEF(MP_QSTR_FILE, (const byte*)"\x83\x04" "FILE")
QDEF(MP_QSTR_FIT, (const byte*)"\x1e\x03" "FIT")
QDEF(MP_QSTR_FIXED, (const byte*)"\xb3\x05" "FIXED")
QDEF(MP_QSTR_FLAG, (const byte*)"\x29\x04" "FLAG")
QDEF(MP_QSTR_FLEX_ALIGN, (const byte*)"\xe0\x0a" "FLEX_ALIGN")
QDEF(MP_QSTR_FLEX_FLOW, (const byte*)"\x1f\x09" "FLEX_FLOW")
QDEF(MP_QSTR_FLOAT32, (const byte*)"\xb4\x07" "FLOAT32")
QDEF(MP_QSTR_FLOAT64, (const byte*)"\x17\x07" "FLOAT64")
QDEF(MP_QSTR_FLOATING, (const byte*)"\xf5\x08" "FLOATING")
QDEF(MP_QSTR_FOCUSED, (const byte*)"\x08\x07" "FOCUSED")
QDEF(MP_QSTR_FOCUS_KEY, (const byte*)"\x61\x09" "FOCUS_KEY")
QDEF(MP_QSTR_FONT_FMT_TXT, (const byte*)"\xf1\x0c" "FONT_FMT_TXT")
QDEF(MP_QSTR_FONT_FMT_TXT_CMAP, (const byte*)"\x51\x11" "FONT_FMT_TXT_CMAP")
QDEF(MP_QSTR_FONT_SUBPX, (const byte*)"\x05\x0a" "FONT_SUBPX")
QDEF(MP_QSTR_FOREGROUND, (const byte*)"\x5e\x0a" "FOREGROUND")
QDEF(MP_QSTR_FORMAT0_FULL, (const byte*)"\x9a\x0c" "FORMAT0_FULL")
QDEF(MP_QSTR_FORMAT0_TINY, (const byte*)"\x83\x0c" "FORMAT0_TINY")
QDEF(MP_QSTR_FS_ERR, (const byte*)"\xaa\x06" "FS_ERR")
QDEF(MP_QSTR_FS_MODE, (const byte*)"\xac\x07" "FS_MODE")
QDEF(MP_QSTR_FS_RES, (const byte*)"\x0b\x06" "FS_RES")
QDEF(MP_QSTR_FS_SEEK, (const byte*)"\x57\x07" "FS_SEEK")
QDEF(MP_QSTR_FULL, (const byte*)"\x96\x04" "FULL")
QDEF(MP_QSTR_FULL_COVER, (const byte*)"\x64\x0a" "FULL_COVER")
QDEF(MP_QSTR_FileIO, (const byte*)"\xc5\x06" "FileIO")
QDEF(MP_QSTR_FrameJumpNum, (const byte*)"\x6c\x0c" "FrameJumpNum")
QDEF(MP_QSTR_GESTURE, (const byte*)"\x02\x07" "GESTURE")
QDEF(MP_QSTR_GESTURE_BUBBLE, (const byte*)"\x43\x0e" "GESTURE_BUBBLE")
QDEF(MP_QSTR_GET_SELF_SIZE, (const byte*)"\xaa\x0d" "GET_SELF_SIZE")
QDEF(MP_QSTR_GPIO0, (const byte*)"\xa4\x05" "GPIO0")
QDEF(MP_QSTR_GPIO1, (const byte*)"\xa5\x05" "GPIO1")
QDEF(MP_QSTR_GPIO10, (const byte*)"\x75\x06" "GPIO10")
QDEF(MP_QSTR_GPIO11, (const byte*)"\x74\x06" "GPIO11")
QDEF(MP_QSTR_GPIO12, (const byte*)"\x77\x06" "GPIO12")
QDEF(MP_QSTR_GPIO13, (const byte*)"\x76\x06" "GPIO13")
QDEF(MP_QSTR_GPIO14, (const byte*)"\x71\x06" "GPIO14")
QDEF(MP_QSTR_GPIO15, (const byte*)"\x70\x06" "GPIO15")
QDEF(MP_QSTR_GPIO16, (const byte*)"\x73\x06" "GPIO16")
QDEF(MP_QSTR_GPIO17, (const byte*)"\x72\x06" "GPIO17")
QDEF(MP_QSTR_GPIO18, (const byte*)"\x7d\x06" "GPIO18")
QDEF(MP_QSTR_GPIO19, (const byte*)"\x7c\x06" "GPIO19")
QDEF(MP_QSTR_GPIO2, (const byte*)"\xa6\x05" "GPIO2")
QDEF(MP_QSTR_GPIO20, (const byte*)"\x56\x06" "GPIO20")
QDEF(MP_QSTR_GPIO21, (const byte*)"\x57\x06" "GPIO21")
QDEF(MP_QSTR_GPIO22, (const byte*)"\x54\x06" "GPIO22")
QDEF(MP_QSTR_GPIO23, (const byte*)"\x55\x06" "GPIO23")
QDEF(MP_QSTR_GPIO24, (const byte*)"\x52\x06" "GPIO24")
QDEF(MP_QSTR_GPIO25, (const byte*)"\x53\x06" "GPIO25")
QDEF(MP_QSTR_GPIO26, (const byte*)"\x50\x06" "GPIO26")
QDEF(MP_QSTR_GPIO27, (const byte*)"\x51\x06" "GPIO27")
QDEF(MP_QSTR_GPIO28, (const byte*)"\x5e\x06" "GPIO28")
QDEF(MP_QSTR_GPIO29, (const byte*)"\x5f\x06" "GPIO29")
QDEF(MP_QSTR_GPIO3, (const byte*)"\xa7\x05" "GPIO3")
QDEF(MP_QSTR_GPIO30, (const byte*)"\xb7\x06" "GPIO30")
QDEF(MP_QSTR_GPIO31, (const byte*)"\xb6\x06" "GPIO31")
QDEF(MP_QSTR_GPIO32, (const byte*)"\xb5\x06" "GPIO32")
QDEF(MP_QSTR_GPIO33, (const byte*)"\xb4\x06" "GPIO33")
QDEF(MP_QSTR_GPIO34, (const byte*)"\xb3\x06" "GPIO34")
QDEF(MP_QSTR_GPIO35, (const byte*)"\xb2\x06" "GPIO35")
QDEF(MP_QSTR_GPIO36, (const byte*)"\xb1\x06" "GPIO36")
QDEF(MP_QSTR_GPIO37, (const byte*)"\xb0\x06" "GPIO37")
QDEF(MP_QSTR_GPIO38, (const byte*)"\xbf\x06" "GPIO38")
QDEF(MP_QSTR_GPIO39, (const byte*)"\xbe\x06" "GPIO39")
QDEF(MP_QSTR_GPIO4, (const byte*)"\xa0\x05" "GPIO4")
QDEF(MP_QSTR_GPIO40, (const byte*)"\x90\x06" "GPIO40")
QDEF(MP_QSTR_GPIO41, (const byte*)"\x91\x06" "GPIO41")
QDEF(MP_QSTR_GPIO42, (const byte*)"\x92\x06" "GPIO42")
QDEF(MP_QSTR_GPIO43, (const byte*)"\x93\x06" "GPIO43")
QDEF(MP_QSTR_GPIO44, (const byte*)"\x94\x06" "GPIO44")
QDEF(MP_QSTR_GPIO45, (const byte*)"\x95\x06" "GPIO45")
QDEF(MP_QSTR_GPIO46, (const byte*)"\x96\x06" "GPIO46")
QDEF(MP_QSTR_GPIO47, (const byte*)"\x97\x06" "GPIO47")
QDEF(MP_QSTR_GPIO5, (const byte*)"\xa1\x05" "GPIO5")
QDEF(MP_QSTR_GPIO6, (const byte*)"\xa2\x05" "GPIO6")
QDEF(MP_QSTR_GPIO7, (const byte*)"\xa3\x05" "GPIO7")
QDEF(MP_QSTR_GPIO8, (const byte*)"\xac\x05" "GPIO8")
QDEF(MP_QSTR_GPIO9, (const byte*)"\xad\x05" "GPIO9")
QDEF(MP_QSTR_GPS, (const byte*)"\xe1\x03" "GPS")
QDEF(MP_QSTR_GRAD_DIR, (const byte*)"\x15\x08" "GRAD_DIR")
QDEF(MP_QSTR_GREEN, (const byte*)"\xde\x05" "GREEN")
QDEF(MP_QSTR_GREY, (const byte*)"\x8c\x04" "GREY")
QDEF(MP_QSTR_GRID, (const byte*)"\x1d\x04" "GRID")
QDEF(MP_QSTR_GRID_ALIGN, (const byte*)"\x8f\x0a" "GRID_ALIGN")
QDEF(MP_QSTR_GRID_TEMPLATE, (const byte*)"\xd2\x0d" "GRID_TEMPLATE")
QDEF(MP_QSTR_GROUP_REFOCUS_POLICY, (const byte*)"\x61\x14" "GROUP_REFOCUS_POLICY")
QDEF(MP_QSTR_HEIGHT, (const byte*)"\xfa\x06" "HEIGHT")
QDEF(MP_QSTR_HIDDEN, (const byte*)"\xef\x06" "HIDDEN")
QDEF(MP_QSTR_HIT_TEST, (const byte*)"\xf9\x08" "HIT_TEST")
QDEF(MP_QSTR_HOME, (const byte*)"\x6a\x04" "HOME")
QDEF(MP_QSTR_HOR, (const byte*)"\x50\x03" "HOR")
QDEF(MP_QSTR_HOVERED, (const byte*)"\x82\x07" "HOVERED")
QDEF(MP_QSTR_HUE, (const byte*)"\x9d\x03" "HUE")
QDEF(MP_QSTR_HW_ERR, (const byte*)"\x60\x06" "HW_ERR")
QDEF(MP_QSTR_I2C, (const byte*)"\x5d\x03" "I2C")
QDEF(MP_QSTR_I2C0, (const byte*)"\xcd\x04" "I2C0")
QDEF(MP_QSTR_I2C2, (const byte*)"\xcf\x04" "I2C2")
QDEF(MP_QSTR_I2C3, (const byte*)"\xce\x04" "I2C3")
QDEF(MP_QSTR_IGNORE_LAYOUT, (const byte*)"\x98\x0d" "IGNORE_LAYOUT")
QDEF(MP_QSTR_IMAGE, (const byte*)"\x62\x05" "IMAGE")
QDEF(MP_QSTR_IMG_OPA, (const byte*)"\xe7\x07" "IMG_OPA")
QDEF(MP_QSTR_IMG_RECOLOR, (const byte*)"\x53\x0b" "IMG_RECOLOR")
QDEF(MP_QSTR_IMG_RECOLOR_OPA, (const byte*)"\x72\x0f" "IMG_RECOLOR_OPA")
QDEF(MP_QSTR_IMG_ZOOM, (const byte*)"\x6e\x08" "IMG_ZOOM")
QDEF(MP_QSTR_IN, (const byte*)"\x22\x02" "IN")
QDEF(MP_QSTR_INDEV_STATE, (const byte*)"\xfd\x0b" "INDEV_STATE")
QDEF(MP_QSTR_INDEV_TYPE, (const byte*)"\x72\x0a" "INDEV_TYPE")
QDEF(MP_QSTR_INDEXED_1BIT, (const byte*)"\x8b\x0c" "INDEXED_1BIT")
QDEF(MP_QSTR_INDEXED_2BIT, (const byte*)"\x68\x0c" "INDEXED_2BIT")
QDEF(MP_QSTR_INDEXED_4BIT, (const byte*)"\x2e\x0c" "INDEXED_4BIT")
QDEF(MP_QSTR_INDEXED_8BIT, (const byte*)"\xa2\x0c" "INDEXED_8BIT")
QDEF(MP_QSTR_INDICATOR, (const byte*)"\x44\x09" "INDICATOR")
QDEF(MP_QSTR_INDICATOR_TYPE, (const byte*)"\xc3\x0e" "INDICATOR_TYPE")
QDEF(MP_QSTR_INDIGO, (const byte*)"\x87\x06" "INDIGO")
QDEF(MP_QSTR_INFINITE, (const byte*)"\xfb\x08" "INFINITE")
QDEF(MP_QSTR_INFO, (const byte*)"\xeb\x04" "INFO")
QDEF(MP_QSTR_INHERIT, (const byte*)"\x60\x07" "INHERIT")
QDEF(MP_QSTR_INSERT, (const byte*)"\xd2\x06" "INSERT")
QDEF(MP_QSTR_INT, (const byte*)"\x36\x03" "INT")
QDEF(MP_QSTR_INT16, (const byte*)"\x91\x05" "INT16")
QDEF(MP_QSTR_INT32, (const byte*)"\x57\x05" "INT32")
QDEF(MP_QSTR_INT64, (const byte*)"\xf4\x05" "INT64")
QDEF(MP_QSTR_INT8, (const byte*)"\xce\x04" "INT8")
QDEF(MP_QSTR_INTERNAL, (const byte*)"\x42\x08" "INTERNAL")
QDEF(MP_QSTR_INV, (const byte*)"\x34\x03" "INV")
QDEF(MP_QSTR_INV_PARAM, (const byte*)"\xc4\x09" "INV_PARAM")
QDEF(MP_QSTR_IOBase, (const byte*)"\x36\x06" "IOBase")
QDEF(MP_QSTR_IPPROTO_IP, (const byte*)"\x0c\x0a" "IPPROTO_IP")
QDEF(MP_QSTR_IPPROTO_TCP, (const byte*)"\xb2\x0b" "IPPROTO_TCP")
QDEF(MP_QSTR_IPPROTO_TCP_SER, (const byte*)"\xa9\x0f" "IPPROTO_TCP_SER")
QDEF(MP_QSTR_IPPROTO_UDP, (const byte*)"\x54\x0b" "IPPROTO_UDP")
QDEF(MP_QSTR_IP_ADD_MEMBERSHIP, (const byte*)"\x6f\x11" "IP_ADD_MEMBERSHIP")
QDEF(MP_QSTR_IRQ_FALLING, (const byte*)"\x37\x0b" "IRQ_FALLING")
QDEF(MP_QSTR_IRQ_RISING, (const byte*)"\x78\x0a" "IRQ_RISING")
QDEF(MP_QSTR_IRQ_RISING_FALLING, (const byte*)"\x60\x12" "IRQ_RISING_FALLING")
QDEF(MP_QSTR_ITEMS, (const byte*)"\x03\x05" "ITEMS")
QDEF(MP_QSTR_Interface, (const byte*)"\xe0\x09" "Interface")
QDEF(MP_QSTR_K26, (const byte*)"\x2a\x03" "K26")
QDEF(MP_QSTR_K9, (const byte*)"\x97\x02" "K9")
QDEF(MP_QSTR_KEY, (const byte*)"\x12\x03" "KEY")
QDEF(MP_QSTR_KEYBOARD, (const byte*)"\xa8\x08" "KEYBOARD")
QDEF(MP_QSTR_KEYPAD, (const byte*)"\x27\x06" "KEYPAD")
QDEF(MP_QSTR_KNOB, (const byte*)"\xcd\x04" "KNOB")
QDEF(MP_QSTR_KNOB_LEFT, (const byte*)"\xa9\x09" "KNOB_LEFT")
QDEF(MP_QSTR_LABEL_DOT, (const byte*)"\xe3\x09" "LABEL_DOT")
QDEF(MP_QSTR_LABEL_POS, (const byte*)"\xf0\x09" "LABEL_POS")
QDEF(MP_QSTR_LABEL_TEXT_SELECTION, (const byte*)"\x9e\x14" "LABEL_TEXT_SELECTION")
QDEF(MP_QSTR_LAST, (const byte*)"\x2f\x04" "LAST")
QDEF(MP_QSTR_LAYOUT, (const byte*)"\x9f\x06" "LAYOUT")
QDEF(MP_QSTR_LAYOUT_1, (const byte*)"\x11\x08" "LAYOUT_1")
QDEF(MP_QSTR_LAYOUT_2, (const byte*)"\x12\x08" "LAYOUT_2")
QDEF(MP_QSTR_LAYOUT_CHANGED, (const byte*)"\x02\x0e" "LAYOUT_CHANGED")
QDEF(MP_QSTR_LAYOUT_FLEX, (const byte*)"\x97\x0b" "LAYOUT_FLEX")
QDEF(MP_QSTR_LAYOUT_GRID, (const byte*)"\x38\x0b" "LAYOUT_GRID")
QDEF(MP_QSTR_LCD, (const byte*)"\xce\x03" "LCD")
QDEF(MP_QSTR_LEAVE, (const byte*)"\x9e\x05" "LEAVE")
QDEF(MP_QSTR_LEFT, (const byte*)"\x9e\x04" "LEFT")
QDEF(MP_QSTR_LEFT_MID, (const byte*)"\x01\x08" "LEFT_MID")
QDEF(MP_QSTR_LIGHT_BLUE, (const byte*)"\x5a\x0a" "LIGHT_BLUE")
QDEF(MP_QSTR_LIGHT_GREEN, (const byte*)"\x5f\x0b" "LIGHT_GREEN")
QDEF(MP_QSTR_LIME, (const byte*)"\xe8\x04" "LIME")
QDEF(MP_QSTR_LINE, (const byte*)"\x8b\x04" "LINE")
QDEF(MP_QSTR_LINE_AND_POINT, (const byte*)"\xec\x0e" "LINE_AND_POINT")
QDEF(MP_QSTR_LINE_COLOR, (const byte*)"\xa9\x0a" "LINE_COLOR")
QDEF(MP_QSTR_LINE_DASH_GAP, (const byte*)"\x83\x0d" "LINE_DASH_GAP")
QDEF(MP_QSTR_LINE_DASH_WIDTH, (const byte*)"\xd3\x0f" "LINE_DASH_WIDTH")
QDEF(MP_QSTR_LINE_OPA, (const byte*)"\xaa\x08" "LINE_OPA")
QDEF(MP_QSTR_LINE_ROUNDED, (const byte*)"\xb7\x0c" "LINE_ROUNDED")
QDEF(MP_QSTR_LINE_WIDTH, (const byte*)"\x12\x0a" "LINE_WIDTH")
QDEF(MP_QSTR_LIST, (const byte*)"\x27\x04" "LIST")
QDEF(MP_QSTR_LITTLE_ENDIAN, (const byte*)"\xbf\x0d" "LITTLE_ENDIAN")
QDEF(MP_QSTR_LOCKED, (const byte*)"\xcf\x06" "LOCKED")
QDEF(MP_QSTR_LOG_LEVEL, (const byte*)"\x68\x09" "LOG_LEVEL")
QDEF(MP_QSTR_LONG, (const byte*)"\x0f\x04" "LONG")
QDEF(MP_QSTR_LONGLONG, (const byte*)"\x85\x08" "LONGLONG")
QDEF(MP_QSTR_LONG_PRESSED, (const byte*)"\xb6\x0c" "LONG_PRESSED")
QDEF(MP_QSTR_LONG_PRESSED_REPEAT, (const byte*)"\x5e\x13" "LONG_PRESSED_REPEAT")
QDEF(MP_QSTR_LOOP, (const byte*)"\x39\x04" "LOOP")
QDEF(MP_QSTR_LTR, (const byte*)"\xaf\x03" "LTR")
QDEF(MP_QSTR_LV_ALIGN, (const byte*)"\xcd\x08" "LV_ALIGN")
QDEF(MP_QSTR_LV_ANIM, (const byte*)"\x2b\x07" "LV_ANIM")
QDEF(MP_QSTR_LV_ANIM_IMG_PART, (const byte*)"\x5f\x10" "LV_ANIM_IMG_PART")
QDEF(MP_QSTR_LV_ARC_DRAW_PART, (const byte*)"\x67\x10" "LV_ARC_DRAW_PART")
QDEF(MP_QSTR_LV_ARC_MODE, (const byte*)"\x4c\x0b" "LV_ARC_MODE")
QDEF(MP_QSTR_LV_BAR_DRAW_PART, (const byte*)"\x46\x10" "LV_BAR_DRAW_PART")
QDEF(MP_QSTR_LV_BAR_MODE, (const byte*)"\x4d\x0b" "LV_BAR_MODE")
QDEF(MP_QSTR_LV_BASE_DIR, (const byte*)"\x95\x0b" "LV_BASE_DIR")
QDEF(MP_QSTR_LV_BLEND_MODE, (const byte*)"\xbd\x0d" "LV_BLEND_MODE")
QDEF(MP_QSTR_LV_BORDER_SIDE, (const byte*)"\x28\x0e" "LV_BORDER_SIDE")
QDEF(MP_QSTR_LV_BTNMATRIX_CTRL, (const byte*)"\xf5\x11" "LV_BTNMATRIX_CTRL")
QDEF(MP_QSTR_LV_BTNMATRIX_DRAW_PART, (const byte*)"\x74\x16" "LV_BTNMATRIX_DRAW_PART")
QDEF(MP_QSTR_LV_CHART_AXIS, (const byte*)"\xb0\x0d" "LV_CHART_AXIS")
QDEF(MP_QSTR_LV_CHART_DRAW_PART, (const byte*)"\xfb\x12" "LV_CHART_DRAW_PART")
QDEF(MP_QSTR_LV_CHART_TYPE, (const byte*)"\x2b\x0d" "LV_CHART_TYPE")
QDEF(MP_QSTR_LV_CHART_UPDATE_MODE, (const byte*)"\xbe\x14" "LV_CHART_UPDATE_MODE")
QDEF(MP_QSTR_LV_CHECKBOX_DRAW_PART, (const byte*)"\xa4\x15" "LV_CHECKBOX_DRAW_PART")
QDEF(MP_QSTR_LV_COLORWHEEL_MODE, (const byte*)"\xd2\x12" "LV_COLORWHEEL_MODE")
QDEF(MP_QSTR_LV_COVER_RES, (const byte*)"\xb6\x0c" "LV_COVER_RES")
QDEF(MP_QSTR_LV_DIR, (const byte*)"\xdf\x06" "LV_DIR")
QDEF(MP_QSTR_LV_DISP_ROT, (const byte*)"\x98\x0b" "LV_DISP_ROT")
QDEF(MP_QSTR_LV_DRAW_MASK_LINE_SIDE, (const byte*)"\x1e\x16" "LV_DRAW_MASK_LINE_SIDE")
QDEF(MP_QSTR_LV_DRAW_MASK_RES, (const byte*)"\xd0\x10" "LV_DRAW_MASK_RES")
QDEF(MP_QSTR_LV_DRAW_MASK_TYPE, (const byte*)"\x2c\x11" "LV_DRAW_MASK_TYPE")
QDEF(MP_QSTR_LV_EVENT, (const byte*)"\x6c\x08" "LV_EVENT")
QDEF(MP_QSTR_LV_FLEX_ALIGN, (const byte*)"\x45\x0d" "LV_FLEX_ALIGN")
QDEF(MP_QSTR_LV_FLEX_FLOW, (const byte*)"\x9a\x0c" "LV_FLEX_FLOW")
QDEF(MP_QSTR_LV_FONT_FMT_TXT, (const byte*)"\x94\x0f" "LV_FONT_FMT_TXT")
QDEF(MP_QSTR_LV_FONT_FMT_TXT_CMAP, (const byte*)"\xd4\x14" "LV_FONT_FMT_TXT_CMAP")
QDEF(MP_QSTR_LV_FONT_SUBPX, (const byte*)"\xa0\x0d" "LV_FONT_SUBPX")
QDEF(MP_QSTR_LV_FS_MODE, (const byte*)"\x29\x0a" "LV_FS_MODE")
QDEF(MP_QSTR_LV_FS_RES, (const byte*)"\xae\x09" "LV_FS_RES")
QDEF(MP_QSTR_LV_FS_SEEK, (const byte*)"\x92\x0a" "LV_FS_SEEK")
QDEF(MP_QSTR_LV_GRAD_DIR, (const byte*)"\x70\x0b" "LV_GRAD_DIR")
QDEF(MP_QSTR_LV_GRID_ALIGN, (const byte*)"\xaa\x0d" "LV_GRID_ALIGN")
QDEF(MP_QSTR_LV_GROUP_REFOCUS_POLICY, (const byte*)"\x04\x17" "LV_GROUP_REFOCUS_POLICY")
QDEF(MP_QSTR_LV_IME_PINYIN_MODE, (const byte*)"\x0b\x12" "LV_IME_PINYIN_MODE")
QDEF(MP_QSTR_LV_IMGBTN_STATE, (const byte*)"\xd3\x0f" "LV_IMGBTN_STATE")
QDEF(MP_QSTR_LV_IMG_CF, (const byte*)"\xd9\x09" "LV_IMG_CF")
QDEF(MP_QSTR_LV_IMG_SIZE_MODE, (const byte*)"\x05\x10" "LV_IMG_SIZE_MODE")
QDEF(MP_QSTR_LV_IMG_SRC, (const byte*)"\x3e\x0a" "LV_IMG_SRC")
QDEF(MP_QSTR_LV_INDEV_STATE, (const byte*)"\xf8\x0e" "LV_INDEV_STATE")
QDEF(MP_QSTR_LV_INDEV_TYPE, (const byte*)"\x57\x0d" "LV_INDEV_TYPE")
QDEF(MP_QSTR_LV_KEY, (const byte*)"\xd7\x06" "LV_KEY")
QDEF(MP_QSTR_LV_KEYBOARD_MODE, (const byte*)"\x51\x10" "LV_KEYBOARD_MODE")
QDEF(MP_QSTR_LV_LABEL_LONG, (const byte*)"\xf3\x0d" "LV_LABEL_LONG")
QDEF(MP_QSTR_LV_LED_DRAW_PART, (const byte*)"\xda\x10" "LV_LED_DRAW_PART")
QDEF(MP_QSTR_LV_METER_DRAW_PART, (const byte*)"\x7c\x12" "LV_METER_DRAW_PART")
QDEF(MP_QSTR_LV_METER_INDICATOR_TYPE, (const byte*)"\x12\x17" "LV_METER_INDICATOR_TYPE")
QDEF(MP_QSTR_LV_OBJ_CLASS_EDITABLE, (const byte*)"\x1f\x15" "LV_OBJ_CLASS_EDITABLE")
QDEF(MP_QSTR_LV_OBJ_CLASS_GROUP_DEF, (const byte*)"\x6e\x16" "LV_OBJ_CLASS_GROUP_DEF")
QDEF(MP_QSTR_LV_OBJ_DRAW_PART, (const byte*)"\xb0\x10" "LV_OBJ_DRAW_PART")
QDEF(MP_QSTR_LV_OBJ_FLAG, (const byte*)"\x34\x0b" "LV_OBJ_FLAG")
QDEF(MP_QSTR_LV_OBJ_TREE_WALK, (const byte*)"\x90\x10" "LV_OBJ_TREE_WALK")
QDEF(MP_QSTR_LV_OPA, (const byte*)"\x7e\x06" "LV_OPA")
QDEF(MP_QSTR_LV_PALETTE, (const byte*)"\xfd\x0a" "LV_PALETTE")
QDEF(MP_QSTR_LV_PART, (const byte*)"\x77\x07" "LV_PART")
QDEF(MP_QSTR_LV_PART_TEXTAREA, (const byte*)"\x82\x10" "LV_PART_TEXTAREA")
QDEF(MP_QSTR_LV_RES, (const byte*)"\x44\x06" "LV_RES")
QDEF(MP_QSTR_LV_ROLLER_MODE, (const byte*)"\xf6\x0e" "LV_ROLLER_MODE")
QDEF(MP_QSTR_LV_SCROLLBAR_MODE, (const byte*)"\xe0\x11" "LV_SCROLLBAR_MODE")
QDEF(MP_QSTR_LV_SCROLL_SNAP, (const byte*)"\x3e\x0e" "LV_SCROLL_SNAP")
QDEF(MP_QSTR_LV_SCR_LOAD_ANIM, (const byte*)"\x4f\x10" "LV_SCR_LOAD_ANIM")
QDEF(MP_QSTR_LV_SLIDER_DRAW_PART, (const byte*)"\x92\x13" "LV_SLIDER_DRAW_PART")
QDEF(MP_QSTR_LV_SLIDER_MODE, (const byte*)"\x99\x0e" "LV_SLIDER_MODE")
QDEF(MP_QSTR_LV_SPAN_MODE, (const byte*)"\x10\x0c" "LV_SPAN_MODE")
QDEF(MP_QSTR_LV_SPAN_OVERFLOW, (const byte*)"\x0f\x10" "LV_SPAN_OVERFLOW")
QDEF(MP_QSTR_LV_STATE, (const byte*)"\x77\x08" "LV_STATE")
QDEF(MP_QSTR_LV_STYLE, (const byte*)"\x77\x08" "LV_STYLE")
QDEF(MP_QSTR_LV_SYMBOL, (const byte*)"\xc6\x09" "LV_SYMBOL")
QDEF(MP_QSTR_LV_TABLE_CELL_CTRL, (const byte*)"\xb1\x12" "LV_TABLE_CELL_CTRL")
QDEF(MP_QSTR_LV_TABLE_DRAW_PART, (const byte*)"\x49\x12" "LV_TABLE_DRAW_PART")
QDEF(MP_QSTR_LV_TEXT_ALIGN, (const byte*)"\xaf\x0d" "LV_TEXT_ALIGN")
QDEF(MP_QSTR_LV_TEXT_CMD_STATE, (const byte*)"\xc0\x11" "LV_TEXT_CMD_STATE")
QDEF(MP_QSTR_LV_TEXT_DECOR, (const byte*)"\x1d\x0d" "LV_TEXT_DECOR")
QDEF(MP_QSTR_LV_TEXT_FLAG, (const byte*)"\x6e\x0c" "LV_TEXT_FLAG")
QDEF(MP_QSTR_LockType, (const byte*)"\x36\x08" "LockType")
QDEF(MP_QSTR_Loop, (const byte*)"\x19\x04" "Loop")
QDEF(MP_QSTR_MAC, (const byte*)"\x8a\x03" "MAC")
QDEF(MP_QSTR_MAIN, (const byte*)"\xce\x04" "MAIN")
QDEF(MP_QSTR_MAP, (const byte*)"\x99\x03" "MAP")
QDEF(MP_QSTR_MASKED, (const byte*)"\x30\x06" "MASKED")
QDEF(MP_QSTR_MAX, (const byte*)"\x91\x03" "MAX")
QDEF(MP_QSTR_MAX_HEIGHT, (const byte*)"\xd1\x0a" "MAX_HEIGHT")
QDEF(MP_QSTR_MAX_WIDTH, (const byte*)"\x88\x09" "MAX_WIDTH")
QDEF(MP_QSTR_MERGE_RIGHT, (const byte*)"\x62\x0b" "MERGE_RIGHT")
QDEF(MP_QSTR_MIN, (const byte*)"\x8f\x03" "MIN")
QDEF(MP_QSTR_MINUS, (const byte*)"\x29\x05" "MINUS")
QDEF(MP_QSTR_MIN_HEIGHT, (const byte*)"\x0f\x0a" "MIN_HEIGHT")
QDEF(MP_QSTR_MIN_WIDTH, (const byte*)"\x16\x09" "MIN_WIDTH")
QDEF(MP_QSTR_MODE, (const byte*)"\x66\x04" "MODE")
QDEF(MP_QSTR_MONO, (const byte*)"\xa6\x04" "MONO")
QDEF(MP_QSTR_MOVE_BOTTOM, (const byte*)"\xc4\x0b" "MOVE_BOTTOM")
QDEF(MP_QSTR_MOVE_LEFT, (const byte*)"\xf0\x09" "MOVE_LEFT")
QDEF(MP_QSTR_MOVE_RIGHT, (const byte*)"\xeb\x0a" "MOVE_RIGHT")
QDEF(MP_QSTR_MOVE_TOP, (const byte*)"\x60\x08" "MOVE_TOP")
QDEF(MP_QSTR_MUTE, (const byte*)"\x6c\x04" "MUTE")
QDEF(MP_QSTR_NATIVE, (const byte*)"\x04\x06" "NATIVE")
QDEF(MP_QSTR_NEEDLE_IMG, (const byte*)"\x7a\x0a" "NEEDLE_IMG")
QDEF(MP_QSTR_NEEDLE_LINE, (const byte*)"\x77\x0b" "NEEDLE_LINE")
QDEF(MP_QSTR_NEUTRAL, (const byte*)"\xf0\x07" "NEUTRAL")
QDEF(MP_QSTR_NEW_LINE, (const byte*)"\x68\x08" "NEW_LINE")
QDEF(MP_QSTR_NEXT, (const byte*)"\x02\x04" "NEXT")
QDEF(MP_QSTR_NOBLOCK, (const byte*)"\x2d\x07" "NOBLOCK")
QDEF(MP_QSTR_NONE, (const byte*)"\x4f\x04" "NONE")
QDEF(MP_QSTR_NORMAL, (const byte*)"\xf6\x06" "NORMAL")
QDEF(MP_QSTR_NOT_COVER, (const byte*)"\xa2\x09" "NOT_COVER")
QDEF(MP_QSTR_NOT_EX, (const byte*)"\x32\x06" "NOT_EX")
QDEF(MP_QSTR_NOT_IMP, (const byte*)"\x5b\x07" "NOT_IMP")
QDEF(MP_QSTR_NO_REPEAT, (const byte*)"\xec\x09" "NO_REPEAT")
QDEF(MP_QSTR_NUM, (const byte*)"\x93\x03" "NUM")
QDEF(MP_QSTR_NUMBER, (const byte*)"\x46\x06" "NUMBER")
QDEF(MP_QSTR_NotImplemented, (const byte*)"\x3e\x0e" "NotImplemented")
QDEF(MP_QSTR_OBJ_FLAG_FLEX_IN_NEW, (const byte*)"\x02\x14" "OBJ_FLAG_FLEX_IN_NEW")
QDEF(MP_QSTR_OFF, (const byte*)"\xaa\x03" "OFF")
QDEF(MP_QSTR_OK, (const byte*)"\x61\x02" "OK")
QDEF(MP_QSTR_ON, (const byte*)"\x64\x02" "ON")
QDEF(MP_QSTR_ONE_SHOT, (const byte*)"\x5e\x08" "ONE_SHOT")
QDEF(MP_QSTR_OPA, (const byte*)"\xfb\x03" "OPA")
QDEF(MP_QSTR_ORANGE, (const byte*)"\xf5\x06" "ORANGE")
QDEF(MP_QSTR_OUT, (const byte*)"\x0b\x03" "OUT")
QDEF(MP_QSTR_OUTLINE_COLOR, (const byte*)"\xa7\x0d" "OUTLINE_COLOR")
QDEF(MP_QSTR_OUTLINE_OPA, (const byte*)"\xa4\x0b" "OUTLINE_OPA")
QDEF(MP_QSTR_OUTLINE_PAD, (const byte*)"\x0f\x0b" "OUTLINE_PAD")
QDEF(MP_QSTR_OUTLINE_WIDTH, (const byte*)"\x1c\x0d" "OUTLINE_WIDTH")
QDEF(MP_QSTR_OUT_BOTTOM_LEFT, (const byte*)"\x3f\x0f" "OUT_BOTTOM_LEFT")
QDEF(MP_QSTR_OUT_BOTTOM_MID, (const byte*)"\x04\x0e" "OUT_BOTTOM_MID")
QDEF(MP_QSTR_OUT_BOTTOM_RIGHT, (const byte*)"\x84\x10" "OUT_BOTTOM_RIGHT")
QDEF(MP_QSTR_OUT_LEFT_BOTTOM, (const byte*)"\xbf\x0f" "OUT_LEFT_BOTTOM")
QDEF(MP_QSTR_OUT_LEFT_MID, (const byte*)"\x30\x0c" "OUT_LEFT_MID")
QDEF(MP_QSTR_OUT_LEFT_TOP, (const byte*)"\x7b\x0c" "OUT_LEFT_TOP")
QDEF(MP_QSTR_OUT_OF_MEM, (const byte*)"\x07\x0a" "OUT_OF_MEM")
QDEF(MP_QSTR_OUT_RIGHT_BOTTOM, (const byte*)"\xa4\x10" "OUT_RIGHT_BOTTOM")
QDEF(MP_QSTR_OUT_RIGHT_MID, (const byte*)"\x8b\x0d" "OUT_RIGHT_MID")
QDEF(MP_QSTR_OUT_RIGHT_TOP, (const byte*)"\xc0\x0d" "OUT_RIGHT_TOP")
QDEF(MP_QSTR_OUT_TOP_LEFT, (const byte*)"\xfb\x0c" "OUT_TOP_LEFT")
QDEF(MP_QSTR_OUT_TOP_MID, (const byte*)"\xc0\x0b" "OUT_TOP_MID")
QDEF(MP_QSTR_OUT_TOP_RIGHT, (const byte*)"\x40\x0d" "OUT_TOP_RIGHT")
QDEF(MP_QSTR_OVER_BOTTOM, (const byte*)"\x9b\x0b" "OVER_BOTTOM")
QDEF(MP_QSTR_OVER_LEFT, (const byte*)"\xaf\x09" "OVER_LEFT")
QDEF(MP_QSTR_OVER_RIGHT, (const byte*)"\x14\x0a" "OVER_RIGHT")
QDEF(MP_QSTR_OVER_TOP, (const byte*)"\x5f\x08" "OVER_TOP")
QDEF(MP_QSTR_PAD_BOTTOM, (const byte*)"\xa0\x0a" "PAD_BOTTOM")
QDEF(MP_QSTR_PAD_COLUMN, (const byte*)"\x99\x0a" "PAD_COLUMN")
QDEF(MP_QSTR_PAD_LEFT, (const byte*)"\x54\x08" "PAD_LEFT")
QDEF(MP_QSTR_PAD_RIGHT, (const byte*)"\x4f\x09" "PAD_RIGHT")
QDEF(MP_QSTR_PAD_ROW, (const byte*)"\xc5\x07" "PAD_ROW")
QDEF(MP_QSTR_PAD_TOP, (const byte*)"\x44\x07" "PAD_TOP")
QDEF(MP_QSTR_PALETTE, (const byte*)"\x78\x07" "PALETTE")
QDEF(MP_QSTR_PAR, (const byte*)"\x06\x03" "PAR")
QDEF(MP_QSTR_PART, (const byte*)"\x92\x04" "PART")
QDEF(MP_QSTR_PART_TEXTAREA, (const byte*)"\x87\x0d" "PART_TEXTAREA")
QDEF(MP_QSTR_PASTE, (const byte*)"\x56\x05" "PASTE")
QDEF(MP_QSTR_PAUSE, (const byte*)"\xf7\x05" "PAUSE")
QDEF(MP_QSTR_PCM, (const byte*)"\xdb\x03" "PCM")
QDEF(MP_QSTR_PERIODIC, (const byte*)"\x0a\x08" "PERIODIC")
QDEF(MP_QSTR_PINK, (const byte*)"\x19\x04" "PINK")
QDEF(MP_QSTR_PINYIN_MODE, (const byte*)"\xd0\x0b" "PINYIN_MODE")
QDEF(MP_QSTR_PLACEHOLDER, (const byte*)"\xe6\x0b" "PLACEHOLDER")
QDEF(MP_QSTR_PLAIN, (const byte*)"\x5f\x05" "PLAIN")
QDEF(MP_QSTR_PLAY, (const byte*)"\xe1\x04" "PLAY")
QDEF(MP_QSTR_PLUS, (const byte*)"\x7f\x04" "PLUS")
QDEF(MP_QSTR_POINTER, (const byte*)"\x7e\x07" "POINTER")
QDEF(MP_QSTR_POLLERR, (const byte*)"\xdf\x07" "POLLERR")
QDEF(MP_QSTR_POLLHUP, (const byte*)"\x77\x07" "POLLHUP")
QDEF(MP_QSTR_POLLIN, (const byte*)"\x7d\x06" "POLLIN")
QDEF(MP_QSTR_POLLOUT, (const byte*)"\x74\x07" "POLLOUT")
QDEF(MP_QSTR_POWER, (const byte*)"\xfa\x05" "POWER")
QDEF(MP_QSTR_PRESSED, (const byte*)"\x63\x07" "PRESSED")
QDEF(MP_QSTR_PRESSING, (const byte*)"\xc2\x08" "PRESSING")
QDEF(MP_QSTR_PRESS_LOCK, (const byte*)"\xf6\x0a" "PRESS_LOCK")
QDEF(MP_QSTR_PRESS_LOST, (const byte*)"\xf9\x0a" "PRESS_LOST")
QDEF(MP_QSTR_PREV, (const byte*)"\x74\x04" "PREV")
QDEF(MP_QSTR_PRIMARY_X, (const byte*)"\xae\x09" "PRIMARY_X")
QDEF(MP_QSTR_PRIMARY_Y, (const byte*)"\xaf\x09" "PRIMARY_Y")
QDEF(MP_QSTR_PROP_ANY, (const byte*)"\x51\x08" "PROP_ANY")
QDEF(MP_QSTR_PROP_INV, (const byte*)"\x56\x08" "PROP_INV")
QDEF(MP_QSTR_PTR, (const byte*)"\xb3\x03" "PTR")
QDEF(MP_QSTR_PULL_DISABLE, (const byte*)"\x6b\x0c" "PULL_DISABLE")
QDEF(MP_QSTR_PULL_PD, (const byte*)"\xcb\x07" "PULL_PD")
QDEF(MP_QSTR_PULL_PU, (const byte*)"\xda\x07" "PULL_PU")
QDEF(MP_QSTR_PURPLE, (const byte*)"\x0b\x06" "PURPLE")
QDEF(MP_QSTR_Pin, (const byte*)"\x12\x03" "Pin")
QDEF(MP_QSTR_PinBase, (const byte*)"\x47\x07" "PinBase")
QDEF(MP_QSTR_Power, (const byte*)"\xfa\x05" "Power")
QDEF(MP_QSTR_PowerKey, (const byte*)"\x0d\x08" "PowerKey")
QDEF(MP_QSTR_RADIUS, (const byte*)"\xbd\x06" "RADIUS")
QDEF(MP_QSTR_RANGE, (const byte*)"\x7a\x05" "RANGE")
QDEF(MP_QSTR_RAW, (const byte*)"\x01\x03" "RAW")
QDEF(MP_QSTR_RAW_ALPHA, (const byte*)"\x6a\x09" "RAW_ALPHA")
QDEF(MP_QSTR_RAW_CHROMA_KEYED, (const byte*)"\x0d\x10" "RAW_CHROMA_KEYED")
QDEF(MP_QSTR_RD, (const byte*)"\x93\x02" "RD")
QDEF(MP_QSTR_READONLY, (const byte*)"\x03\x08" "READONLY")
QDEF(MP_QSTR_READY, (const byte*)"\xce\x05" "READY")
QDEF(MP_QSTR_REAL, (const byte*)"\xbf\x04" "REAL")
QDEF(MP_QSTR_RECOLOR, (const byte*)"\x4f\x07" "RECOLOR")
QDEF(MP_QSTR_RECTANGLE, (const byte*)"\x44\x09" "RECTANGLE")
QDEF(MP_QSTR_RED, (const byte*)"\x96\x03" "RED")
QDEF(MP_QSTR_REFRESH, (const byte*)"\xb8\x07" "REFRESH")
QDEF(MP_QSTR_REFR_EXT_DRAW_SIZE, (const byte*)"\x15\x12" "REFR_EXT_DRAW_SIZE")
QDEF(MP_QSTR_RELEASED, (const byte*)"\xc8\x08" "RELEASED")
QDEF(MP_QSTR_REPL_UART, (const byte*)"\xa3\x09" "REPL_UART")
QDEF(MP_QSTR_RES, (const byte*)"\x81\x03" "RES")
QDEF(MP_QSTR_RESERVED_15, (const byte*)"\x1a\x0b" "RESERVED_15")
QDEF(MP_QSTR_RESERVED_16, (const byte*)"\x19\x0b" "RESERVED_16")
QDEF(MP_QSTR_RESERVED_17, (const byte*)"\x18\x0b" "RESERVED_17")
QDEF(MP_QSTR_RESERVED_18, (const byte*)"\x17\x0b" "RESERVED_18")
QDEF(MP_QSTR_RESERVED_19, (const byte*)"\x16\x0b" "RESERVED_19")
QDEF(MP_QSTR_RESERVED_20, (const byte*)"\xfc\x0b" "RESERVED_20")
QDEF(MP_QSTR_RESERVED_21, (const byte*)"\xfd\x0b" "RESERVED_21")
QDEF(MP_QSTR_RESERVED_22, (const byte*)"\xfe\x0b" "RESERVED_22")
QDEF(MP_QSTR_RESERVED_23, (const byte*)"\xff\x0b" "RESERVED_23")
QDEF(MP_QSTR_REVERSE, (const byte*)"\x85\x07" "REVERSE")
QDEF(MP_QSTR_RIGHT, (const byte*)"\xc5\x05" "RIGHT")
QDEF(MP_QSTR_RIGHT_MID, (const byte*)"\x5a\x09" "RIGHT_MID")
QDEF(MP_QSTR_ROW, (const byte*)"\xcf\x03" "ROW")
QDEF(MP_QSTR_ROW_REVERSE, (const byte*)"\x50\x0b" "ROW_REVERSE")
QDEF(MP_QSTR_ROW_WRAP, (const byte*)"\xe4\x08" "ROW_WRAP")
QDEF(MP_QSTR_ROW_WRAP_REVERSE, (const byte*)"\x7b\x10" "ROW_WRAP_REVERSE")
QDEF(MP_QSTR_RTC, (const byte*)"\xa0\x03" "RTC")
QDEF(MP_QSTR_RTL, (const byte*)"\xaf\x03" "RTL")
QDEF(MP_QSTR_Read, (const byte*)"\x97\x04" "Read")
QDEF(MP_QSTR_SATURATION, (const byte*)"\x99\x0a" "SATURATION")
QDEF(MP_QSTR_SAVE, (const byte*)"\xe4\x04" "SAVE")
QDEF(MP_QSTR_SCALE_LINES, (const byte*)"\xff\x0b" "SCALE_LINES")
QDEF(MP_QSTR_SCATTER, (const byte*)"\xe3\x07" "SCATTER")
QDEF(MP_QSTR_SCROLL, (const byte*)"\x68\x06" "SCROLL")
QDEF(MP_QSTR_SCROLLABLE, (const byte*)"\x42\x0a" "SCROLLABLE")
QDEF(MP_QSTR_SCROLLBAR, (const byte*)"\xd9\x09" "SCROLLBAR")
QDEF(MP_QSTR_SCROLLBAR_MODE, (const byte*)"\x45\x0e" "SCROLLBAR_MODE")
QDEF(MP_QSTR_SCROLLED, (const byte*)"\x89\x08" "SCROLLED")
QDEF(MP_QSTR_SCROLL_BEGIN, (const byte*)"\x10\x0c" "SCROLL_BEGIN")
QDEF(MP_QSTR_SCROLL_CHAIN, (const byte*)"\x7a\x0c" "SCROLL_CHAIN")
QDEF(MP_QSTR_SCROLL_CIRCULAR, (const byte*)"\xc6\x0f" "SCROLL_CIRCULAR")
QDEF(MP_QSTR_SCROLL_ELASTIC, (const byte*)"\xd2\x0e" "SCROLL_ELASTIC")
QDEF(MP_QSTR_SCROLL_END, (const byte*)"\x18\x0a" "SCROLL_END")
QDEF(MP_QSTR_SCROLL_MOMENTUM, (const byte*)"\x9f\x0f" "SCROLL_MOMENTUM")
QDEF(MP_QSTR_SCROLL_ONE, (const byte*)"\x93\x0a" "SCROLL_ONE")
QDEF(MP_QSTR_SCROLL_ON_FOCUS, (const byte*)"\x65\x0f" "SCROLL_ON_FOCUS")
QDEF(MP_QSTR_SCROLL_SNAP, (const byte*)"\xbb\x0b" "SCROLL_SNAP")
QDEF(MP_QSTR_SCR_LOAD_ANIM, (const byte*)"\xca\x0d" "SCR_LOAD_ANIM")
QDEF(MP_QSTR_SD_CARD, (const byte*)"\x59\x07" "SD_CARD")
QDEF(MP_QSTR_SECONDARY_X, (const byte*)"\xd8\x0b" "SECONDARY_X")
QDEF(MP_QSTR_SECONDARY_Y, (const byte*)"\xd9\x0b" "SECONDARY_Y")
QDEF(MP_QSTR_SELECTED, (const byte*)"\x2c\x08" "SELECTED")
QDEF(MP_QSTR_SET, (const byte*)"\x07\x03" "SET")
QDEF(MP_QSTR_SETTINGS, (const byte*)"\x20\x08" "SETTINGS")
QDEF(MP_QSTR_SHADOW_COLOR, (const byte*)"\x01\x0c" "SHADOW_COLOR")
QDEF(MP_QSTR_SHADOW_OFS_X, (const byte*)"\xa1\x0c" "SHADOW_OFS_X")
QDEF(MP_QSTR_SHADOW_OFS_Y, (const byte*)"\xa0\x0c" "SHADOW_OFS_Y")
QDEF(MP_QSTR_SHADOW_OPA, (const byte*)"\x02\x0a" "SHADOW_OPA")
QDEF(MP_QSTR_SHADOW_SPREAD, (const byte*)"\x8d\x0d" "SHADOW_SPREAD")
QDEF(MP_QSTR_SHADOW_WIDTH, (const byte*)"\xfa\x0c" "SHADOW_WIDTH")
QDEF(MP_QSTR_SHIFT, (const byte*)"\xe5\x05" "SHIFT")
QDEF(MP_QSTR_SHORT, (const byte*)"\xf7\x05" "SHORT")
QDEF(MP_QSTR_SHORT_CLICKED, (const byte*)"\x87\x0d" "SHORT_CLICKED")
QDEF(MP_QSTR_SHUFFLE, (const byte*)"\xa2\x07" "SHUFFLE")
QDEF(MP_QSTR_SIP_ERR_CREATE, (const byte*)"\x8e\x0e" "SIP_ERR_CREATE")
QDEF(MP_QSTR_SIP_ERR_INVALID_INPUT, (const byte*)"\x12\x15" "SIP_ERR_INVALID_INPUT")
QDEF(MP_QSTR_SIP_ERR_NOT_REGISTERED, (const byte*)"\x08\x16" "SIP_ERR_NOT_REGISTERED")
QDEF(MP_QSTR_SIP_ERR_NOT_SUPPORTED, (const byte*)"\x4e\x15" "SIP_ERR_NOT_SUPPORTED")
QDEF(MP_QSTR_SIP_ERR_PARAMETER_INVALID, (const byte*)"\x6d\x19" "SIP_ERR_PARAMETER_INVALID")
QDEF(MP_QSTR_SIP_ERR_REGISTERED, (const byte*)"\x82\x12" "SIP_ERR_REGISTERED")
QDEF(MP_QSTR_SIP_ERR_SUCCESS, (const byte*)"\xa9\x0f" "SIP_ERR_SUCCESS")
QDEF(MP_QSTR_SIP_ERR_THREAD_CREATION, (const byte*)"\x52\x17" "SIP_ERR_THREAD_CREATION")
QDEF(MP_QSTR_SIP_ERR_TIMEOUT, (const byte*)"\xb1\x0f" "SIP_ERR_TIMEOUT")
QDEF(MP_QSTR_SIP_ERR_UNKNOWN_ERROR, (const byte*)"\x65\x15" "SIP_ERR_UNKNOWN_ERROR")
QDEF(MP_QSTR_SIP_NO_REGISTERED, (const byte*)"\x06\x11" "SIP_NO_REGISTERED")
QDEF(MP_QSTR_SIP_REGISTERED, (const byte*)"\x78\x0e" "SIP_REGISTERED")
QDEF(MP_QSTR_SIZE, (const byte*)"\x60\x04" "SIZE")
QDEF(MP_QSTR_SIZE_CHANGED, (const byte*)"\xfd\x0c" "SIZE_CHANGED")
QDEF(MP_QSTR_SIZE_MODE, (const byte*)"\x1c\x09" "SIZE_MODE")
QDEF(MP_QSTR_SKIP_CHILDREN, (const byte*)"\xa8\x0d" "SKIP_CHILDREN")
QDEF(MP_QSTR_SNAPPABLE, (const byte*)"\x93\x09" "SNAPPABLE")
QDEF(MP_QSTR_SOCK_DGRAM, (const byte*)"\xb3\x0a" "SOCK_DGRAM")
QDEF(MP_QSTR_SOCK_RAW, (const byte*)"\xca\x08" "SOCK_RAW")
QDEF(MP_QSTR_SOCK_STREAM, (const byte*)"\x32\x0b" "SOCK_STREAM")
QDEF(MP_QSTR_SOL_SOCKET, (const byte*)"\x0f\x0a" "SOL_SOCKET")
QDEF(MP_QSTR_SO_ACCEPTCONN, (const byte*)"\xea\x0d" "SO_ACCEPTCONN")
QDEF(MP_QSTR_SO_REUSEADDR, (const byte*)"\x21\x0c" "SO_REUSEADDR")
QDEF(MP_QSTR_SPACE_AROUND, (const byte*)"\x3d\x0c" "SPACE_AROUND")
QDEF(MP_QSTR_SPACE_BETWEEN, (const byte*)"\xd4\x0d" "SPACE_BETWEEN")
QDEF(MP_QSTR_SPACE_EVENLY, (const byte*)"\x73\x0c" "SPACE_EVENLY")
QDEF(MP_QSTR_SPAN_MODE, (const byte*)"\x95\x09" "SPAN_MODE")
QDEF(MP_QSTR_SPAN_OVERFLOW, (const byte*)"\x8a\x0d" "SPAN_OVERFLOW")
QDEF(MP_QSTR_SPARSE_FULL, (const byte*)"\x0f\x0b" "SPARSE_FULL")
QDEF(MP_QSTR_SPARSE_TINY, (const byte*)"\x16\x0b" "SPARSE_TINY")
QDEF(MP_QSTR_SPECIAL, (const byte*)"\x64\x07" "SPECIAL")
QDEF(MP_QSTR_SPI, (const byte*)"\xef\x03" "SPI")
QDEF(MP_QSTR_SPI0, (const byte*)"\xff\x04" "SPI0")
QDEF(MP_QSTR_SPI1, (const byte*)"\xfe\x04" "SPI1")
QDEF(MP_QSTR_SPICS, (const byte*)"\x5f\x05" "SPICS")
QDEF(MP_QSTR_SPIDC, (const byte*)"\xa8\x05" "SPIDC")
QDEF(MP_QSTR_SPIMode, (const byte*)"\x2c\x07" "SPIMode")
QDEF(MP_QSTR_SPIPort, (const byte*)"\xd6\x07" "SPIPort")
QDEF(MP_QSTR_SPIRST, (const byte*)"\x7a\x06" "SPIRST")
QDEF(MP_QSTR_SRC, (const byte*)"\x27\x03" "SRC")
QDEF(MP_QSTR_STANDARD_MODE, (const byte*)"\x62\x0d" "STANDARD_MODE")
QDEF(MP_QSTR_START, (const byte*)"\xe5\x05" "START")
QDEF(MP_QSTR_STATE, (const byte*)"\x32\x05" "STATE")
QDEF(MP_QSTR_STEREO, (const byte*)"\xbf\x06" "STEREO")
QDEF(MP_QSTR_STOP, (const byte*)"\x5d\x04" "STOP")
QDEF(MP_QSTR_STRETCH, (const byte*)"\x6a\x07" "STRETCH")
QDEF(MP_QSTR_STRIKETHROUGH, (const byte*)"\x2c\x0d" "STRIKETHROUGH")
QDEF(MP_QSTR_STYLE, (const byte*)"\x32\x05" "STYLE")
QDEF(MP_QSTR_STYLE_CHANGED, (const byte*)"\xaf\x0d" "STYLE_CHANGED")
QDEF(MP_QSTR_STYLE_FLEX_CROSS_PLACE, (const byte*)"\x1f\x16" "STYLE_FLEX_CROSS_PLACE")
QDEF(MP_QSTR_STYLE_FLEX_FLOW, (const byte*)"\xb7\x0f" "STYLE_FLEX_FLOW")
QDEF(MP_QSTR_STYLE_FLEX_GROW, (const byte*)"\x08\x0f" "STYLE_FLEX_GROW")
QDEF(MP_QSTR_STYLE_FLEX_MAIN_PLACE, (const byte*)"\x8a\x15" "STYLE_FLEX_MAIN_PLACE")
QDEF(MP_QSTR_STYLE_FLEX_TRACK_PLACE, (const byte*)"\x0e\x16" "STYLE_FLEX_TRACK_PLACE")
QDEF(MP_QSTR_STYLE_GRID_CELL_COLUMN_POS, (const byte*)"\xb6\x1a" "STYLE_GRID_CELL_COLUMN_POS")
QDEF(MP_QSTR_STYLE_GRID_CELL_COLUMN_SPAN, (const byte*)"\xd6\x1b" "STYLE_GRID_CELL_COLUMN_SPAN")
QDEF(MP_QSTR_STYLE_GRID_CELL_ROW_POS, (const byte*)"\x6a\x17" "STYLE_GRID_CELL_ROW_POS")
QDEF(MP_QSTR_STYLE_GRID_CELL_ROW_SPAN, (const byte*)"\x8a\x18" "STYLE_GRID_CELL_ROW_SPAN")
QDEF(MP_QSTR_STYLE_GRID_CELL_X_ALIGN, (const byte*)"\x59\x17" "STYLE_GRID_CELL_X_ALIGN")
QDEF(MP_QSTR_STYLE_GRID_CELL_Y_ALIGN, (const byte*)"\x98\x17" "STYLE_GRID_CELL_Y_ALIGN")
QDEF(MP_QSTR_STYLE_GRID_COLUMN_ALIGN, (const byte*)"\x4e\x17" "STYLE_GRID_COLUMN_ALIGN")
QDEF(MP_QSTR_STYLE_GRID_COLUMN_DSC_ARRAY, (const byte*)"\x51\x1b" "STYLE_GRID_COLUMN_DSC_ARRAY")
QDEF(MP_QSTR_STYLE_GRID_ROW_ALIGN, (const byte*)"\xb2\x14" "STYLE_GRID_ROW_ALIGN")
QDEF(MP_QSTR_STYLE_GRID_ROW_DSC_ARRAY, (const byte*)"\x2d\x18" "STYLE_GRID_ROW_DSC_ARRAY")
QDEF(MP_QSTR_SUBTRACTIVE, (const byte*)"\x6b\x0b" "SUBTRACTIVE")
QDEF(MP_QSTR_SYMBOL, (const byte*)"\xe3\x06" "SYMBOL")
QDEF(MP_QSTR_SYMMETRICAL, (const byte*)"\x6b\x0b" "SYMMETRICAL")
QDEF(MP_QSTR_SecureData, (const byte*)"\xc2\x0a" "SecureData")
QDEF(MP_QSTR_Signal, (const byte*)"\x9b\x06" "Signal")
QDEF(MP_QSTR_SoftReset, (const byte*)"\x7e\x09" "SoftReset")
QDEF(MP_QSTR_StopAsyncIteration, (const byte*)"\xec\x12" "StopAsyncIteration")
QDEF(MP_QSTR_Store, (const byte*)"\x3a\x05" "Store")
QDEF(MP_QSTR_StringIO, (const byte*)"\x76\x08" "StringIO")
QDEF(MP_QSTR_Struct, (const byte*)"\xf2\x06" "Struct")
QDEF(MP_QSTR_TABLE_CELL, (const byte*)"\x82\x0a" "TABLE_CELL")
QDEF(MP_QSTR_TCP_CUSTOMIZE_PORT, (const byte*)"\x3e\x12" "TCP_CUSTOMIZE_PORT")
QDEF(MP_QSTR_TCP_KEEPALIVE, (const byte*)"\xd1\x0d" "TCP_KEEPALIVE")
QDEF(MP_QSTR_TEAL, (const byte*)"\x79\x04" "TEAL")
QDEF(MP_QSTR_TEPin, (const byte*)"\x63\x05" "TEPin")
QDEF(MP_QSTR_TESel, (const byte*)"\x2e\x05" "TESel")
QDEF(MP_QSTR_TEXTAREA_CURSOR, (const byte*)"\x3a\x0f" "TEXTAREA_CURSOR")
QDEF(MP_QSTR_TEXT_ALIGN, (const byte*)"\x0a\x0a" "TEXT_ALIGN")
QDEF(MP_QSTR_TEXT_CMD_STATE, (const byte*)"\x65\x0e" "TEXT_CMD_STATE")
QDEF(MP_QSTR_TEXT_COLOR, (const byte*)"\x7a\x0a" "TEXT_COLOR")
QDEF(MP_QSTR_TEXT_CROP, (const byte*)"\xc9\x09" "TEXT_CROP")
QDEF(MP_QSTR_TEXT_DECOR, (const byte*)"\xb8\x0a" "TEXT_DECOR")
QDEF(MP_QSTR_TEXT_FLAG, (const byte*)"\xab\x09" "TEXT_FLAG")
QDEF(MP_QSTR_TEXT_FONT, (const byte*)"\x54\x09" "TEXT_FONT")
QDEF(MP_QSTR_TEXT_LETTER_SPACE, (const byte*)"\x62\x11" "TEXT_LETTER_SPACE")
QDEF(MP_QSTR_TEXT_LINE_SPACE, (const byte*)"\x12\x0f" "TEXT_LINE_SPACE")
QDEF(MP_QSTR_TEXT_LOWER, (const byte*)"\x24\x0a" "TEXT_LOWER")
QDEF(MP_QSTR_TEXT_OPA, (const byte*)"\xb9\x08" "TEXT_OPA")
QDEF(MP_QSTR_TEXT_UPPER, (const byte*)"\xc5\x0a" "TEXT_UPPER")
QDEF(MP_QSTR_TICK, (const byte*)"\x30\x04" "TICK")
QDEF(MP_QSTR_TICKS, (const byte*)"\x63\x05" "TICKS")
QDEF(MP_QSTR_TICK_LABEL, (const byte*)"\x49\x0a" "TICK_LABEL")
QDEF(MP_QSTR_TOP, (const byte*)"\x4e\x03" "TOP")
QDEF(MP_QSTR_TOP_LEFT, (const byte*)"\xca\x08" "TOP_LEFT")
QDEF(MP_QSTR_TOP_MID, (const byte*)"\xd1\x07" "TOP_MID")
QDEF(MP_QSTR_TOP_RIGHT, (const byte*)"\x11\x09" "TOP_RIGHT")
QDEF(MP_QSTR_TOUT, (const byte*)"\xff\x04" "TOUT")
QDEF(MP_QSTR_TRACE, (const byte*)"\xc4\x05" "TRACE")
QDEF(MP_QSTR_TRACK, (const byte*)"\xca\x05" "TRACK")
QDEF(MP_QSTR_TRANSFORM_ANGLE, (const byte*)"\x57\x0f" "TRANSFORM_ANGLE")
QDEF(MP_QSTR_TRANSFORM_HEIGHT, (const byte*)"\xa9\x10" "TRANSFORM_HEIGHT")
QDEF(MP_QSTR_TRANSFORM_WIDTH, (const byte*)"\xf0\x0f" "TRANSFORM_WIDTH")
QDEF(MP_QSTR_TRANSFORM_ZOOM, (const byte*)"\x61\x0e" "TRANSFORM_ZOOM")
QDEF(MP_QSTR_TRANSITION, (const byte*)"\x2a\x0a" "TRANSITION")
QDEF(MP_QSTR_TRANSLATE_X, (const byte*)"\xc4\x0b" "TRANSLATE_X")
QDEF(MP_QSTR_TRANSLATE_Y, (const byte*)"\xc5\x0b" "TRANSLATE_Y")
QDEF(MP_QSTR_TRANSP, (const byte*)"\xcf\x06" "TRANSP")
QDEF(MP_QSTR_TRASH, (const byte*)"\xd9\x05" "TRASH")
QDEF(MP_QSTR_TREE_WALK, (const byte*)"\xed\x09" "TREE_WALK")
QDEF(MP_QSTR_TRUE, (const byte*)"\xf3\x04" "TRUE")
QDEF(MP_QSTR_TRUE_COLOR, (const byte*)"\x91\x0a" "TRUE_COLOR")
QDEF(MP_QSTR_TRUE_COLOR_ALPHA, (const byte*)"\xfa\x10" "TRUE_COLOR_ALPHA")
QDEF(MP_QSTR_TRUE_COLOR_CHROMA_KEYED, (const byte*)"\x9d\x17" "TRUE_COLOR_CHROMA_KEYED")
QDEF(MP_QSTR_TYPE, (const byte*)"\x5d\x04" "TYPE")
QDEF(MP_QSTR_Task, (const byte*)"\x08\x04" "Task")
QDEF(MP_QSTR_TaskQueue, (const byte*)"\x99\x09" "TaskQueue")
QDEF(MP_QSTR_TextIOWrapper, (const byte*)"\xad\x0d" "TextIOWrapper")
QDEF(MP_QSTR_Timer, (const byte*)"\xa2\x05" "Timer")
QDEF(MP_QSTR_Timer0, (const byte*)"\xd2\x06" "Timer0")
QDEF(MP_QSTR_Timer1, (const byte*)"\xd3\x06" "Timer1")
QDEF(MP_QSTR_Timer2, (const byte*)"\xd0\x06" "Timer2")
QDEF(MP_QSTR_Timer3, (const byte*)"\xd1\x06" "Timer3")
QDEF(MP_QSTR_Type_ECM, (const byte*)"\x29\x08" "Type_ECM")
QDEF(MP_QSTR_Type_RNDIS, (const byte*)"\x20\x0a" "Type_RNDIS")
QDEF(MP_QSTR_UART, (const byte*)"\xb7\x04" "UART")
QDEF(MP_QSTR_UART0, (const byte*)"\xa7\x05" "UART0")
QDEF(MP_QSTR_UART1, (const byte*)"\xa6\x05" "UART1")
QDEF(MP_QSTR_UART2, (const byte*)"\xa5\x05" "UART2")
QDEF(MP_QSTR_UART3, (const byte*)"\xa4\x05" "UART3")
QDEF(MP_QSTR_UINT, (const byte*)"\x23\x04" "UINT")
QDEF(MP_QSTR_UINT16, (const byte*)"\xc4\x06" "UINT16")
QDEF(MP_QSTR_UINT32, (const byte*)"\x82\x06" "UINT32")
QDEF(MP_QSTR_UINT64, (const byte*)"\x61\x06" "UINT64")
QDEF(MP_QSTR_UINT8, (const byte*)"\xbb\x05" "UINT8")
QDEF(MP_QSTR_ULONG, (const byte*)"\x7a\x05" "ULONG")
QDEF(MP_QSTR_ULONGLONG, (const byte*)"\x70\x09" "ULONGLONG")
QDEF(MP_QSTR_UNDERLINE, (const byte*)"\xc3\x09" "UNDERLINE")
QDEF(MP_QSTR_UNKNOWN, (const byte*)"\x8d\x07" "UNKNOWN")
QDEF(MP_QSTR_UP, (const byte*)"\xa0\x02" "UP")
QDEF(MP_QSTR_UPDATE_MODE, (const byte*)"\x08\x0b" "UPDATE_MODE")
QDEF(MP_QSTR_UPLOAD, (const byte*)"\x46\x06" "UPLOAD")
QDEF(MP_QSTR_USB, (const byte*)"\x41\x03" "USB")
QDEF(MP_QSTR_USBNET, (const byte*)"\xde\x06" "USBNET")
QDEF(MP_QSTR_USER, (const byte*)"\x54\x04" "USER")
QDEF(MP_QSTR_USER_1, (const byte*)"\xda\x06" "USER_1")
QDEF(MP_QSTR_USER_2, (const byte*)"\xd9\x06" "USER_2")
QDEF(MP_QSTR_USER_3, (const byte*)"\xd8\x06" "USER_3")
QDEF(MP_QSTR_USER_4, (const byte*)"\xdf\x06" "USER_4")
QDEF(MP_QSTR_USER_ENCODED_0, (const byte*)"\x26\x0e" "USER_ENCODED_0")
QDEF(MP_QSTR_USER_ENCODED_1, (const byte*)"\x27\x0e" "USER_ENCODED_1")
QDEF(MP_QSTR_USER_ENCODED_2, (const byte*)"\x24\x0e" "USER_ENCODED_2")
QDEF(MP_QSTR_USER_ENCODED_3, (const byte*)"\x25\x0e" "USER_ENCODED_3")
QDEF(MP_QSTR_USER_ENCODED_4, (const byte*)"\x22\x0e" "USER_ENCODED_4")
QDEF(MP_QSTR_USER_ENCODED_5, (const byte*)"\x23\x0e" "USER_ENCODED_5")
QDEF(MP_QSTR_USER_ENCODED_6, (const byte*)"\x20\x0e" "USER_ENCODED_6")
QDEF(MP_QSTR_USER_ENCODED_7, (const byte*)"\x21\x0e" "USER_ENCODED_7")
QDEF(MP_QSTR_USHORT, (const byte*)"\xa2\x06" "USHORT")
QDEF(MP_QSTR_VALUE, (const byte*)"\xae\x05" "VALUE")
QDEF(MP_QSTR_VALUE_CHANGED, (const byte*)"\x33\x0d" "VALUE_CHANGED")
QDEF(MP_QSTR_VARIABLE, (const byte*)"\xe3\x08" "VARIABLE")
QDEF(MP_QSTR_VER, (const byte*)"\x84\x03" "VER")
QDEF(MP_QSTR_VIDEO, (const byte*)"\xb4\x05" "VIDEO")
QDEF(MP_QSTR_VIRTUAL, (const byte*)"\xa4\x07" "VIRTUAL")
QDEF(MP_QSTR_VOID, (const byte*)"\x31\x04" "VOID")
QDEF(MP_QSTR_VOIP_EVENT_ANSWERED, (const byte*)"\x94\x13" "VOIP_EVENT_ANSWERED")
QDEF(MP_QSTR_VOIP_EVENT_BYE, (const byte*)"\x57\x0e" "VOIP_EVENT_BYE")
QDEF(MP_QSTR_VOIP_EVENT_CANCELED, (const byte*)"\xee\x13" "VOIP_EVENT_CANCELED")
QDEF(MP_QSTR_VOIP_EVENT_DEREGISTERED, (const byte*)"\x40\x17" "VOIP_EVENT_DEREGISTERED")
QDEF(MP_QSTR_VOIP_EVENT_DIALING, (const byte*)"\xc9\x12" "VOIP_EVENT_DIALING")
QDEF(MP_QSTR_VOIP_EVENT_ENDED, (const byte*)"\x87\x10" "VOIP_EVENT_ENDED")
QDEF(MP_QSTR_VOIP_EVENT_ERROR, (const byte*)"\xd1\x10" "VOIP_EVENT_ERROR")
QDEF(MP_QSTR_VOIP_EVENT_FAILED, (const byte*)"\x0a\x11" "VOIP_EVENT_FAILED")
QDEF(MP_QSTR_VOIP_EVENT_REGISTERED, (const byte*)"\xc1\x15" "VOIP_EVENT_REGISTERED")
QDEF(MP_QSTR_VOIP_EVENT_RINGING, (const byte*)"\x7b\x12" "VOIP_EVENT_RINGING")
QDEF(MP_QSTR_VOLUME_MAX, (const byte*)"\xa6\x0a" "VOLUME_MAX")
QDEF(MP_QSTR_VOLUME_MID, (const byte*)"\xb2\x0a" "VOLUME_MID")
QDEF(MP_QSTR_VfsLfs1, (const byte*)"\xee\x07" "VfsLfs1")
QDEF(MP_QSTR_WAIT, (const byte*)"\x8e\x04" "WAIT")
QDEF(MP_QSTR_WARN, (const byte*)"\xef\x04" "WARN")
QDEF(MP_QSTR_WARNING, (const byte*)"\x4f\x07" "WARNING")
QDEF(MP_QSTR_WDT, (const byte*)"\x62\x03" "WDT")
QDEF(MP_QSTR_WEAK, (const byte*)"\x9d\x04" "WEAK")
QDEF(MP_QSTR_WIDGET_1, (const byte*)"\xc7\x08" "WIDGET_1")
QDEF(MP_QSTR_WIDGET_2, (const byte*)"\xc4\x08" "WIDGET_2")
QDEF(MP_QSTR_WIDTH, (const byte*)"\xc3\x05" "WIDTH")
QDEF(MP_QSTR_WIFI, (const byte*)"\x74\x04" "WIFI")
QDEF(MP_QSTR_WR, (const byte*)"\x60\x02" "WR")
QDEF(MP_QSTR_WRAP, (const byte*)"\x11\x04" "WRAP")
QDEF(MP_QSTR_WRITEONLY, (const byte*)"\x0c\x09" "WRITEONLY")
QDEF(MP_QSTR_WRITEREAD, (const byte*)"\xca\x09" "WRITEREAD")
QDEF(MP_QSTR_X, (const byte*)"\xfd\x01" "X")
QDEF(MP_QSTR_Y, (const byte*)"\xfc\x01" "Y")
QDEF(MP_QSTR_YELLOW, (const byte*)"\x41\x06" "YELLOW")
QDEF(MP_QSTR_YT8512H, (const byte*)"\x4e\x07" "YT8512H")
QDEF(MP_QSTR__0, (const byte*)"\x0a\x02" "_0")
QDEF(MP_QSTR__10, (const byte*)"\x5b\x03" "_10")
QDEF(MP_QSTR__100, (const byte*)"\x8b\x04" "_100")
QDEF(MP_QSTR__180, (const byte*)"\x83\x04" "_180")
QDEF(MP_QSTR__20, (const byte*)"\x38\x03" "_20")
QDEF(MP_QSTR__270, (const byte*)"\x2f\x04" "_270")
QDEF(MP_QSTR__30, (const byte*)"\x19\x03" "_30")
QDEF(MP_QSTR__40, (const byte*)"\xfe\x03" "_40")
QDEF(MP_QSTR__50, (const byte*)"\xdf\x03" "_50")
QDEF(MP_QSTR__60, (const byte*)"\xbc\x03" "_60")
QDEF(MP_QSTR__70, (const byte*)"\x9d\x03" "_70")
QDEF(MP_QSTR__80, (const byte*)"\x72\x03" "_80")
QDEF(MP_QSTR__90, (const byte*)"\x53\x03" "_90")
QDEF(MP_QSTR__del, (const byte*)"\xb7\x04" "_del")
QDEF(MP_QSTR__exc_context, (const byte*)"\x24\x0c" "_exc_context")
QDEF(MP_QSTR__lv_draw_mask_common_dsc_t, (const byte*)"\x26\x1a" "_lv_draw_mask_common_dsc_t")
QDEF(MP_QSTR__lv_draw_mask_common_dsc_t_cb, (const byte*)"\x98\x1d" "_lv_draw_mask_common_dsc_t_cb")
QDEF(MP_QSTR__lv_indev_proc_t, (const byte*)"\x95\x10" "_lv_indev_proc_t")
QDEF(MP_QSTR__lv_indev_proc_types_keypad_t, (const byte*)"\xfc\x1d" "_lv_indev_proc_types_keypad_t")
QDEF(MP_QSTR__lv_indev_proc_types_pointer_t, (const byte*)"\x45\x1e" "_lv_indev_proc_types_pointer_t")
QDEF(MP_QSTR__lv_indev_proc_types_t, (const byte*)"\xc1\x16" "_lv_indev_proc_types_t")
QDEF(MP_QSTR__lv_mp_int_wrapper, (const byte*)"\x62\x12" "_lv_mp_int_wrapper")
QDEF(MP_QSTR__task_queue, (const byte*)"\xd9\x0b" "_task_queue")
QDEF(MP_QSTR__thread, (const byte*)"\xd4\x07" "_thread")
QDEF(MP_QSTR__uasyncio, (const byte*)"\xcf\x09" "_uasyncio")
QDEF(MP_QSTR_a2b_base64, (const byte*)"\x3c\x0a" "a2b_base64")
QDEF(MP_QSTR_accept, (const byte*)"\x85\x06" "accept")
QDEF(MP_QSTR_acos, (const byte*)"\x1b\x04" "acos")
QDEF(MP_QSTR_acosh, (const byte*)"\x13\x05" "acosh")
QDEF(MP_QSTR_acquire, (const byte*)"\x1d\x07" "acquire")
QDEF(MP_QSTR_act_obj, (const byte*)"\xab\x07" "act_obj")
QDEF(MP_QSTR_act_point, (const byte*)"\x40\x09" "act_point")
QDEF(MP_QSTR_act_scr, (const byte*)"\xce\x07" "act_scr")
QDEF(MP_QSTR_act_time, (const byte*)"\xb9\x08" "act_time")
QDEF(MP_QSTR_active, (const byte*)"\x69\x06" "active")
QDEF(MP_QSTR_add, (const byte*)"\x44\x03" "add")
QDEF(MP_QSTR_add_arc, (const byte*)"\xcb\x07" "add_arc")
QDEF(MP_QSTR_add_btn, (const byte*)"\x03\x07" "add_btn")
QDEF(MP_QSTR_add_cell_ctrl, (const byte*)"\x2b\x0d" "add_cell_ctrl")
QDEF(MP_QSTR_add_char, (const byte*)"\xc3\x08" "add_char")
QDEF(MP_QSTR_add_cursor, (const byte*)"\xd1\x0a" "add_cursor")
QDEF(MP_QSTR_add_event_cb, (const byte*)"\xc9\x0c" "add_event_cb")
QDEF(MP_QSTR_add_flag, (const byte*)"\x77\x08" "add_flag")
QDEF(MP_QSTR_add_needle_img, (const byte*)"\x84\x0e" "add_needle_img")
QDEF(MP_QSTR_add_needle_line, (const byte*)"\xa9\x0f" "add_needle_line")
QDEF(MP_QSTR_add_obj, (const byte*)"\x5c\x07" "add_obj")
QDEF(MP_QSTR_add_option, (const byte*)"\x58\x0a" "add_option")
QDEF(MP_QSTR_add_scale, (const byte*)"\xa3\x09" "add_scale")
QDEF(MP_QSTR_add_scale_lines, (const byte*)"\x21\x0f" "add_scale_lines")
QDEF(MP_QSTR_add_series, (const byte*)"\x01\x0a" "add_series")
QDEF(MP_QSTR_add_state, (const byte*)"\x8c\x09" "add_state")
QDEF(MP_QSTR_add_style, (const byte*)"\x8c\x09" "add_style")
QDEF(MP_QSTR_add_tab, (const byte*)"\xac\x07" "add_tab")
QDEF(MP_QSTR_add_text, (const byte*)"\x06\x08" "add_text")
QDEF(MP_QSTR_add_tile, (const byte*)"\x8f\x08" "add_tile")
QDEF(MP_QSTR_add_title, (const byte*)"\x3b\x09" "add_title")
QDEF(MP_QSTR_addressof, (const byte*)"\x5a\x09" "addressof")
QDEF(MP_QSTR_adv_w, (const byte*)"\x1e\x05" "adv_w")
QDEF(MP_QSTR_aes_ecb, (const byte*)"\x49\x07" "aes_ecb")
QDEF(MP_QSTR_align, (const byte*)"\xa8\x05" "align")
QDEF(MP_QSTR_align_to, (const byte*)"\x6c\x08" "align_to")
QDEF(MP_QSTR_allocate_lock, (const byte*)"\xec\x0d" "allocate_lock")
QDEF(MP_QSTR_allocate_semaphore, (const byte*)"\x5d\x12" "allocate_semaphore")
QDEF(MP_QSTR_allocate_semphore, (const byte*)"\x1c\x11" "allocate_semphore")
QDEF(MP_QSTR_allocate_spec_attr, (const byte*)"\x2e\x12" "allocate_spec_attr")
QDEF(MP_QSTR_alpha, (const byte*)"\x51\x05" "alpha")
QDEF(MP_QSTR_always_zero, (const byte*)"\xe9\x0b" "always_zero")
QDEF(MP_QSTR_angle, (const byte*)"\x84\x05" "angle")
QDEF(MP_QSTR_angle_init, (const byte*)"\x41\x0a" "angle_init")
QDEF(MP_QSTR_angle_range, (const byte*)"\x84\x0b" "angle_range")
QDEF(MP_QSTR_anim_count_running, (const byte*)"\xca\x12" "anim_count_running")
QDEF(MP_QSTR_anim_del, (const byte*)"\x7c\x08" "anim_del")
QDEF(MP_QSTR_anim_del_all, (const byte*)"\xe2\x0c" "anim_del_all")
QDEF(MP_QSTR_anim_get, (const byte*)"\x27\x08" "anim_get")
QDEF(MP_QSTR_anim_refr_now, (const byte*)"\xbb\x0d" "anim_refr_now")
QDEF(MP_QSTR_anim_speed_to_time, (const byte*)"\x58\x12" "anim_speed_to_time")
QDEF(MP_QSTR_anim_t, (const byte*)"\xc5\x06" "anim_t")
QDEF(MP_QSTR_anim_timeline_add, (const byte*)"\x34\x11" "anim_timeline_add")
QDEF(MP_QSTR_anim_timeline_create, (const byte*)"\xf1\x14" "anim_timeline_create")
QDEF(MP_QSTR_anim_timeline_del, (const byte*)"\x58\x11" "anim_timeline_del")
QDEF(MP_QSTR_anim_timeline_get_playtime, (const byte*)"\x2d\x1a" "anim_timeline_get_playtime")
QDEF(MP_QSTR_anim_timeline_get_reverse, (const byte*)"\xfc\x19" "anim_timeline_get_reverse")
QDEF(MP_QSTR_anim_timeline_set_progress, (const byte*)"\x75\x1a" "anim_timeline_set_progress")
QDEF(MP_QSTR_anim_timeline_set_reverse, (const byte*)"\xe8\x19" "anim_timeline_set_reverse")
QDEF(MP_QSTR_anim_timeline_start, (const byte*)"\x75\x13" "anim_timeline_start")
QDEF(MP_QSTR_animimg, (const byte*)"\x4d\x07" "animimg")
QDEF(MP_QSTR_animimg_class, (const byte*)"\xbc\x0d" "animimg_class")
QDEF(MP_QSTR_answer, (const byte*)"\xb9\x06" "answer")
QDEF(MP_QSTR_antennaSecRXOffCtrl, (const byte*)"\x23\x13" "antennaSecRXOffCtrl")
QDEF(MP_QSTR_antialias, (const byte*)"\x81\x09" "antialias")
QDEF(MP_QSTR_antialiasing, (const byte*)"\x01\x0c" "antialiasing")
QDEF(MP_QSTR_apply_cb, (const byte*)"\x4f\x08" "apply_cb")
QDEF(MP_QSTR_arc, (const byte*)"\x95\x03" "arc")
QDEF(MP_QSTR_arc_class, (const byte*)"\xe4\x09" "arc_class")
QDEF(MP_QSTR_arc_dsc, (const byte*)"\xfe\x07" "arc_dsc")
QDEF(MP_QSTR_area_is_visible, (const byte*)"\xe6\x0f" "area_is_visible")
QDEF(MP_QSTR_area_t, (const byte*)"\x59\x06" "area_t")
QDEF(MP_QSTR_argv, (const byte*)"\xc7\x04" "argv")
QDEF(MP_QSTR_array, (const byte*)"\x7c\x05" "array")
QDEF(MP_QSTR_asin, (const byte*)"\x50\x04" "asin")
QDEF(MP_QSTR_asinh, (const byte*)"\x38\x05" "asinh")
QDEF(MP_QSTR_async_call, (const byte*)"\xfe\x0a" "async_call")
QDEF(MP_QSTR_async_wait, (const byte*)"\xd7\x0a" "async_wait")
QDEF(MP_QSTR_atan, (const byte*)"\x1f\x04" "atan")
QDEF(MP_QSTR_atan2, (const byte*)"\xcd\x05" "atan2")
QDEF(MP_QSTR_atanh, (const byte*)"\x97\x05" "atanh")
QDEF(MP_QSTR_atcmd, (const byte*)"\xfa\x05" "atcmd")
QDEF(MP_QSTR_aud_tone_play, (const byte*)"\xe1\x0d" "aud_tone_play")
QDEF(MP_QSTR_aud_tone_play_stop, (const byte*)"\xc6\x12" "aud_tone_play_stop")
QDEF(MP_QSTR_audio, (const byte*)"\x53\x05" "audio")
QDEF(MP_QSTR_audiobuf, (const byte*)"\x62\x08" "audiobuf")
QDEF(MP_QSTR_authmode, (const byte*)"\xce\x08" "authmode")
QDEF(MP_QSTR_autoSleep, (const byte*)"\x85\x09" "autoSleep")
QDEF(MP_QSTR_autosleep, (const byte*)"\x65\x09" "autosleep")
QDEF(MP_QSTR_b2a_base64, (const byte*)"\x3c\x0a" "b2a_base64")
QDEF(MP_QSTR_b40acs, (const byte*)"\x72\x06" "b40acs")
QDEF(MP_QSTR_bar, (const byte*)"\xf4\x03" "bar")
QDEF(MP_QSTR_bar_class, (const byte*)"\x45\x09" "bar_class")
QDEF(MP_QSTR_base_class, (const byte*)"\x41\x0a" "base_class")
QDEF(MP_QSTR_base_line, (const byte*)"\xe1\x09" "base_line")
QDEF(MP_QSTR_baudrate, (const byte*)"\xf5\x08" "baudrate")
QDEF(MP_QSTR_bezier3, (const byte*)"\x55\x07" "bezier3")
QDEF(MP_QSTR_bg_color, (const byte*)"\x02\x08" "bg_color")
QDEF(MP_QSTR_bg_img, (const byte*)"\x7c\x06" "bg_img")
QDEF(MP_QSTR_bg_img_opa, (const byte*)"\x7d\x0a" "bg_img_opa")
QDEF(MP_QSTR_bg_img_recolor, (const byte*)"\x09\x0e" "bg_img_recolor")
QDEF(MP_QSTR_bg_img_recolor_opa, (const byte*)"\x48\x12" "bg_img_recolor_opa")
QDEF(MP_QSTR_bg_img_src, (const byte*)"\x21\x0a" "bg_img_src")
QDEF(MP_QSTR_bg_img_symbol_font, (const byte*)"\x29\x12" "bg_img_symbol_font")
QDEF(MP_QSTR_bg_img_tiled, (const byte*)"\xf3\x0c" "bg_img_tiled")
QDEF(MP_QSTR_bg_opa, (const byte*)"\x01\x06" "bg_opa")
QDEF(MP_QSTR_bidi_dir, (const byte*)"\xe3\x08" "bidi_dir")
QDEF(MP_QSTR_bin, (const byte*)"\xe0\x03" "bin")
QDEF(MP_QSTR_binascii, (const byte*)"\x91\x08" "binascii")
QDEF(MP_QSTR_bind, (const byte*)"\x84\x04" "bind")
QDEF(MP_QSTR_bits, (const byte*)"\x49\x04" "bits")
QDEF(MP_QSTR_blend_mode, (const byte*)"\xf8\x0a" "blend_mode")
QDEF(MP_QSTR_blink, (const byte*)"\x07\x05" "blink")
QDEF(MP_QSTR_blue, (const byte*)"\x7b\x04" "blue")
QDEF(MP_QSTR_blur_hor, (const byte*)"\x46\x08" "blur_hor")
QDEF(MP_QSTR_blur_ver, (const byte*)"\x12\x08" "blur_ver")
QDEF(MP_QSTR_border_color, (const byte*)"\x6b\x0c" "border_color")
QDEF(MP_QSTR_border_opa, (const byte*)"\x68\x0a" "border_opa")
QDEF(MP_QSTR_border_post, (const byte*)"\x2e\x0b" "border_post")
QDEF(MP_QSTR_border_side, (const byte*)"\xed\x0b" "border_side")
QDEF(MP_QSTR_border_width, (const byte*)"\x10\x0c" "border_width")
QDEF(MP_QSTR_bound_method, (const byte*)"\x97\x0c" "bound_method")
QDEF(MP_QSTR_box_h, (const byte*)"\xa7\x05" "box_h")
QDEF(MP_QSTR_box_w, (const byte*)"\xb8\x05" "box_w")
QDEF(MP_QSTR_bpp, (const byte*)"\xc7\x03" "bpp")
QDEF(MP_QSTR_breakin, (const byte*)"\xdd\x07" "breakin")
QDEF(MP_QSTR_btn, (const byte*)"\x5d\x03" "btn")
QDEF(MP_QSTR_btn_class, (const byte*)"\xac\x09" "btn_class")
QDEF(MP_QSTR_btn_id, (const byte*)"\x8f\x06" "btn_id")
QDEF(MP_QSTR_btn_points, (const byte*)"\x5d\x0a" "btn_points")
QDEF(MP_QSTR_btnmatrix, (const byte*)"\x86\x09" "btnmatrix")
QDEF(MP_QSTR_btnmatrix_class, (const byte*)"\x37\x0f" "btnmatrix_class")
QDEF(MP_QSTR_buf1, (const byte*)"\xc5\x04" "buf1")
QDEF(MP_QSTR_buf2, (const byte*)"\xc6\x04" "buf2")
QDEF(MP_QSTR_buf_act, (const byte*)"\x9d\x07" "buf_act")
QDEF(MP_QSTR_buf_alloc, (const byte*)"\x86\x09" "buf_alloc")
QDEF(MP_QSTR_buf_free, (const byte*)"\x1f\x08" "buf_free")
QDEF(MP_QSTR_buf_get_img_size, (const byte*)"\x7b\x10" "buf_get_img_size")
QDEF(MP_QSTR_buf_get_px_alpha, (const byte*)"\x21\x10" "buf_get_px_alpha")
QDEF(MP_QSTR_buf_get_px_color, (const byte*)"\xa8\x10" "buf_get_px_color")
QDEF(MP_QSTR_buf_set_palette, (const byte*)"\x0b\x0f" "buf_set_palette")
QDEF(MP_QSTR_buf_set_px_alpha, (const byte*)"\xb5\x10" "buf_set_px_alpha")
QDEF(MP_QSTR_buf_set_px_color, (const byte*)"\x3c\x10" "buf_set_px_color")
QDEF(MP_QSTR_buffer, (const byte*)"\xe5\x06" "buffer")
QDEF(MP_QSTR_buffering, (const byte*)"\x25\x09" "buffering")
QDEF(MP_QSTR_built_in_close, (const byte*)"\x12\x0e" "built_in_close")
QDEF(MP_QSTR_built_in_info, (const byte*)"\xaa\x0d" "built_in_info")
QDEF(MP_QSTR_built_in_open, (const byte*)"\xd0\x0d" "built_in_open")
QDEF(MP_QSTR_built_in_read_line, (const byte*)"\xa7\x12" "built_in_read_line")
QDEF(MP_QSTR_bytearray_at, (const byte*)"\x9c\x0c" "bytearray_at")
QDEF(MP_QSTR_byteorder, (const byte*)"\x61\x09" "byteorder")
QDEF(MP_QSTR_bytes_at, (const byte*)"\xb6\x08" "bytes_at")
QDEF(MP_QSTR_cache_invalidate_src, (const byte*)"\x6a\x14" "cache_invalidate_src")
QDEF(MP_QSTR_cache_set_size, (const byte*)"\x2e\x0e" "cache_set_size")
QDEF(MP_QSTR_calcsize, (const byte*)"\x4d\x08" "calcsize")
QDEF(MP_QSTR_calculate_ext_draw_size, (const byte*)"\xb2\x17" "calculate_ext_draw_size")
QDEF(MP_QSTR_calendar, (const byte*)"\x17\x08" "calendar")
QDEF(MP_QSTR_calendar_class, (const byte*)"\x66\x0e" "calendar_class")
QDEF(MP_QSTR_calendar_date_t, (const byte*)"\xb7\x0f" "calendar_date_t")
QDEF(MP_QSTR_calendar_header_arrow, (const byte*)"\x51\x15" "calendar_header_arrow")
QDEF(MP_QSTR_calendar_header_arrow_class, (const byte*)"\xa0\x1b" "calendar_header_arrow_class")
QDEF(MP_QSTR_calendar_header_dropdown, (const byte*)"\x93\x18" "calendar_header_dropdown")
QDEF(MP_QSTR_calendar_header_dropdown_class, (const byte*)"\xe2\x1e" "calendar_header_dropdown_class")
QDEF(MP_QSTR_call, (const byte*)"\x67\x04" "call")
QDEF(MP_QSTR_call_exception_handler, (const byte*)"\xe8\x16" "call_exception_handler")
QDEF(MP_QSTR_callback, (const byte*)"\x4c\x08" "callback")
QDEF(MP_QSTR_cancel, (const byte*)"\x03\x06" "cancel")
QDEF(MP_QSTR_canvas, (const byte*)"\xcd\x06" "canvas")
QDEF(MP_QSTR_canvas_class, (const byte*)"\x3c\x0c" "canvas_class")
QDEF(MP_QSTR_cb, (const byte*)"\xe4\x02" "cb")
QDEF(MP_QSTR_ceil, (const byte*)"\x06\x04" "ceil")
QDEF(MP_QSTR_center, (const byte*)"\x4e\x06" "center")
QDEF(MP_QSTR_cert, (const byte*)"\x25\x04" "cert")
QDEF(MP_QSTR_cf, (const byte*)"\xe0\x02" "cf")
QDEF(MP_QSTR_cf_get_px_size, (const byte*)"\xc4\x0e" "cf_get_px_size")
QDEF(MP_QSTR_cf_has_alpha, (const byte*)"\xce\x0c" "cf_has_alpha")
QDEF(MP_QSTR_cf_is_chroma_keyed, (const byte*)"\x29\x12" "cf_is_chroma_keyed")
QDEF(MP_QSTR_cfg, (const byte*)"\x87\x03" "cfg")
QDEF(MP_QSTR_ch, (const byte*)"\xee\x02" "ch")
QDEF(MP_QSTR_changePin, (const byte*)"\xf4\x09" "changePin")
QDEF(MP_QSTR_channel, (const byte*)"\x26\x07" "channel")
QDEF(MP_QSTR_char_val, (const byte*)"\xf9\x08" "char_val")
QDEF(MP_QSTR_chart, (const byte*)"\x09\x05" "chart")
QDEF(MP_QSTR_chart_class, (const byte*)"\x78\x0b" "chart_class")
QDEF(MP_QSTR_chart_cursor_t, (const byte*)"\xb7\x0e" "chart_cursor_t")
QDEF(MP_QSTR_chart_series_t, (const byte*)"\x66\x0e" "chart_series_t")
QDEF(MP_QSTR_chdir, (const byte*)"\xb1\x05" "chdir")
QDEF(MP_QSTR_check_type, (const byte*)"\x44\x0a" "check_type")
QDEF(MP_QSTR_checkbox, (const byte*)"\x56\x08" "checkbox")
QDEF(MP_QSTR_checkbox_class, (const byte*)"\xe7\x0e" "checkbox_class")
QDEF(MP_QSTR_choice, (const byte*)"\x2e\x06" "choice")
QDEF(MP_QSTR_clamp_height, (const byte*)"\xd6\x0c" "clamp_height")
QDEF(MP_QSTR_clamp_width, (const byte*)"\xcf\x0b" "clamp_width")
QDEF(MP_QSTR_class_create_obj, (const byte*)"\x08\x10" "class_create_obj")
QDEF(MP_QSTR_class_init_obj, (const byte*)"\x56\x0e" "class_init_obj")
QDEF(MP_QSTR_class_p, (const byte*)"\xa4\x07" "class_p")
QDEF(MP_QSTR_clean, (const byte*)"\x60\x05" "clean")
QDEF(MP_QSTR_clean_dcache, (const byte*)"\x97\x0c" "clean_dcache")
QDEF(MP_QSTR_clean_dcache_cb, (const byte*)"\xc9\x0f" "clean_dcache_cb")
QDEF(MP_QSTR_clear_btn_ctrl, (const byte*)"\xed\x0e" "clear_btn_ctrl")
QDEF(MP_QSTR_clear_btn_ctrl_all, (const byte*)"\xf3\x12" "clear_btn_ctrl_all")
QDEF(MP_QSTR_clear_cell_ctrl, (const byte*)"\x53\x0f" "clear_cell_ctrl")
QDEF(MP_QSTR_clear_flag, (const byte*)"\x8f\x0a" "clear_flag")
QDEF(MP_QSTR_clear_options, (const byte*)"\x13\x0d" "clear_options")
QDEF(MP_QSTR_clear_selection, (const byte*)"\x83\x0f" "clear_selection")
QDEF(MP_QSTR_clear_state, (const byte*)"\x74\x0b" "clear_state")
QDEF(MP_QSTR_clk, (const byte*)"\x41\x03" "clk")
QDEF(MP_QSTR_close_cb, (const byte*)"\x6d\x08" "close_cb")
QDEF(MP_QSTR_closure, (const byte*)"\x74\x07" "closure")
QDEF(MP_QSTR_cmath, (const byte*)"\xb6\x05" "cmath")
QDEF(MP_QSTR_code, (const byte*)"\x68\x04" "code")
QDEF(MP_QSTR_collect, (const byte*)"\x9b\x07" "collect")
QDEF(MP_QSTR_color, (const byte*)"\xd8\x05" "color")
QDEF(MP_QSTR_color32_ch_t, (const byte*)"\x66\x0c" "color32_ch_t")
QDEF(MP_QSTR_color32_t, (const byte*)"\xf2\x09" "color32_t")
QDEF(MP_QSTR_color_black, (const byte*)"\x20\x0b" "color_black")
QDEF(MP_QSTR_color_brightness, (const byte*)"\x4e\x10" "color_brightness")
QDEF(MP_QSTR_color_change_lightness, (const byte*)"\xab\x16" "color_change_lightness")
QDEF(MP_QSTR_color_chroma_key, (const byte*)"\x35\x10" "color_chroma_key")
QDEF(MP_QSTR_color_darken, (const byte*)"\xf0\x0c" "color_darken")
QDEF(MP_QSTR_color_end, (const byte*)"\xc8\x09" "color_end")
QDEF(MP_QSTR_color_fill, (const byte*)"\xc8\x0a" "color_fill")
QDEF(MP_QSTR_color_filter_dsc_t, (const byte*)"\x07\x12" "color_filter_dsc_t")
QDEF(MP_QSTR_color_hex, (const byte*)"\xb2\x09" "color_hex")
QDEF(MP_QSTR_color_hex3, (const byte*)"\xc1\x0a" "color_hex3")
QDEF(MP_QSTR_color_hsv_t, (const byte*)"\xe1\x0b" "color_hsv_t")
QDEF(MP_QSTR_color_hsv_to_rgb, (const byte*)"\xc6\x10" "color_hsv_to_rgb")
QDEF(MP_QSTR_color_lighten, (const byte*)"\xd2\x0d" "color_lighten")
QDEF(MP_QSTR_color_make, (const byte*)"\x85\x0a" "color_make")
QDEF(MP_QSTR_color_mix, (const byte*)"\xfb\x09" "color_mix")
QDEF(MP_QSTR_color_mix_premult, (const byte*)"\xc3\x11" "color_mix_premult")
QDEF(MP_QSTR_color_mix_with_alpha, (const byte*)"\xad\x14" "color_mix_with_alpha")
QDEF(MP_QSTR_color_premult, (const byte*)"\x20\x0d" "color_premult")
QDEF(MP_QSTR_color_primary, (const byte*)"\x6b\x0d" "color_primary")
QDEF(MP_QSTR_color_rgb_to_hsv, (const byte*)"\x06\x10" "color_rgb_to_hsv")
QDEF(MP_QSTR_color_secondary, (const byte*)"\xdd\x0f" "color_secondary")
QDEF(MP_QSTR_color_start, (const byte*)"\x47\x0b" "color_start")
QDEF(MP_QSTR_color_t, (const byte*)"\x13\x07" "color_t")
QDEF(MP_QSTR_color_to1, (const byte*)"\xad\x09" "color_to1")
QDEF(MP_QSTR_color_to16, (const byte*)"\x7b\x0a" "color_to16")
QDEF(MP_QSTR_color_to32, (const byte*)"\xbd\x0a" "color_to32")
QDEF(MP_QSTR_color_to8, (const byte*)"\xa4\x09" "color_to8")
QDEF(MP_QSTR_color_to_hsv, (const byte*)"\xae\x0c" "color_to_hsv")
QDEF(MP_QSTR_color_white, (const byte*)"\x80\x0b" "color_white")
QDEF(MP_QSTR_colorwheel, (const byte*)"\x0b\x0a" "colorwheel")
QDEF(MP_QSTR_colorwheel_class, (const byte*)"\x7a\x10" "colorwheel_class")
QDEF(MP_QSTR_compile, (const byte*)"\xf4\x07" "compile")
QDEF(MP_QSTR_complex, (const byte*)"\xc5\x07" "complex")
QDEF(MP_QSTR_config, (const byte*)"\x4f\x06" "config")
QDEF(MP_QSTR_connect, (const byte*)"\xdb\x07" "connect")
QDEF(MP_QSTR_const_props, (const byte*)"\x51\x0b" "const_props")
QDEF(MP_QSTR_constructor_cb, (const byte*)"\x13\x0e" "constructor_cb")
QDEF(MP_QSTR_continue_reading, (const byte*)"\x09\x10" "continue_reading")
QDEF(MP_QSTR_control_485, (const byte*)"\x44\x0b" "control_485")
QDEF(MP_QSTR_coord_y, (const byte*)"\xb6\x07" "coord_y")
QDEF(MP_QSTR_coords, (const byte*)"\xc3\x06" "coords")
QDEF(MP_QSTR_copy_buf, (const byte*)"\x8e\x08" "copy_buf")
QDEF(MP_QSTR_copysign, (const byte*)"\x33\x08" "copysign")
QDEF(MP_QSTR_coro, (const byte*)"\xb4\x04" "coro")
QDEF(MP_QSTR_cos, (const byte*)"\x7a\x03" "cos")
QDEF(MP_QSTR_cosh, (const byte*)"\xd2\x04" "cosh")
QDEF(MP_QSTR_count_reset, (const byte*)"\x6c\x0b" "count_reset")
QDEF(MP_QSTR_crc32, (const byte*)"\x76\x05" "crc32")
QDEF(MP_QSTR_create_obj, (const byte*)"\xd9\x0a" "create_obj")
QDEF(MP_QSTR_create_wakelock, (const byte*)"\x4d\x0f" "create_wakelock")
QDEF(MP_QSTR_csd, (const byte*)"\xf1\x03" "csd")
QDEF(MP_QSTR_csqQueryPoll, (const byte*)"\x31\x0c" "csqQueryPoll")
QDEF(MP_QSTR_curCnt, (const byte*)"\xd8\x06" "curCnt")
QDEF(MP_QSTR_cur_task, (const byte*)"\xf3\x08" "cur_task")
QDEF(MP_QSTR_current_target, (const byte*)"\xc2\x0e" "current_target")
QDEF(MP_QSTR_current_value, (const byte*)"\x38\x0d" "current_value")
QDEF(MP_QSTR_cursor, (const byte*)"\xcf\x06" "cursor")
QDEF(MP_QSTR_cursor_down, (const byte*)"\x42\x0b" "cursor_down")
QDEF(MP_QSTR_cursor_left, (const byte*)"\xab\x0b" "cursor_left")
QDEF(MP_QSTR_cursor_right, (const byte*)"\x70\x0c" "cursor_right")
QDEF(MP_QSTR_cursor_up, (const byte*)"\x55\x09" "cursor_up")
QDEF(MP_QSTR_custom_del, (const byte*)"\x84\x0a" "custom_del")
QDEF(MP_QSTR_cut_text, (const byte*)"\xc5\x08" "cut_text")
QDEF(MP_QSTR_dalay, (const byte*)"\xd4\x05" "dalay")
QDEF(MP_QSTR_dash_gap, (const byte*)"\x52\x08" "dash_gap")
QDEF(MP_QSTR_dash_width, (const byte*)"\xc2\x0a" "dash_width")
QDEF(MP_QSTR_data, (const byte*)"\x15\x04" "data")
QDEF(MP_QSTR_data_size, (const byte*)"\xcf\x09" "data_size")
QDEF(MP_QSTR_databuf, (const byte*)"\x64\x07" "databuf")
QDEF(MP_QSTR_datalen, (const byte*)"\x72\x07" "datalen")
QDEF(MP_QSTR_dataline, (const byte*)"\xdb\x08" "dataline")
QDEF(MP_QSTR_datasize, (const byte*)"\x30\x08" "datasize")
QDEF(MP_QSTR_datetime, (const byte*)"\xe4\x08" "datetime")
QDEF(MP_QSTR_day, (const byte*)"\xf9\x03" "day")
QDEF(MP_QSTR_decode, (const byte*)"\xa9\x06" "decode")
QDEF(MP_QSTR_decoder, (const byte*)"\xbb\x07" "decoder")
QDEF(MP_QSTR_decoder_built_in_close, (const byte*)"\x93\x16" "decoder_built_in_close")
QDEF(MP_QSTR_decoder_built_in_info, (const byte*)"\x8b\x15" "decoder_built_in_info")
QDEF(MP_QSTR_decoder_built_in_open, (const byte*)"\x71\x15" "decoder_built_in_open")
QDEF(MP_QSTR_decoder_built_in_read_line, (const byte*)"\x26\x1a" "decoder_built_in_read_line")
QDEF(MP_QSTR_decoder_close, (const byte*)"\xd2\x0d" "decoder_close")
QDEF(MP_QSTR_decoder_create, (const byte*)"\x01\x0e" "decoder_create")
QDEF(MP_QSTR_decoder_delete, (const byte*)"\xdd\x0e" "decoder_delete")
QDEF(MP_QSTR_decoder_get_info, (const byte*)"\xa3\x10" "decoder_get_info")
QDEF(MP_QSTR_decoder_open, (const byte*)"\x10\x0c" "decoder_open")
QDEF(MP_QSTR_decoder_read_line, (const byte*)"\x67\x11" "decoder_read_line")
QDEF(MP_QSTR_decoder_set_close_cb, (const byte*)"\xb1\x14" "decoder_set_close_cb")
QDEF(MP_QSTR_decoder_set_info_cb, (const byte*)"\xe9\x13" "decoder_set_info_cb")
QDEF(MP_QSTR_decoder_set_open_cb, (const byte*)"\x13\x13" "decoder_set_open_cb")
QDEF(MP_QSTR_decoder_set_read_line_cb, (const byte*)"\x24\x18" "decoder_set_read_line_cb")
QDEF(MP_QSTR_decompress, (const byte*)"\x62\x0a" "decompress")
QDEF(MP_QSTR_decor, (const byte*)"\x5a\x05" "decor")
QDEF(MP_QSTR_decrement, (const byte*)"\x42\x09" "decrement")
QDEF(MP_QSTR_def_event_cb, (const byte*)"\x6f\x0c" "def_event_cb")
QDEF(MP_QSTR_default, (const byte*)"\xce\x07" "default")
QDEF(MP_QSTR_degrees, (const byte*)"\x02\x07" "degrees")
QDEF(MP_QSTR_deinit, (const byte*)"\x9e\x06" "deinit")
QDEF(MP_QSTR_del_anim_ready_cb, (const byte*)"\x76\x11" "del_anim_ready_cb")
QDEF(MP_QSTR_del_async, (const byte*)"\xd1\x09" "del_async")
QDEF(MP_QSTR_del_char, (const byte*)"\x6f\x08" "del_char")
QDEF(MP_QSTR_del_char_forward, (const byte*)"\xab\x10" "del_char_forward")
QDEF(MP_QSTR_del_delayed, (const byte*)"\x43\x0b" "del_delayed")
QDEF(MP_QSTR_del_prev, (const byte*)"\xc6\x08" "del_prev")
QDEF(MP_QSTR_del_span, (const byte*)"\x7b\x08" "del_span")
QDEF(MP_QSTR_delattr, (const byte*)"\xdb\x07" "delattr")
QDEF(MP_QSTR_delay, (const byte*)"\x50\x05" "delay")
QDEF(MP_QSTR_delete, (const byte*)"\x9c\x06" "delete")
QDEF(MP_QSTR_delete_lock, (const byte*)"\x68\x0b" "delete_lock")
QDEF(MP_QSTR_delete_semaphore, (const byte*)"\x59\x10" "delete_semaphore")
QDEF(MP_QSTR_delete_semphore, (const byte*)"\x98\x0f" "delete_semphore")
QDEF(MP_QSTR_delete_timer, (const byte*)"\x84\x0c" "delete_timer")
QDEF(MP_QSTR_delete_wakelock, (const byte*)"\x30\x0f" "delete_wakelock")
QDEF(MP_QSTR_deleted, (const byte*)"\x78\x07" "deleted")
QDEF(MP_QSTR_deleter, (const byte*)"\x6e\x07" "deleter")
QDEF(MP_QSTR_delta_deg, (const byte*)"\xc4\x09" "delta_deg")
QDEF(MP_QSTR_deque, (const byte*)"\x05\x05" "deque")
QDEF(MP_QSTR_destructor_cb, (const byte*)"\xd0\x0d" "destructor_cb")
QDEF(MP_QSTR_dhcp, (const byte*)"\x7a\x04" "dhcp")
QDEF(MP_QSTR_dial, (const byte*)"\x65\x04" "dial")
QDEF(MP_QSTR_dialerAnswer, (const byte*)"\x8e\x0c" "dialerAnswer")
QDEF(MP_QSTR_dialerConnect, (const byte*)"\x0c\x0d" "dialerConnect")
QDEF(MP_QSTR_dialerReject, (const byte*)"\x5d\x0c" "dialerReject")
QDEF(MP_QSTR_dict_view, (const byte*)"\x2d\x09" "dict_view")
QDEF(MP_QSTR_difference, (const byte*)"\x72\x0a" "difference")
QDEF(MP_QSTR_difference_update, (const byte*)"\x9c\x11" "difference_update")
QDEF(MP_QSTR_digest, (const byte*)"\xcd\x06" "digest")
QDEF(MP_QSTR_dir_close_cb, (const byte*)"\x0d\x0c" "dir_close_cb")
QDEF(MP_QSTR_dir_d, (const byte*)"\x61\x05" "dir_d")
QDEF(MP_QSTR_dir_open_cb, (const byte*)"\x2f\x0b" "dir_open_cb")
QDEF(MP_QSTR_dir_read_cb, (const byte*)"\x49\x0b" "dir_read_cb")
QDEF(MP_QSTR_disable, (const byte*)"\x91\x07" "disable")
QDEF(MP_QSTR_disablePin, (const byte*)"\x06\x0a" "disablePin")
QDEF(MP_QSTR_disabled, (const byte*)"\xd5\x08" "disabled")
QDEF(MP_QSTR_discard, (const byte*)"\x0f\x07" "discard")
QDEF(MP_QSTR_disp, (const byte*)"\x2b\x04" "disp")
QDEF(MP_QSTR_disp_draw_buf_t, (const byte*)"\x91\x0f" "disp_draw_buf_t")
QDEF(MP_QSTR_disp_drv_t, (const byte*)"\xff\x0a" "disp_drv_t")
QDEF(MP_QSTR_disp_get_default, (const byte*)"\x96\x10" "disp_get_default")
QDEF(MP_QSTR_disp_load_scr, (const byte*)"\x0f\x0d" "disp_load_scr")
QDEF(MP_QSTR_disp_t, (const byte*)"\x20\x06" "disp_t")
QDEF(MP_QSTR_displayoffbuf, (const byte*)"\x01\x0d" "displayoffbuf")
QDEF(MP_QSTR_displayonbuf, (const byte*)"\x2f\x0c" "displayonbuf")
QDEF(MP_QSTR_doc, (const byte*)"\x2d\x03" "doc")
QDEF(MP_QSTR_done, (const byte*)"\x45\x04" "done")
QDEF(MP_QSTR_dpi, (const byte*)"\xd8\x03" "dpi")
QDEF(MP_QSTR_dpx, (const byte*)"\xc9\x03" "dpx")
QDEF(MP_QSTR_draw_arc, (const byte*)"\x6a\x08" "draw_arc")
QDEF(MP_QSTR_draw_arc_dsc_t, (const byte*)"\x0a\x0e" "draw_arc_dsc_t")
QDEF(MP_QSTR_draw_arc_get_area, (const byte*)"\x6b\x11" "draw_arc_get_area")
QDEF(MP_QSTR_draw_area, (const byte*)"\x8d\x09" "draw_area")
QDEF(MP_QSTR_draw_buf, (const byte*)"\x8b\x08" "draw_buf")
QDEF(MP_QSTR_draw_dsc_init, (const byte*)"\x4b\x0d" "draw_dsc_init")
QDEF(MP_QSTR_draw_img, (const byte*)"\xd9\x08" "draw_img")
QDEF(MP_QSTR_draw_img_dsc_t, (const byte*)"\xb9\x0e" "draw_img_dsc_t")
QDEF(MP_QSTR_draw_label, (const byte*)"\xbc\x0a" "draw_label")
QDEF(MP_QSTR_draw_label_dsc_t, (const byte*)"\xdc\x10" "draw_label_dsc_t")
QDEF(MP_QSTR_draw_label_hint_t, (const byte*)"\x33\x11" "draw_label_hint_t")
QDEF(MP_QSTR_draw_letter, (const byte*)"\xa4\x0b" "draw_letter")
QDEF(MP_QSTR_draw_line, (const byte*)"\x54\x09" "draw_line")
QDEF(MP_QSTR_draw_line_dsc_t, (const byte*)"\xf4\x0f" "draw_line_dsc_t")
QDEF(MP_QSTR_draw_mask_add, (const byte*)"\xd0\x0d" "draw_mask_add")
QDEF(MP_QSTR_draw_mask_angle_param_cfg_t, (const byte*)"\x16\x1b" "draw_mask_angle_param_cfg_t")
QDEF(MP_QSTR_draw_mask_angle_param_t, (const byte*)"\x8b\x17" "draw_mask_angle_param_t")
QDEF(MP_QSTR_draw_mask_apply, (const byte*)"\xc5\x0f" "draw_mask_apply")
QDEF(MP_QSTR_draw_mask_fade_param_cfg_t, (const byte*)"\xd1\x1a" "draw_mask_fade_param_cfg_t")
QDEF(MP_QSTR_draw_mask_fade_param_t, (const byte*)"\x4c\x16" "draw_mask_fade_param_t")
QDEF(MP_QSTR_draw_mask_get_cnt, (const byte*)"\x61\x11" "draw_mask_get_cnt")
QDEF(MP_QSTR_draw_mask_line_param_cfg_t, (const byte*)"\xd9\x1a" "draw_mask_line_param_cfg_t")
QDEF(MP_QSTR_draw_mask_line_param_t, (const byte*)"\x44\x16" "draw_mask_line_param_t")
QDEF(MP_QSTR_draw_mask_map_param_cfg_t, (const byte*)"\x6b\x19" "draw_mask_map_param_cfg_t")
QDEF(MP_QSTR_draw_mask_map_param_t, (const byte*)"\x36\x15" "draw_mask_map_param_t")
QDEF(MP_QSTR_draw_mask_radius_param_cfg_t, (const byte*)"\x2f\x1c" "draw_mask_radius_param_cfg_t")
QDEF(MP_QSTR_draw_mask_radius_param_t, (const byte*)"\xf2\x18" "draw_mask_radius_param_t")
QDEF(MP_QSTR_draw_mask_remove_custom, (const byte*)"\x3b\x17" "draw_mask_remove_custom")
QDEF(MP_QSTR_draw_mask_remove_id, (const byte*)"\xe5\x13" "draw_mask_remove_id")
QDEF(MP_QSTR_draw_part_check_type, (const byte*)"\x73\x14" "draw_part_check_type")
QDEF(MP_QSTR_draw_polygon, (const byte*)"\xd6\x0c" "draw_polygon")
QDEF(MP_QSTR_draw_rect, (const byte*)"\x3a\x09" "draw_rect")
QDEF(MP_QSTR_draw_rect_dsc_t, (const byte*)"\xda\x0f" "draw_rect_dsc_t")
QDEF(MP_QSTR_draw_text, (const byte*)"\x87\x09" "draw_text")
QDEF(MP_QSTR_draw_triangle, (const byte*)"\x34\x0d" "draw_triangle")
QDEF(MP_QSTR_driver, (const byte*)"\x1b\x06" "driver")
QDEF(MP_QSTR_dropdown, (const byte*)"\xde\x08" "dropdown")
QDEF(MP_QSTR_dropdown_class, (const byte*)"\x6f\x0e" "dropdown_class")
QDEF(MP_QSTR_dropdownlist_class, (const byte*)"\x0d\x12" "dropdownlist_class")
QDEF(MP_QSTR_drv, (const byte*)"\x85\x03" "drv")
QDEF(MP_QSTR_drv_update, (const byte*)"\xcb\x0a" "drv_update")
QDEF(MP_QSTR_drv_update_cb, (const byte*)"\x15\x0d" "drv_update_cb")
QDEF(MP_QSTR_dsc, (const byte*)"\xb1\x03" "dsc")
QDEF(MP_QSTR_dsc_init, (const byte*)"\x94\x08" "dsc_init")
QDEF(MP_QSTR_dump, (const byte*)"\xe9\x04" "dump")
QDEF(MP_QSTR_dumps, (const byte*)"\x7a\x05" "dumps")
QDEF(MP_QSTR_e, (const byte*)"\xc0\x01" "e")
QDEF(MP_QSTR_early_apply, (const byte*)"\x2d\x0b" "early_apply")
QDEF(MP_QSTR_editable, (const byte*)"\x33\x08" "editable")
QDEF(MP_QSTR_editing, (const byte*)"\x19\x07" "editing")
QDEF(MP_QSTR_enable, (const byte*)"\x04\x06" "enable")
QDEF(MP_QSTR_enablePin, (const byte*)"\xb3\x09" "enablePin")
QDEF(MP_QSTR_enable_alarm, (const byte*)"\x08\x0c" "enable_alarm")
QDEF(MP_QSTR_enable_style_refresh, (const byte*)"\x4e\x14" "enable_style_refresh")
QDEF(MP_QSTR_enc_diff, (const byte*)"\x3f\x08" "enc_diff")
QDEF(MP_QSTR_encode, (const byte*)"\x43\x06" "encode")
QDEF(MP_QSTR_encoding, (const byte*)"\x06\x08" "encoding")
QDEF(MP_QSTR_end_angle, (const byte*)"\x94\x09" "end_angle")
QDEF(MP_QSTR_end_line, (const byte*)"\xdb\x08" "end_line")
QDEF(MP_QSTR_end_value, (const byte*)"\x5e\x09" "end_value")
QDEF(MP_QSTR_enforce_init, (const byte*)"\x36\x0c" "enforce_init")
QDEF(MP_QSTR_enumerate, (const byte*)"\x71\x09" "enumerate")
QDEF(MP_QSTR_erf, (const byte*)"\x94\x03" "erf")
QDEF(MP_QSTR_erfc, (const byte*)"\x77\x04" "erfc")
QDEF(MP_QSTR_errno, (const byte*)"\xc1\x05" "errno")
QDEF(MP_QSTR_error_msg, (const byte*)"\x9b\x09" "error_msg")
QDEF(MP_QSTR_errorcode, (const byte*)"\x10\x09" "errorcode")
QDEF(MP_QSTR_ethernet, (const byte*)"\x14\x08" "ethernet")
QDEF(MP_QSTR_event_base, (const byte*)"\xc3\x0a" "event_base")
QDEF(MP_QSTR_event_cb, (const byte*)"\xd7\x08" "event_cb")
QDEF(MP_QSTR_event_register_id, (const byte*)"\x8d\x11" "event_register_id")
QDEF(MP_QSTR_event_send, (const byte*)"\x0a\x0a" "event_send")
QDEF(MP_QSTR_event_t, (const byte*)"\x02\x07" "event_t")
QDEF(MP_QSTR_example, (const byte*)"\xed\x07" "example")
QDEF(MP_QSTR_exception, (const byte*)"\xd2\x09" "exception")
QDEF(MP_QSTR_exec_cb, (const byte*)"\x60\x07" "exec_cb")
QDEF(MP_QSTR_execfile, (const byte*)"\x58\x08" "execfile")
QDEF(MP_QSTR_exit, (const byte*)"\x85\x04" "exit")
QDEF(MP_QSTR_exp, (const byte*)"\xc8\x03" "exp")
QDEF(MP_QSTR_expm1, (const byte*)"\x74\x05" "expm1")
QDEF(MP_QSTR_extra_init, (const byte*)"\x3a\x0a" "extra_init")
QDEF(MP_QSTR_f, (const byte*)"\xc3\x01" "f")
QDEF(MP_QSTR_fabs, (const byte*)"\x93\x04" "fabs")
QDEF(MP_QSTR_fade_in, (const byte*)"\x5b\x07" "fade_in")
QDEF(MP_QSTR_fade_out, (const byte*)"\x32\x08" "fade_out")
QDEF(MP_QSTR_fast_extint, (const byte*)"\xe0\x0b" "fast_extint")
QDEF(MP_QSTR_fastmode, (const byte*)"\x86\x08" "fastmode")
QDEF(MP_QSTR_feed, (const byte*)"\xa7\x04" "feed")
QDEF(MP_QSTR_feedback_cb, (const byte*)"\xb2\x0b" "feedback_cb")
QDEF(MP_QSTR_file, (const byte*)"\xc3\x04" "file")
QDEF(MP_QSTR_file_d, (const byte*)"\x98\x06" "file_d")
QDEF(MP_QSTR_fileno, (const byte*)"\x82\x06" "fileno")
QDEF(MP_QSTR_fill_bg, (const byte*)"\x10\x07" "fill_bg")
QDEF(MP_QSTR_filter, (const byte*)"\x25\x06" "filter")
QDEF(MP_QSTR_filter_cb, (const byte*)"\xbb\x09" "filter_cb")
QDEF(MP_QSTR_filter_time, (const byte*)"\xaf\x0b" "filter_time")
QDEF(MP_QSTR_flag, (const byte*)"\x69\x04" "flag")
QDEF(MP_QSTR_flags, (const byte*)"\xfa\x05" "flags")
QDEF(MP_QSTR_flat, (const byte*)"\x7a\x04" "flat")
QDEF(MP_QSTR_flex_init, (const byte*)"\x77\x09" "flex_init")
QDEF(MP_QSTR_float, (const byte*)"\x35\x05" "float")
QDEF(MP_QSTR_floor, (const byte*)"\x7d\x05" "floor")
QDEF(MP_QSTR_flow, (const byte*)"\x37\x04" "flow")
QDEF(MP_QSTR_flush, (const byte*)"\x61\x05" "flush")
QDEF(MP_QSTR_flush_cb, (const byte*)"\xff\x08" "flush_cb")
QDEF(MP_QSTR_flush_is_last, (const byte*)"\x11\x0d" "flush_is_last")
QDEF(MP_QSTR_flush_ready, (const byte*)"\xd5\x0b" "flush_ready")
QDEF(MP_QSTR_flushing, (const byte*)"\x21\x08" "flushing")
QDEF(MP_QSTR_flushing_last, (const byte*)"\xf4\x0d" "flushing_last")
QDEF(MP_QSTR_fmod, (const byte*)"\xe5\x04" "fmod")
QDEF(MP_QSTR_focus_cb, (const byte*)"\x57\x08" "focus_cb")
QDEF(MP_QSTR_focus_freeze, (const byte*)"\x9d\x0c" "focus_freeze")
QDEF(MP_QSTR_focus_next, (const byte*)"\xf1\x0a" "focus_next")
QDEF(MP_QSTR_focus_prev, (const byte*)"\x87\x0a" "focus_prev")
QDEF(MP_QSTR_font, (const byte*)"\x96\x04" "font")
QDEF(MP_QSTR_font_06_songti, (const byte*)"\x18\x0e" "font_06_songti")
QDEF(MP_QSTR_font_09_songti, (const byte*)"\xf7\x0e" "font_09_songti")
QDEF(MP_QSTR_font_18_songti, (const byte*)"\x17\x0e" "font_18_songti")
QDEF(MP_QSTR_font_20_songti, (const byte*)"\x5c\x0e" "font_20_songti")
QDEF(MP_QSTR_font_default, (const byte*)"\x42\x0c" "font_default")
QDEF(MP_QSTR_font_glyph_dsc_t, (const byte*)"\x23\x10" "font_glyph_dsc_t")
QDEF(MP_QSTR_font_large, (const byte*)"\x14\x0a" "font_large")
QDEF(MP_QSTR_font_load, (const byte*)"\x6f\x09" "font_load")
QDEF(MP_QSTR_font_montserrat_14, (const byte*)"\xc8\x12" "font_montserrat_14")
QDEF(MP_QSTR_font_normal, (const byte*)"\x3a\x0b" "font_normal")
QDEF(MP_QSTR_font_small, (const byte*)"\x36\x0a" "font_small")
QDEF(MP_QSTR_font_t, (const byte*)"\x5d\x06" "font_t")
QDEF(MP_QSTR_fota, (const byte*)"\xd9\x04" "fota")
QDEF(MP_QSTR_frag_pct, (const byte*)"\x0f\x08" "frag_pct")
QDEF(MP_QSTR_frameJump, (const byte*)"\xba\x09" "frameJump")
QDEF(MP_QSTR_frame_id, (const byte*)"\x4a\x08" "frame_id")
QDEF(MP_QSTR_free, (const byte*)"\xf1\x04" "free")
QDEF(MP_QSTR_free_biggest_size, (const byte*)"\x1d\x11" "free_biggest_size")
QDEF(MP_QSTR_free_cnt, (const byte*)"\xb7\x08" "free_cnt")
QDEF(MP_QSTR_free_size, (const byte*)"\x2b\x09" "free_size")
QDEF(MP_QSTR_freq, (const byte*)"\xe5\x04" "freq")
QDEF(MP_QSTR_frexp, (const byte*)"\x1c\x05" "frexp")
QDEF(MP_QSTR_frozen, (const byte*)"\x0f\x06" "frozen")
QDEF(MP_QSTR_frozenset, (const byte*)"\xed\x09" "frozenset")
QDEF(MP_QSTR_fs_dir_t, (const byte*)"\xbb\x08" "fs_dir_t")
QDEF(MP_QSTR_fs_drv_t, (const byte*)"\x84\x08" "fs_drv_t")
QDEF(MP_QSTR_fs_file_t, (const byte*)"\x82\x09" "fs_file_t")
QDEF(MP_QSTR_fs_get_drv, (const byte*)"\xa6\x0a" "fs_get_drv")
QDEF(MP_QSTR_fs_get_ext, (const byte*)"\x6f\x0a" "fs_get_ext")
QDEF(MP_QSTR_fs_get_last, (const byte*)"\x2c\x0b" "fs_get_last")
QDEF(MP_QSTR_fs_get_letters, (const byte*)"\x8b\x0e" "fs_get_letters")
QDEF(MP_QSTR_fs_is_ready, (const byte*)"\x81\x0b" "fs_is_ready")
QDEF(MP_QSTR_fs_up, (const byte*)"\xaa\x05" "fs_up")
QDEF(MP_QSTR_ftpDownload, (const byte*)"\x33\x0b" "ftpDownload")
QDEF(MP_QSTR_full, (const byte*)"\xd6\x04" "full")
QDEF(MP_QSTR_full_refresh, (const byte*)"\x54\x0c" "full_refresh")
QDEF(MP_QSTR_function, (const byte*)"\x27\x08" "function")
QDEF(MP_QSTR_future, (const byte*)"\x01\x06" "future")
QDEF(MP_QSTR_gamma, (const byte*)"\x02\x05" "gamma")
QDEF(MP_QSTR_gc, (const byte*)"\x61\x02" "gc")
QDEF(MP_QSTR_generator, (const byte*)"\x96\x09" "generator")
QDEF(MP_QSTR_gesture_dir, (const byte*)"\x02\x0b" "gesture_dir")
QDEF(MP_QSTR_gesture_limit, (const byte*)"\x28\x0d" "gesture_limit")
QDEF(MP_QSTR_gesture_min_velocity, (const byte*)"\xdf\x14" "gesture_min_velocity")
QDEF(MP_QSTR_gesture_sent, (const byte*)"\x91\x0c" "gesture_sent")
QDEF(MP_QSTR_gesture_sum, (const byte*)"\x56\x0b" "gesture_sum")
QDEF(MP_QSTR_getAddressinfo, (const byte*)"\x4b\x0e" "getAddressinfo")
QDEF(MP_QSTR_getApn, (const byte*)"\xcc\x06" "getApn")
QDEF(MP_QSTR_getCellInfo, (const byte*)"\x3b\x0b" "getCellInfo")
QDEF(MP_QSTR_getCellInfos, (const byte*)"\xe8\x0c" "getCellInfos")
QDEF(MP_QSTR_getCi, (const byte*)"\xb9\x05" "getCi")
QDEF(MP_QSTR_getCnt, (const byte*)"\x0a\x06" "getCnt")
QDEF(MP_QSTR_getConfig, (const byte*)"\x99\x09" "getConfig")
QDEF(MP_QSTR_getDevFwVersion, (const byte*)"\x4f\x0f" "getDevFwVersion")
QDEF(MP_QSTR_getDevImei, (const byte*)"\x8c\x0a" "getDevImei")
QDEF(MP_QSTR_getDevModel, (const byte*)"\x6b\x0b" "getDevModel")
QDEF(MP_QSTR_getDevProductId, (const byte*)"\x82\x0f" "getDevProductId")
QDEF(MP_QSTR_getDevSN, (const byte*)"\xd9\x08" "getDevSN")
QDEF(MP_QSTR_getDialerStatus, (const byte*)"\x90\x0f" "getDialerStatus")
QDEF(MP_QSTR_getIccid, (const byte*)"\xd7\x08" "getIccid")
QDEF(MP_QSTR_getImsi, (const byte*)"\xcd\x07" "getImsi")
QDEF(MP_QSTR_getInfo, (const byte*)"\xbd\x07" "getInfo")
QDEF(MP_QSTR_getLac, (const byte*)"\xfd\x06" "getLac")
QDEF(MP_QSTR_getMcc, (const byte*)"\xfe\x06" "getMcc")
QDEF(MP_QSTR_getMnc, (const byte*)"\x93\x06" "getMnc")
QDEF(MP_QSTR_getModemFun, (const byte*)"\x80\x0b" "getModemFun")
QDEF(MP_QSTR_getNetMode, (const byte*)"\x2f\x0a" "getNetMode")
QDEF(MP_QSTR_getPDPContext, (const byte*)"\x48\x0d" "getPDPContext")
QDEF(MP_QSTR_getPdpRange, (const byte*)"\x28\x0b" "getPdpRange")
QDEF(MP_QSTR_getPhoneNumber, (const byte*)"\xac\x0e" "getPhoneNumber")
QDEF(MP_QSTR_getPhonebookStatus, (const byte*)"\x72\x12" "getPhonebookStatus")
QDEF(MP_QSTR_getPinRemAttempts, (const byte*)"\xe0\x11" "getPinRemAttempts")
QDEF(MP_QSTR_getPlayFile, (const byte*)"\xd1\x0b" "getPlayFile")
QDEF(MP_QSTR_getPlayState, (const byte*)"\x01\x0c" "getPlayState")
QDEF(MP_QSTR_getRange, (const byte*)"\x8c\x08" "getRange")
QDEF(MP_QSTR_getRingNumber, (const byte*)"\xe2\x0d" "getRingNumber")
QDEF(MP_QSTR_getServingCi, (const byte*)"\x2b\x0c" "getServingCi")
QDEF(MP_QSTR_getServingLac, (const byte*)"\x2f\x0d" "getServingLac")
QDEF(MP_QSTR_getServingMcc, (const byte*)"\xac\x0d" "getServingMcc")
QDEF(MP_QSTR_getServingMnc, (const byte*)"\x41\x0d" "getServingMnc")
QDEF(MP_QSTR_getSignal, (const byte*)"\xed\x09" "getSignal")
QDEF(MP_QSTR_getSiminfo, (const byte*)"\x4a\x0a" "getSiminfo")
QDEF(MP_QSTR_getSpeed, (const byte*)"\x74\x08" "getSpeed")
QDEF(MP_QSTR_getState, (const byte*)"\xc4\x08" "getState")
QDEF(MP_QSTR_getStatus, (const byte*)"\x27\x09" "getStatus")
QDEF(MP_QSTR_getTimeZone, (const byte*)"\x18\x0b" "getTimeZone")
QDEF(MP_QSTR_getTimeZoneEx, (const byte*)"\x85\x0d" "getTimeZoneEx")
QDEF(MP_QSTR_getVbatt, (const byte*)"\x06\x08" "getVbatt")
QDEF(MP_QSTR_getVolume, (const byte*)"\x3b\x09" "getVolume")
QDEF(MP_QSTR_get_accepted_chars, (const byte*)"\x79\x12" "get_accepted_chars")
QDEF(MP_QSTR_get_active_btn_text, (const byte*)"\x85\x13" "get_active_btn_text")
QDEF(MP_QSTR_get_align, (const byte*)"\xc1\x09" "get_align")
QDEF(MP_QSTR_get_angle, (const byte*)"\xed\x09" "get_angle")
QDEF(MP_QSTR_get_angle_end, (const byte*)"\x3d\x0d" "get_angle_end")
QDEF(MP_QSTR_get_angle_start, (const byte*)"\xb2\x0f" "get_angle_start")
QDEF(MP_QSTR_get_answer_number, (const byte*)"\xac\x11" "get_answer_number")
QDEF(MP_QSTR_get_antialias, (const byte*)"\xe8\x0d" "get_antialias")
QDEF(MP_QSTR_get_antialiasing, (const byte*)"\xc8\x10" "get_antialiasing")
QDEF(MP_QSTR_get_bg_angle_end, (const byte*)"\x07\x10" "get_bg_angle_end")
QDEF(MP_QSTR_get_bg_angle_start, (const byte*)"\x88\x12" "get_bg_angle_start")
QDEF(MP_QSTR_get_bitmap_fmt_txt, (const byte*)"\x08\x12" "get_bitmap_fmt_txt")
QDEF(MP_QSTR_get_brightness, (const byte*)"\x05\x0e" "get_brightness")
QDEF(MP_QSTR_get_btn_text, (const byte*)"\xf6\x0c" "get_btn_text")
QDEF(MP_QSTR_get_btns, (const byte*)"\x47\x08" "get_btns")
QDEF(MP_QSTR_get_call_number, (const byte*)"\x32\x0f" "get_call_number")
QDEF(MP_QSTR_get_call_status, (const byte*)"\x45\x0f" "get_call_status")
QDEF(MP_QSTR_get_cell_value, (const byte*)"\xfe\x0e" "get_cell_value")
QDEF(MP_QSTR_get_child, (const byte*)"\xa6\x09" "get_child")
QDEF(MP_QSTR_get_child_cnt, (const byte*)"\xe0\x0d" "get_child_cnt")
QDEF(MP_QSTR_get_child_id, (const byte*)"\x54\x0c" "get_child_id")
QDEF(MP_QSTR_get_class, (const byte*)"\xc2\x09" "get_class")
QDEF(MP_QSTR_get_click_area, (const byte*)"\xca\x0e" "get_click_area")
QDEF(MP_QSTR_get_close_btn, (const byte*)"\x9d\x0d" "get_close_btn")
QDEF(MP_QSTR_get_code, (const byte*)"\xe1\x08" "get_code")
QDEF(MP_QSTR_get_col_cnt, (const byte*)"\x8a\x0b" "get_col_cnt")
QDEF(MP_QSTR_get_col_width, (const byte*)"\xb5\x0d" "get_col_width")
QDEF(MP_QSTR_get_color_mode, (const byte*)"\xed\x0e" "get_color_mode")
QDEF(MP_QSTR_get_color_mode_fixed, (const byte*)"\xe4\x14" "get_color_mode_fixed")
QDEF(MP_QSTR_get_content, (const byte*)"\x45\x0b" "get_content")
QDEF(MP_QSTR_get_content_coords, (const byte*)"\xfc\x12" "get_content_coords")
QDEF(MP_QSTR_get_content_height, (const byte*)"\xc5\x12" "get_content_height")
QDEF(MP_QSTR_get_content_width, (const byte*)"\x3c\x11" "get_content_width")
QDEF(MP_QSTR_get_coords, (const byte*)"\x0a\x0a" "get_coords")
QDEF(MP_QSTR_get_cover_area, (const byte*)"\x49\x0e" "get_cover_area")
QDEF(MP_QSTR_get_current_target, (const byte*)"\xcb\x12" "get_current_target")
QDEF(MP_QSTR_get_cursor_click_pos, (const byte*)"\x44\x14" "get_cursor_click_pos")
QDEF(MP_QSTR_get_cursor_point, (const byte*)"\x75\x10" "get_cursor_point")
QDEF(MP_QSTR_get_cursor_pos, (const byte*)"\xb5\x0e" "get_cursor_pos")
QDEF(MP_QSTR_get_delay, (const byte*)"\xb9\x09" "get_delay")
QDEF(MP_QSTR_get_dir, (const byte*)"\x13\x07" "get_dir")
QDEF(MP_QSTR_get_disp, (const byte*)"\x22\x08" "get_disp")
QDEF(MP_QSTR_get_dpi, (const byte*)"\x31\x07" "get_dpi")
QDEF(MP_QSTR_get_draw_buf, (const byte*)"\xc2\x0c" "get_draw_buf")
QDEF(MP_QSTR_get_draw_part_dsc, (const byte*)"\x8f\x11" "get_draw_part_dsc")
QDEF(MP_QSTR_get_editing, (const byte*)"\x70\x0b" "get_editing")
QDEF(MP_QSTR_get_expand_height, (const byte*)"\xca\x11" "get_expand_height")
QDEF(MP_QSTR_get_expand_width, (const byte*)"\x53\x10" "get_expand_width")
QDEF(MP_QSTR_get_focus_cb, (const byte*)"\xde\x0c" "get_focus_cb")
QDEF(MP_QSTR_get_focused, (const byte*)"\x81\x0b" "get_focused")
QDEF(MP_QSTR_get_gesture_dir, (const byte*)"\xab\x0f" "get_gesture_dir")
QDEF(MP_QSTR_get_glyph_bitmap, (const byte*)"\x9a\x10" "get_glyph_bitmap")
QDEF(MP_QSTR_get_glyph_dsc, (const byte*)"\x0d\x0d" "get_glyph_dsc")
QDEF(MP_QSTR_get_glyph_dsc_fmt_txt, (const byte*)"\x2a\x15" "get_glyph_dsc_fmt_txt")
QDEF(MP_QSTR_get_glyph_width, (const byte*)"\x9f\x0f" "get_glyph_width")
QDEF(MP_QSTR_get_group, (const byte*)"\xd3\x09" "get_group")
QDEF(MP_QSTR_get_header, (const byte*)"\x53\x0a" "get_header")
QDEF(MP_QSTR_get_heap_size, (const byte*)"\xca\x0d" "get_heap_size")
QDEF(MP_QSTR_get_height, (const byte*)"\xf3\x0a" "get_height")
QDEF(MP_QSTR_get_highlighted_dates, (const byte*)"\x05\x15" "get_highlighted_dates")
QDEF(MP_QSTR_get_highlighted_dates_num, (const byte*)"\xac\x19" "get_highlighted_dates_num")
QDEF(MP_QSTR_get_hit_test_info, (const byte*)"\x61\x11" "get_hit_test_info")
QDEF(MP_QSTR_get_hor_res, (const byte*)"\xe2\x0b" "get_hor_res")
QDEF(MP_QSTR_get_hsv, (const byte*)"\xc1\x07" "get_hsv")
QDEF(MP_QSTR_get_ident, (const byte*)"\xfe\x09" "get_ident")
QDEF(MP_QSTR_get_img, (const byte*)"\xcf\x07" "get_img")
QDEF(MP_QSTR_get_inactive_time, (const byte*)"\x4d\x11" "get_inactive_time")
QDEF(MP_QSTR_get_indent, (const byte*)"\xf0\x0a" "get_indent")
QDEF(MP_QSTR_get_indev, (const byte*)"\x1c\x09" "get_indev")
QDEF(MP_QSTR_get_info, (const byte*)"\x62\x08" "get_info")
QDEF(MP_QSTR_get_key, (const byte*)"\xdb\x07" "get_key")
QDEF(MP_QSTR_get_label, (const byte*)"\xaa\x09" "get_label")
QDEF(MP_QSTR_get_layer_sys, (const byte*)"\x89\x0d" "get_layer_sys")
QDEF(MP_QSTR_get_layer_top, (const byte*)"\x9b\x0d" "get_layer_top")
QDEF(MP_QSTR_get_left_value, (const byte*)"\xc3\x0e" "get_left_value")
QDEF(MP_QSTR_get_letter_on, (const byte*)"\x4c\x0d" "get_letter_on")
QDEF(MP_QSTR_get_letter_pos, (const byte*)"\x41\x0e" "get_letter_pos")
QDEF(MP_QSTR_get_line_height, (const byte*)"\x62\x0f" "get_line_height")
QDEF(MP_QSTR_get_list, (const byte*)"\x2e\x08" "get_list")
QDEF(MP_QSTR_get_local_style_prop, (const byte*)"\xab\x14" "get_local_style_prop")
QDEF(MP_QSTR_get_long_mode, (const byte*)"\xba\x0d" "get_long_mode")
QDEF(MP_QSTR_get_map, (const byte*)"\x50\x07" "get_map")
QDEF(MP_QSTR_get_map_array, (const byte*)"\xb6\x0d" "get_map_array")
QDEF(MP_QSTR_get_max_length, (const byte*)"\xdb\x0e" "get_max_length")
QDEF(MP_QSTR_get_max_line_h, (const byte*)"\x9e\x0e" "get_max_line_h")
QDEF(MP_QSTR_get_max_value, (const byte*)"\x0c\x0d" "get_max_value")
QDEF(MP_QSTR_get_min_value, (const byte*)"\x92\x0d" "get_min_value")
QDEF(MP_QSTR_get_mode, (const byte*)"\xaf\x08" "get_mode")
QDEF(MP_QSTR_get_next, (const byte*)"\x0b\x08" "get_next")
QDEF(MP_QSTR_get_obj_count, (const byte*)"\x97\x0d" "get_obj_count")
QDEF(MP_QSTR_get_offset_x, (const byte*)"\xc6\x0c" "get_offset_x")
QDEF(MP_QSTR_get_offset_y, (const byte*)"\xc7\x0c" "get_offset_y")
QDEF(MP_QSTR_get_old_size, (const byte*)"\x31\x0c" "get_old_size")
QDEF(MP_QSTR_get_one_checked, (const byte*)"\x70\x0f" "get_one_checked")
QDEF(MP_QSTR_get_one_line, (const byte*)"\xf9\x0c" "get_one_line")
QDEF(MP_QSTR_get_option_cnt, (const byte*)"\x89\x0e" "get_option_cnt")
QDEF(MP_QSTR_get_options, (const byte*)"\x9c\x0b" "get_options")
QDEF(MP_QSTR_get_overflow, (const byte*)"\x70\x0c" "get_overflow")
QDEF(MP_QSTR_get_param, (const byte*)"\x23\x09" "get_param")
QDEF(MP_QSTR_get_parent, (const byte*)"\x50\x0a" "get_parent")
QDEF(MP_QSTR_get_password_mode, (const byte*)"\x4f\x11" "get_password_mode")
QDEF(MP_QSTR_get_password_show_time, (const byte*)"\xa5\x16" "get_password_show_time")
QDEF(MP_QSTR_get_pivot, (const byte*)"\xf8\x09" "get_pivot")
QDEF(MP_QSTR_get_placeholder_text, (const byte*)"\x8d\x14" "get_placeholder_text")
QDEF(MP_QSTR_get_point, (const byte*)"\x01\x09" "get_point")
QDEF(MP_QSTR_get_point_count, (const byte*)"\x3c\x0f" "get_point_count")
QDEF(MP_QSTR_get_point_pos_by_id, (const byte*)"\x05\x13" "get_point_pos_by_id")
QDEF(MP_QSTR_get_pressed_date, (const byte*)"\x61\x10" "get_pressed_date")
QDEF(MP_QSTR_get_pressed_point, (const byte*)"\x99\x11" "get_pressed_point")
QDEF(MP_QSTR_get_prop, (const byte*)"\x71\x08" "get_prop")
QDEF(MP_QSTR_get_prop_inlined, (const byte*)"\x23\x10" "get_prop_inlined")
QDEF(MP_QSTR_get_px, (const byte*)"\xc4\x06" "get_px")
QDEF(MP_QSTR_get_recolor, (const byte*)"\x86\x0b" "get_recolor")
QDEF(MP_QSTR_get_reg_status, (const byte*)"\x37\x0e" "get_reg_status")
QDEF(MP_QSTR_get_rgb, (const byte*)"\xdb\x07" "get_rgb")
QDEF(MP_QSTR_get_rollover, (const byte*)"\xdf\x0c" "get_rollover")
QDEF(MP_QSTR_get_rotation, (const byte*)"\xf8\x0c" "get_rotation")
QDEF(MP_QSTR_get_row_cnt, (const byte*)"\x40\x0b" "get_row_cnt")
QDEF(MP_QSTR_get_scr_act, (const byte*)"\xe7\x0b" "get_scr_act")
QDEF(MP_QSTR_get_scr_prev, (const byte*)"\xa0\x0c" "get_scr_prev")
QDEF(MP_QSTR_get_screen, (const byte*)"\xc0\x0a" "get_screen")
QDEF(MP_QSTR_get_scroll_anim, (const byte*)"\x95\x0f" "get_scroll_anim")
QDEF(MP_QSTR_get_scroll_bottom, (const byte*)"\x31\x11" "get_scroll_bottom")
QDEF(MP_QSTR_get_scroll_dir, (const byte*)"\x01\x0e" "get_scroll_dir")
QDEF(MP_QSTR_get_scroll_end, (const byte*)"\xf1\x0e" "get_scroll_end")
QDEF(MP_QSTR_get_scroll_left, (const byte*)"\xc5\x0f" "get_scroll_left")
QDEF(MP_QSTR_get_scroll_obj, (const byte*)"\xf9\x0e" "get_scroll_obj")
QDEF(MP_QSTR_get_scroll_right, (const byte*)"\x1e\x10" "get_scroll_right")
QDEF(MP_QSTR_get_scroll_snap_x, (const byte*)"\x95\x11" "get_scroll_snap_x")
QDEF(MP_QSTR_get_scroll_snap_y, (const byte*)"\x94\x11" "get_scroll_snap_y")
QDEF(MP_QSTR_get_scroll_top, (const byte*)"\xd5\x0e" "get_scroll_top")
QDEF(MP_QSTR_get_scroll_x, (const byte*)"\x26\x0c" "get_scroll_x")
QDEF(MP_QSTR_get_scroll_y, (const byte*)"\x27\x0c" "get_scroll_y")
QDEF(MP_QSTR_get_scrollbar_area, (const byte*)"\x18\x12" "get_scrollbar_area")
QDEF(MP_QSTR_get_scrollbar_mode, (const byte*)"\xac\x12" "get_scrollbar_mode")
QDEF(MP_QSTR_get_selected, (const byte*)"\xa5\x0c" "get_selected")
QDEF(MP_QSTR_get_selected_btn, (const byte*)"\xa2\x10" "get_selected_btn")
QDEF(MP_QSTR_get_selected_cell, (const byte*)"\xbc\x11" "get_selected_cell")
QDEF(MP_QSTR_get_selected_highlight, (const byte*)"\xea\x16" "get_selected_highlight")
QDEF(MP_QSTR_get_selected_str, (const byte*)"\xaf\x10" "get_selected_str")
QDEF(MP_QSTR_get_self_height, (const byte*)"\x90\x0f" "get_self_height")
QDEF(MP_QSTR_get_self_size_info, (const byte*)"\x3b\x12" "get_self_size_info")
QDEF(MP_QSTR_get_self_width, (const byte*)"\x49\x0e" "get_self_width")
QDEF(MP_QSTR_get_series_next, (const byte*)"\xaf\x0f" "get_series_next")
QDEF(MP_QSTR_get_showed_date, (const byte*)"\xe5\x0f" "get_showed_date")
QDEF(MP_QSTR_get_sip_module_version, (const byte*)"\x06\x16" "get_sip_module_version")
QDEF(MP_QSTR_get_size, (const byte*)"\xa9\x08" "get_size")
QDEF(MP_QSTR_get_size_mode, (const byte*)"\x75\x0d" "get_size_mode")
QDEF(MP_QSTR_get_src, (const byte*)"\x6e\x07" "get_src")
QDEF(MP_QSTR_get_src_left, (const byte*)"\x2a\x0c" "get_src_left")
QDEF(MP_QSTR_get_src_middle, (const byte*)"\x1c\x0e" "get_src_middle")
QDEF(MP_QSTR_get_src_right, (const byte*)"\x11\x0d" "get_src_right")
QDEF(MP_QSTR_get_start_value, (const byte*)"\x78\x0f" "get_start_value")
QDEF(MP_QSTR_get_state, (const byte*)"\x3b\x09" "get_state")
QDEF(MP_QSTR_get_status, (const byte*)"\xf8\x0a" "get_status")
QDEF(MP_QSTR_get_step, (const byte*)"\x1e\x08" "get_step")
QDEF(MP_QSTR_get_style_align, (const byte*)"\xc9\x0f" "get_style_align")
QDEF(MP_QSTR_get_style_anim_speed, (const byte*)"\xf7\x14" "get_style_anim_speed")
QDEF(MP_QSTR_get_style_anim_time, (const byte*)"\x05\x13" "get_style_anim_time")
QDEF(MP_QSTR_get_style_arc_color, (const byte*)"\xf6\x13" "get_style_arc_color")
QDEF(MP_QSTR_get_style_arc_img_src, (const byte*)"\x55\x15" "get_style_arc_img_src")
QDEF(MP_QSTR_get_style_arc_opa, (const byte*)"\xf5\x11" "get_style_arc_opa")
QDEF(MP_QSTR_get_style_arc_rounded, (const byte*)"\xa8\x15" "get_style_arc_rounded")
QDEF(MP_QSTR_get_style_arc_width, (const byte*)"\x4d\x13" "get_style_arc_width")
QDEF(MP_QSTR_get_style_base_dir, (const byte*)"\x51\x12" "get_style_base_dir")
QDEF(MP_QSTR_get_style_bg_color, (const byte*)"\xc3\x12" "get_style_bg_color")
QDEF(MP_QSTR_get_style_bg_grad_color, (const byte*)"\x2c\x17" "get_style_bg_grad_color")
QDEF(MP_QSTR_get_style_bg_grad_dir, (const byte*)"\x4e\x15" "get_style_bg_grad_dir")
QDEF(MP_QSTR_get_style_bg_grad_stop, (const byte*)"\xa9\x16" "get_style_bg_grad_stop")
QDEF(MP_QSTR_get_style_bg_img_opa, (const byte*)"\xfc\x14" "get_style_bg_img_opa")
QDEF(MP_QSTR_get_style_bg_img_recolor, (const byte*)"\x48\x18" "get_style_bg_img_recolor")
QDEF(MP_QSTR_get_style_bg_img_recolor_opa, (const byte*)"\x89\x1c" "get_style_bg_img_recolor_opa")
QDEF(MP_QSTR_get_style_bg_img_src, (const byte*)"\xa0\x14" "get_style_bg_img_src")
QDEF(MP_QSTR_get_style_bg_img_tiled, (const byte*)"\x72\x16" "get_style_bg_img_tiled")
QDEF(MP_QSTR_get_style_bg_main_stop, (const byte*)"\x12\x16" "get_style_bg_main_stop")
QDEF(MP_QSTR_get_style_bg_opa, (const byte*)"\xc0\x10" "get_style_bg_opa")
QDEF(MP_QSTR_get_style_blend_mode, (const byte*)"\x39\x14" "get_style_blend_mode")
QDEF(MP_QSTR_get_style_border_color, (const byte*)"\xea\x16" "get_style_border_color")
QDEF(MP_QSTR_get_style_border_opa, (const byte*)"\xe9\x14" "get_style_border_opa")
QDEF(MP_QSTR_get_style_border_post, (const byte*)"\x4f\x15" "get_style_border_post")
QDEF(MP_QSTR_get_style_border_side, (const byte*)"\x8c\x15" "get_style_border_side")
QDEF(MP_QSTR_get_style_border_width, (const byte*)"\x51\x16" "get_style_border_width")
QDEF(MP_QSTR_get_style_clip_corner, (const byte*)"\xca\x15" "get_style_clip_corner")
QDEF(MP_QSTR_get_style_color_filter_dsc, (const byte*)"\x4d\x1a" "get_style_color_filter_dsc")
QDEF(MP_QSTR_get_style_color_filter_opa, (const byte*)"\x67\x1a" "get_style_color_filter_opa")
QDEF(MP_QSTR_get_style_flex_cross_place, (const byte*)"\xb6\x1a" "get_style_flex_cross_place")
QDEF(MP_QSTR_get_style_flex_flow, (const byte*)"\x7e\x13" "get_style_flex_flow")
QDEF(MP_QSTR_get_style_flex_grow, (const byte*)"\x01\x13" "get_style_flex_grow")
QDEF(MP_QSTR_get_style_flex_main_place, (const byte*)"\xe3\x19" "get_style_flex_main_place")
QDEF(MP_QSTR_get_style_flex_track_place, (const byte*)"\xa7\x1a" "get_style_flex_track_place")
QDEF(MP_QSTR_get_style_grid_cell_column_pos, (const byte*)"\x7f\x1e" "get_style_grid_cell_column_pos")
QDEF(MP_QSTR_get_style_grid_cell_column_span, (const byte*)"\xdf\x1f" "get_style_grid_cell_column_span")
QDEF(MP_QSTR_get_style_grid_cell_row_pos, (const byte*)"\x63\x1b" "get_style_grid_cell_row_pos")
QDEF(MP_QSTR_get_style_grid_cell_row_span, (const byte*)"\x43\x1c" "get_style_grid_cell_row_span")
QDEF(MP_QSTR_get_style_grid_cell_x_align, (const byte*)"\x90\x1b" "get_style_grid_cell_x_align")
QDEF(MP_QSTR_get_style_grid_cell_y_align, (const byte*)"\xd1\x1b" "get_style_grid_cell_y_align")
QDEF(MP_QSTR_get_style_grid_column_align, (const byte*)"\x67\x1b" "get_style_grid_column_align")
QDEF(MP_QSTR_get_style_grid_column_dsc_array, (const byte*)"\x98\x1f" "get_style_grid_column_dsc_array")
QDEF(MP_QSTR_get_style_grid_row_align, (const byte*)"\x5b\x18" "get_style_grid_row_align")
QDEF(MP_QSTR_get_style_grid_row_dsc_array, (const byte*)"\xa4\x1c" "get_style_grid_row_dsc_array")
QDEF(MP_QSTR_get_style_height, (const byte*)"\xfb\x10" "get_style_height")
QDEF(MP_QSTR_get_style_img_opa, (const byte*)"\x86\x11" "get_style_img_opa")
QDEF(MP_QSTR_get_style_img_recolor, (const byte*)"\xf2\x15" "get_style_img_recolor")
QDEF(MP_QSTR_get_style_img_recolor_opa, (const byte*)"\x73\x19" "get_style_img_recolor_opa")
QDEF(MP_QSTR_get_style_layout, (const byte*)"\x1e\x10" "get_style_layout")
QDEF(MP_QSTR_get_style_line_color, (const byte*)"\x48\x14" "get_style_line_color")
QDEF(MP_QSTR_get_style_line_dash_gap, (const byte*)"\xc2\x17" "get_style_line_dash_gap")
QDEF(MP_QSTR_get_style_line_dash_width, (const byte*)"\x52\x19" "get_style_line_dash_width")
QDEF(MP_QSTR_get_style_line_opa, (const byte*)"\x4b\x12" "get_style_line_opa")
QDEF(MP_QSTR_get_style_line_rounded, (const byte*)"\x16\x16" "get_style_line_rounded")
QDEF(MP_QSTR_get_style_line_width, (const byte*)"\xb3\x14" "get_style_line_width")
QDEF(MP_QSTR_get_style_max_height, (const byte*)"\xf0\x14" "get_style_max_height")
QDEF(MP_QSTR_get_style_max_width, (const byte*)"\xe9\x13" "get_style_max_width")
QDEF(MP_QSTR_get_style_min_height, (const byte*)"\x2e\x14" "get_style_min_height")
QDEF(MP_QSTR_get_style_min_width, (const byte*)"\x77\x13" "get_style_min_width")
QDEF(MP_QSTR_get_style_opa, (const byte*)"\x3a\x0d" "get_style_opa")
QDEF(MP_QSTR_get_style_outline_color, (const byte*)"\x46\x17" "get_style_outline_color")
QDEF(MP_QSTR_get_style_outline_opa, (const byte*)"\x45\x15" "get_style_outline_opa")
QDEF(MP_QSTR_get_style_outline_pad, (const byte*)"\x6e\x15" "get_style_outline_pad")
QDEF(MP_QSTR_get_style_outline_width, (const byte*)"\xbd\x17" "get_style_outline_width")
QDEF(MP_QSTR_get_style_pad_bottom, (const byte*)"\x01\x14" "get_style_pad_bottom")
QDEF(MP_QSTR_get_style_pad_column, (const byte*)"\xb8\x14" "get_style_pad_column")
QDEF(MP_QSTR_get_style_pad_left, (const byte*)"\xb5\x12" "get_style_pad_left")
QDEF(MP_QSTR_get_style_pad_right, (const byte*)"\x2e\x13" "get_style_pad_right")
QDEF(MP_QSTR_get_style_pad_row, (const byte*)"\x64\x11" "get_style_pad_row")
QDEF(MP_QSTR_get_style_pad_top, (const byte*)"\xe5\x11" "get_style_pad_top")
QDEF(MP_QSTR_get_style_prop, (const byte*)"\x79\x0e" "get_style_prop")
QDEF(MP_QSTR_get_style_radius, (const byte*)"\x7c\x10" "get_style_radius")
QDEF(MP_QSTR_get_style_shadow_color, (const byte*)"\xa0\x16" "get_style_shadow_color")
QDEF(MP_QSTR_get_style_shadow_ofs_x, (const byte*)"\x60\x16" "get_style_shadow_ofs_x")
QDEF(MP_QSTR_get_style_shadow_ofs_y, (const byte*)"\x61\x16" "get_style_shadow_ofs_y")
QDEF(MP_QSTR_get_style_shadow_opa, (const byte*)"\xa3\x14" "get_style_shadow_opa")
QDEF(MP_QSTR_get_style_shadow_spread, (const byte*)"\x6c\x17" "get_style_shadow_spread")
QDEF(MP_QSTR_get_style_shadow_width, (const byte*)"\x1b\x16" "get_style_shadow_width")
QDEF(MP_QSTR_get_style_text_align, (const byte*)"\xab\x14" "get_style_text_align")
QDEF(MP_QSTR_get_style_text_color, (const byte*)"\x9b\x14" "get_style_text_color")
QDEF(MP_QSTR_get_style_text_decor, (const byte*)"\x19\x14" "get_style_text_decor")
QDEF(MP_QSTR_get_style_text_font, (const byte*)"\xb5\x13" "get_style_text_font")
QDEF(MP_QSTR_get_style_text_letter_space, (const byte*)"\xe3\x1b" "get_style_text_letter_space")
QDEF(MP_QSTR_get_style_text_line_space, (const byte*)"\x53\x19" "get_style_text_line_space")
QDEF(MP_QSTR_get_style_text_opa, (const byte*)"\x98\x12" "get_style_text_opa")
QDEF(MP_QSTR_get_style_transform_angle, (const byte*)"\xf6\x19" "get_style_transform_angle")
QDEF(MP_QSTR_get_style_transform_height, (const byte*)"\xc8\x1a" "get_style_transform_height")
QDEF(MP_QSTR_get_style_transform_width, (const byte*)"\x11\x19" "get_style_transform_width")
QDEF(MP_QSTR_get_style_transform_zoom, (const byte*)"\xc0\x18" "get_style_transform_zoom")
QDEF(MP_QSTR_get_style_transition, (const byte*)"\x2b\x14" "get_style_transition")
QDEF(MP_QSTR_get_style_translate_x, (const byte*)"\x25\x15" "get_style_translate_x")
QDEF(MP_QSTR_get_style_translate_y, (const byte*)"\x24\x15" "get_style_translate_y")
QDEF(MP_QSTR_get_style_width, (const byte*)"\xc2\x0f" "get_style_width")
QDEF(MP_QSTR_get_style_x, (const byte*)"\x3c\x0b" "get_style_x")
QDEF(MP_QSTR_get_style_y, (const byte*)"\x3d\x0b" "get_style_y")
QDEF(MP_QSTR_get_symbol, (const byte*)"\x2a\x0a" "get_symbol")
QDEF(MP_QSTR_get_tab_act, (const byte*)"\xb2\x0b" "get_tab_act")
QDEF(MP_QSTR_get_tab_btns, (const byte*)"\xaf\x0c" "get_tab_btns")
QDEF(MP_QSTR_get_target, (const byte*)"\x7d\x0a" "get_target")
QDEF(MP_QSTR_get_text, (const byte*)"\x51\x08" "get_text")
QDEF(MP_QSTR_get_text_selection, (const byte*)"\xee\x12" "get_text_selection")
QDEF(MP_QSTR_get_text_selection_end, (const byte*)"\xbe\x16" "get_text_selection_end")
QDEF(MP_QSTR_get_text_selection_start, (const byte*)"\x31\x18" "get_text_selection_start")
QDEF(MP_QSTR_get_textarea, (const byte*)"\xc6\x0c" "get_textarea")
QDEF(MP_QSTR_get_theme, (const byte*)"\xdd\x09" "get_theme")
QDEF(MP_QSTR_get_tile_act, (const byte*)"\x31\x0c" "get_tile_act")
QDEF(MP_QSTR_get_title, (const byte*)"\x8c\x09" "get_title")
QDEF(MP_QSTR_get_today_date, (const byte*)"\x80\x0e" "get_today_date")
QDEF(MP_QSTR_get_type, (const byte*)"\x54\x08" "get_type")
QDEF(MP_QSTR_get_user_data, (const byte*)"\x72\x0d" "get_user_data")
QDEF(MP_QSTR_get_value, (const byte*)"\xa7\x09" "get_value")
QDEF(MP_QSTR_get_value_cb, (const byte*)"\x79\x0c" "get_value_cb")
QDEF(MP_QSTR_get_vect, (const byte*)"\x68\x08" "get_vect")
QDEF(MP_QSTR_get_ver_res, (const byte*)"\x36\x0b" "get_ver_res")
QDEF(MP_QSTR_get_wakelock_num, (const byte*)"\x56\x10" "get_wakelock_num")
QDEF(MP_QSTR_get_width, (const byte*)"\xca\x09" "get_width")
QDEF(MP_QSTR_get_worktype, (const byte*)"\x35\x0c" "get_worktype")
QDEF(MP_QSTR_get_wrap, (const byte*)"\x18\x08" "get_wrap")
QDEF(MP_QSTR_get_x, (const byte*)"\x34\x05" "get_x")
QDEF(MP_QSTR_get_x2, (const byte*)"\x86\x06" "get_x2")
QDEF(MP_QSTR_get_x_array, (const byte*)"\x52\x0b" "get_x_array")
QDEF(MP_QSTR_get_x_start_point, (const byte*)"\x58\x11" "get_x_start_point")
QDEF(MP_QSTR_get_y, (const byte*)"\x35\x05" "get_y")
QDEF(MP_QSTR_get_y2, (const byte*)"\xe7\x06" "get_y2")
QDEF(MP_QSTR_get_y_array, (const byte*)"\x13\x0b" "get_y_array")
QDEF(MP_QSTR_get_y_invert, (const byte*)"\xf8\x0c" "get_y_invert")
QDEF(MP_QSTR_get_zoom, (const byte*)"\xfb\x08" "get_zoom")
QDEF(MP_QSTR_get_zoom_x, (const byte*)"\xfc\x0a" "get_zoom_x")
QDEF(MP_QSTR_get_zoom_y, (const byte*)"\xfd\x0a" "get_zoom_y")
QDEF(MP_QSTR_getaddrinfo, (const byte*)"\x6e\x0b" "getaddrinfo")
QDEF(MP_QSTR_getcwd, (const byte*)"\x03\x06" "getcwd")
QDEF(MP_QSTR_getrandbits, (const byte*)"\x66\x0b" "getrandbits")
QDEF(MP_QSTR_getsendacksize, (const byte*)"\xc3\x0e" "getsendacksize")
QDEF(MP_QSTR_getsocketsta, (const byte*)"\xb0\x0c" "getsocketsta")
QDEF(MP_QSTR_getsockopt, (const byte*)"\xac\x0a" "getsockopt")
QDEF(MP_QSTR_getter, (const byte*)"\x90\x06" "getter")
QDEF(MP_QSTR_getvalue, (const byte*)"\x78\x08" "getvalue")
QDEF(MP_QSTR_green, (const byte*)"\xbe\x05" "green")
QDEF(MP_QSTR_grid_fr, (const byte*)"\x76\x07" "grid_fr")
QDEF(MP_QSTR_grid_init, (const byte*)"\xb8\x09" "grid_init")
QDEF(MP_QSTR_group, (const byte*)"\xba\x05" "group")
QDEF(MP_QSTR_group_create, (const byte*)"\x61\x0c" "group_create")
QDEF(MP_QSTR_group_def, (const byte*)"\xc2\x09" "group_def")
QDEF(MP_QSTR_group_focus_obj, (const byte*)"\xf1\x0f" "group_focus_obj")
QDEF(MP_QSTR_group_get_default, (const byte*)"\x47\x11" "group_get_default")
QDEF(MP_QSTR_group_remove_obj, (const byte*)"\x1b\x10" "group_remove_obj")
QDEF(MP_QSTR_group_t, (const byte*)"\x31\x07" "group_t")
QDEF(MP_QSTR_gwaddr, (const byte*)"\x06\x06" "gwaddr")
QDEF(MP_QSTR_h, (const byte*)"\xcd\x01" "h")
QDEF(MP_QSTR_hangup, (const byte*)"\xe0\x06" "hangup")
QDEF(MP_QSTR_has_btn_ctrl, (const byte*)"\x4e\x0c" "has_btn_ctrl")
QDEF(MP_QSTR_has_cell_ctrl, (const byte*)"\x50\x0d" "has_cell_ctrl")
QDEF(MP_QSTR_has_class, (const byte*)"\x8e\x09" "has_class")
QDEF(MP_QSTR_has_flag, (const byte*)"\xac\x08" "has_flag")
QDEF(MP_QSTR_has_flag_any, (const byte*)"\x65\x0c" "has_flag_any")
QDEF(MP_QSTR_has_group, (const byte*)"\x9f\x09" "has_group")
QDEF(MP_QSTR_has_state, (const byte*)"\xf7\x09" "has_state")
QDEF(MP_QSTR_hash_base64, (const byte*)"\x3f\x0b" "hash_base64")
QDEF(MP_QSTR_hash_sha1, (const byte*)"\xc3\x09" "hash_sha1")
QDEF(MP_QSTR_head, (const byte*)"\xed\x04" "head")
QDEF(MP_QSTR_header, (const byte*)"\x9a\x06" "header")
QDEF(MP_QSTR_heap_lock, (const byte*)"\xad\x09" "heap_lock")
QDEF(MP_QSTR_heap_unlock, (const byte*)"\x56\x0b" "heap_unlock")
QDEF(MP_QSTR_height_def, (const byte*)"\x02\x0a" "height_def")
QDEF(MP_QSTR_help, (const byte*)"\x94\x04" "help")
QDEF(MP_QSTR_hex, (const byte*)"\x70\x03" "hex")
QDEF(MP_QSTR_hexlify, (const byte*)"\x2a\x07" "hexlify")
QDEF(MP_QSTR_hidden, (const byte*)"\xef\x06" "hidden")
QDEF(MP_QSTR_hide_series, (const byte*)"\xe1\x0b" "hide_series")
QDEF(MP_QSTR_hight, (const byte*)"\xff\x05" "hight")
QDEF(MP_QSTR_hit_test, (const byte*)"\x59\x08" "hit_test")
QDEF(MP_QSTR_hit_test_info_t, (const byte*)"\x83\x0f" "hit_test_info_t")
QDEF(MP_QSTR_hor_res, (const byte*)"\xcb\x07" "hor_res")
QDEF(MP_QSTR_httpDownload, (const byte*)"\xe9\x0c" "httpDownload")
QDEF(MP_QSTR_i, (const byte*)"\xcc\x01" "i")
QDEF(MP_QSTR_ilistdir, (const byte*)"\x71\x08" "ilistdir")
QDEF(MP_QSTR_imag, (const byte*)"\x47\x04" "imag")
QDEF(MP_QSTR_ime_pinyin, (const byte*)"\xd2\x0a" "ime_pinyin")
QDEF(MP_QSTR_ime_pinyin_class, (const byte*)"\x63\x10" "ime_pinyin_class")
QDEF(MP_QSTR_img, (const byte*)"\x26\x03" "img")
QDEF(MP_QSTR_img_class, (const byte*)"\x97\x09" "img_class")
QDEF(MP_QSTR_img_data, (const byte*)"\xa9\x08" "img_data")
QDEF(MP_QSTR_img_decoder_dsc_t, (const byte*)"\xe7\x11" "img_decoder_dsc_t")
QDEF(MP_QSTR_img_decoder_t, (const byte*)"\x4c\x0d" "img_decoder_t")
QDEF(MP_QSTR_img_dsc, (const byte*)"\x8d\x07" "img_dsc")
QDEF(MP_QSTR_img_dsc_t, (const byte*)"\xc6\x09" "img_dsc_t")
QDEF(MP_QSTR_img_header_t, (const byte*)"\xad\x0c" "img_header_t")
QDEF(MP_QSTR_img_src, (const byte*)"\xfb\x07" "img_src")
QDEF(MP_QSTR_imgbtn, (const byte*)"\x1e\x06" "imgbtn")
QDEF(MP_QSTR_imgbtn_class, (const byte*)"\xaf\x0c" "imgbtn_class")
QDEF(MP_QSTR_implementation, (const byte*)"\x17\x0e" "implementation")
QDEF(MP_QSTR_increase, (const byte*)"\x41\x08" "increase")
QDEF(MP_QSTR_increment, (const byte*)"\xe4\x09" "increment")
QDEF(MP_QSTR_indev_data_t, (const byte*)"\x11\x0c" "indev_data_t")
QDEF(MP_QSTR_indev_drv_t, (const byte*)"\xa1\x0b" "indev_drv_t")
QDEF(MP_QSTR_indev_get_act, (const byte*)"\xf5\x0d" "indev_get_act")
QDEF(MP_QSTR_indev_get_obj_act, (const byte*)"\xed\x11" "indev_get_obj_act")
QDEF(MP_QSTR_indev_get_read_timer, (const byte*)"\x29\x14" "indev_get_read_timer")
QDEF(MP_QSTR_indev_read_timer_cb, (const byte*)"\x9e\x13" "indev_read_timer_cb")
QDEF(MP_QSTR_indev_search_obj, (const byte*)"\x7c\x10" "indev_search_obj")
QDEF(MP_QSTR_indev_t, (const byte*)"\xfe\x07" "indev_t")
QDEF(MP_QSTR_indices, (const byte*)"\x5a\x07" "indices")
QDEF(MP_QSTR_inet_ntop, (const byte*)"\x89\x09" "inet_ntop")
QDEF(MP_QSTR_inet_pton, (const byte*)"\xc9\x09" "inet_pton")
QDEF(MP_QSTR_info_cb, (const byte*)"\xb5\x07" "info_cb")
QDEF(MP_QSTR_init, (const byte*)"\x1f\x04" "init")
QDEF(MP_QSTR_init_draw_arc_dsc, (const byte*)"\xe4\x11" "init_draw_arc_dsc")
QDEF(MP_QSTR_init_draw_img_dsc, (const byte*)"\x17\x11" "init_draw_img_dsc")
QDEF(MP_QSTR_init_draw_label_dsc, (const byte*)"\x32\x13" "init_draw_label_dsc")
QDEF(MP_QSTR_init_draw_line_dsc, (const byte*)"\xfa\x12" "init_draw_line_dsc")
QDEF(MP_QSTR_init_draw_rect_dsc, (const byte*)"\xd4\x12" "init_draw_rect_dsc")
QDEF(MP_QSTR_initbuf, (const byte*)"\x2e\x07" "initbuf")
QDEF(MP_QSTR_input, (const byte*)"\x73\x05" "input")
QDEF(MP_QSTR_ins_text, (const byte*)"\xf3\x08" "ins_text")
QDEF(MP_QSTR_instance_size, (const byte*)"\x76\x0d" "instance_size")
QDEF(MP_QSTR_int_val, (const byte*)"\xf2\x07" "int_val")
QDEF(MP_QSTR_intersection, (const byte*)"\x28\x0c" "intersection")
QDEF(MP_QSTR_intersection_update, (const byte*)"\x06\x13" "intersection_update")
QDEF(MP_QSTR_inv, (const byte*)"\x14\x03" "inv")
QDEF(MP_QSTR_inv_area_joined, (const byte*)"\x40\x0f" "inv_area_joined")
QDEF(MP_QSTR_inv_areas, (const byte*)"\xaf\x09" "inv_areas")
QDEF(MP_QSTR_inv_p, (const byte*)"\x5b\x05" "inv_p")
QDEF(MP_QSTR_invaildbuf, (const byte*)"\xe5\x0a" "invaildbuf")
QDEF(MP_QSTR_invalidate, (const byte*)"\x64\x0a" "invalidate")
QDEF(MP_QSTR_invalidate_area, (const byte*)"\x2c\x0f" "invalidate_area")
QDEF(MP_QSTR_invert, (const byte*)"\xb7\x06" "invert")
QDEF(MP_QSTR_ioctl, (const byte*)"\x78\x05" "ioctl")
QDEF(MP_QSTR_ipaddr, (const byte*)"\x8f\x06" "ipaddr")
QDEF(MP_QSTR_ipconfig, (const byte*)"\xf6\x08" "ipconfig")
QDEF(MP_QSTR_ipoll, (const byte*)"\x53\x05" "ipoll")
QDEF(MP_QSTR_isOk, (const byte*)"\x3b\x04" "isOk")
QDEF(MP_QSTR_is_char_under_pos, (const byte*)"\x9c\x11" "is_char_under_pos")
QDEF(MP_QSTR_is_dragged, (const byte*)"\x76\x0a" "is_dragged")
QDEF(MP_QSTR_is_editable, (const byte*)"\x36\x0b" "is_editable")
QDEF(MP_QSTR_is_empty, (const byte*)"\x55\x08" "is_empty")
QDEF(MP_QSTR_is_group_def, (const byte*)"\xe7\x0c" "is_group_def")
QDEF(MP_QSTR_is_layout_positioned, (const byte*)"\xfd\x14" "is_layout_positioned")
QDEF(MP_QSTR_is_scrolling, (const byte*)"\xed\x0c" "is_scrolling")
QDEF(MP_QSTR_is_valid, (const byte*)"\x36\x08" "is_valid")
QDEF(MP_QSTR_is_visible, (const byte*)"\x6e\x0a" "is_visible")
QDEF(MP_QSTR_isconnected, (const byte*)"\x80\x0b" "isconnected")
QDEF(MP_QSTR_isdisjoint, (const byte*)"\xf7\x0a" "isdisjoint")
QDEF(MP_QSTR_isenabled, (const byte*)"\x9a\x09" "isenabled")
QDEF(MP_QSTR_isfinite, (const byte*)"\xa6\x08" "isfinite")
QDEF(MP_QSTR_isinf, (const byte*)"\x3e\x05" "isinf")
QDEF(MP_QSTR_isnan, (const byte*)"\x9e\x05" "isnan")
QDEF(MP_QSTR_issubset, (const byte*)"\xb9\x08" "issubset")
QDEF(MP_QSTR_issuperset, (const byte*)"\xfc\x0a" "issuperset")
QDEF(MP_QSTR_iterable, (const byte*)"\x25\x08" "iterable")
QDEF(MP_QSTR_iterator, (const byte*)"\x47\x08" "iterator")
QDEF(MP_QSTR_kbd_intr, (const byte*)"\xf6\x08" "kbd_intr")
QDEF(MP_QSTR_keyboard, (const byte*)"\x68\x08" "keyboard")
QDEF(MP_QSTR_keyboard_class, (const byte*)"\xd9\x0e" "keyboard_class")
QDEF(MP_QSTR_keypad, (const byte*)"\xe7\x06" "keypad")
QDEF(MP_QSTR_label, (const byte*)"\x43\x05" "label")
QDEF(MP_QSTR_label_class, (const byte*)"\xb2\x0b" "label_class")
QDEF(MP_QSTR_label_color, (const byte*)"\x01\x0b" "label_color")
QDEF(MP_QSTR_label_dsc, (const byte*)"\xa8\x09" "label_dsc")
QDEF(MP_QSTR_label_gap, (const byte*)"\x2a\x09" "label_gap")
QDEF(MP_QSTR_last_activity_time, (const byte*)"\xf7\x12" "last_activity_time")
QDEF(MP_QSTR_last_area, (const byte*)"\xc7\x09" "last_area")
QDEF(MP_QSTR_last_key, (const byte*)"\xc7\x08" "last_key")
QDEF(MP_QSTR_last_obj, (const byte*)"\x77\x08" "last_obj")
QDEF(MP_QSTR_last_part, (const byte*)"\x07\x09" "last_part")
QDEF(MP_QSTR_last_point, (const byte*)"\x1c\x0a" "last_point")
QDEF(MP_QSTR_last_pressed, (const byte*)"\xf6\x0c" "last_pressed")
QDEF(MP_QSTR_last_raw_point, (const byte*)"\x67\x0e" "last_raw_point")
QDEF(MP_QSTR_last_run, (const byte*)"\x99\x08" "last_run")
QDEF(MP_QSTR_last_state, (const byte*)"\x27\x0a" "last_state")
QDEF(MP_QSTR_layer_sys, (const byte*)"\x20\x09" "layer_sys")
QDEF(MP_QSTR_layer_top, (const byte*)"\xb2\x09" "layer_top")
QDEF(MP_QSTR_layout_register, (const byte*)"\xc9\x0f" "layout_register")
QDEF(MP_QSTR_lcd_brightness, (const byte*)"\x78\x0e" "lcd_brightness")
QDEF(MP_QSTR_lcd_clear, (const byte*)"\xc8\x09" "lcd_clear")
QDEF(MP_QSTR_lcd_display_off, (const byte*)"\x3b\x0f" "lcd_display_off")
QDEF(MP_QSTR_lcd_display_on, (const byte*)"\xb5\x0e" "lcd_display_on")
QDEF(MP_QSTR_lcd_init, (const byte*)"\x2b\x08" "lcd_init")
QDEF(MP_QSTR_lcd_show, (const byte*)"\xb2\x08" "lcd_show")
QDEF(MP_QSTR_lcd_write, (const byte*)"\xec\x09" "lcd_write")
QDEF(MP_QSTR_lcd_write_cmd, (const byte*)"\x59\x0d" "lcd_write_cmd")
QDEF(MP_QSTR_lcd_write_data, (const byte*)"\x23\x0e" "lcd_write_data")
QDEF(MP_QSTR_ldexp, (const byte*)"\x40\x05" "ldexp")
QDEF(MP_QSTR_lease_time, (const byte*)"\x11\x0a" "lease_time")
QDEF(MP_QSTR_led, (const byte*)"\x68\x03" "led")
QDEF(MP_QSTR_led_class, (const byte*)"\xd9\x09" "led_class")
QDEF(MP_QSTR_letter, (const byte*)"\x7b\x06" "letter")
QDEF(MP_QSTR_letter_space, (const byte*)"\x80\x0c" "letter_space")
QDEF(MP_QSTR_lgamma, (const byte*)"\xce\x06" "lgamma")
QDEF(MP_QSTR_lightbuf, (const byte*)"\xca\x08" "lightbuf")
QDEF(MP_QSTR_line, (const byte*)"\xcb\x04" "line")
QDEF(MP_QSTR_line_class, (const byte*)"\x3a\x0a" "line_class")
QDEF(MP_QSTR_line_dsc, (const byte*)"\x20\x08" "line_dsc")
QDEF(MP_QSTR_line_height, (const byte*)"\x8b\x0b" "line_height")
QDEF(MP_QSTR_line_space, (const byte*)"\xb0\x0a" "line_space")
QDEF(MP_QSTR_line_start, (const byte*)"\x54\x0a" "line_start")
QDEF(MP_QSTR_linenum, (const byte*)"\xfd\x07" "linenum")
QDEF(MP_QSTR_list_btn_class, (const byte*)"\x11\x0e" "list_btn_class")
QDEF(MP_QSTR_list_class, (const byte*)"\xd6\x0a" "list_class")
QDEF(MP_QSTR_list_text_class, (const byte*)"\x34\x0f" "list_text_class")
QDEF(MP_QSTR_listdir, (const byte*)"\x98\x07" "listdir")
QDEF(MP_QSTR_listen, (const byte*)"\xcc\x06" "listen")
QDEF(MP_QSTR_ll_t, (const byte*)"\xce\x04" "ll_t")
QDEF(MP_QSTR_load, (const byte*)"\x63\x04" "load")
QDEF(MP_QSTR_loads, (const byte*)"\xb0\x05" "loads")
QDEF(MP_QSTR_local_grad, (const byte*)"\xc7\x0a" "local_grad")
QDEF(MP_QSTR_localtime, (const byte*)"\x7d\x09" "localtime")
QDEF(MP_QSTR_localtime_ex, (const byte*)"\xbf\x0c" "localtime_ex")
QDEF(MP_QSTR_lock, (const byte*)"\xae\x04" "lock")
QDEF(MP_QSTR_locked, (const byte*)"\x0f\x06" "locked")
QDEF(MP_QSTR_log, (const byte*)"\x21\x03" "log")
QDEF(MP_QSTR_log10, (const byte*)"\x40\x05" "log10")
QDEF(MP_QSTR_log2, (const byte*)"\x73\x04" "log2")
QDEF(MP_QSTR_log_register_print_cb, (const byte*)"\x67\x15" "log_register_print_cb")
QDEF(MP_QSTR_long_pr_sent, (const byte*)"\x81\x0c" "long_pr_sent")
QDEF(MP_QSTR_long_press_repeat_time, (const byte*)"\xb5\x16" "long_press_repeat_time")
QDEF(MP_QSTR_long_press_time, (const byte*)"\x7d\x0f" "long_press_time")
QDEF(MP_QSTR_longpr_rep_timestamp, (const byte*)"\xc4\x14" "longpr_rep_timestamp")
QDEF(MP_QSTR_lookahead, (const byte*)"\xeb\x09" "lookahead")
QDEF(MP_QSTR_lrc, (const byte*)"\x98\x03" "lrc")
QDEF(MP_QSTR_lv_anim_t, (const byte*)"\x20\x09" "lv_anim_t")
QDEF(MP_QSTR_lv_anim_t_exec_cb, (const byte*)"\x1a\x11" "lv_anim_t_exec_cb")
QDEF(MP_QSTR_lv_anim_t_get_value_cb, (const byte*)"\xc3\x16" "lv_anim_t_get_value_cb")
QDEF(MP_QSTR_lv_anim_t_path_cb, (const byte*)"\x4c\x11" "lv_anim_t_path_cb")
QDEF(MP_QSTR_lv_anim_t_ready_cb, (const byte*)"\xea\x12" "lv_anim_t_ready_cb")
QDEF(MP_QSTR_lv_anim_t_start_cb, (const byte*)"\xa1\x12" "lv_anim_t_start_cb")
QDEF(MP_QSTR_lv_area_t, (const byte*)"\xbc\x09" "lv_area_t")
QDEF(MP_QSTR_lv_async_call_async_xcb, (const byte*)"\x04\x17" "lv_async_call_async_xcb")
QDEF(MP_QSTR_lv_calendar_date_t, (const byte*)"\xb2\x12" "lv_calendar_date_t")
QDEF(MP_QSTR_lv_chart_cursor_t, (const byte*)"\xd2\x11" "lv_chart_cursor_t")
QDEF(MP_QSTR_lv_chart_series_t, (const byte*)"\x03\x11" "lv_chart_series_t")
QDEF(MP_QSTR_lv_color32_ch_t, (const byte*)"\x03\x0f" "lv_color32_ch_t")
QDEF(MP_QSTR_lv_color32_t, (const byte*)"\xb7\x0c" "lv_color32_t")
QDEF(MP_QSTR_lv_color_filter_dsc_t, (const byte*)"\xa2\x15" "lv_color_filter_dsc_t")
QDEF(MP_QSTR_lv_color_filter_dsc_t_cb, (const byte*)"\x9c\x18" "lv_color_filter_dsc_t_cb")
QDEF(MP_QSTR_lv_color_filter_dsc_t_filter_cb, (const byte*)"\x43\x1f" "lv_color_filter_dsc_t_filter_cb")
QDEF(MP_QSTR_lv_color_hsv_t, (const byte*)"\xa4\x0e" "lv_color_hsv_t")
QDEF(MP_QSTR_lv_disp_draw_buf_t, (const byte*)"\x54\x12" "lv_disp_draw_buf_t")
QDEF(MP_QSTR_lv_disp_drv_t, (const byte*)"\x5a\x0d" "lv_disp_drv_t")
QDEF(MP_QSTR_lv_disp_drv_t_clean_dcache_cb, (const byte*)"\x09\x1d" "lv_disp_drv_t_clean_dcache_cb")
QDEF(MP_QSTR_lv_disp_drv_t_drv_update_cb, (const byte*)"\x55\x1b" "lv_disp_drv_t_drv_update_cb")
QDEF(MP_QSTR_lv_disp_drv_t_flush_cb, (const byte*)"\xbf\x16" "lv_disp_drv_t_flush_cb")
QDEF(MP_QSTR_lv_disp_drv_t_gpu_fill_cb, (const byte*)"\xa9\x19" "lv_disp_drv_t_gpu_fill_cb")
QDEF(MP_QSTR_lv_disp_drv_t_gpu_wait_cb, (const byte*)"\xed\x19" "lv_disp_drv_t_gpu_wait_cb")
QDEF(MP_QSTR_lv_disp_drv_t_monitor_cb, (const byte*)"\xf7\x18" "lv_disp_drv_t_monitor_cb")
QDEF(MP_QSTR_lv_disp_drv_t_rounder_cb, (const byte*)"\xee\x18" "lv_disp_drv_t_rounder_cb")
QDEF(MP_QSTR_lv_disp_drv_t_set_px_cb, (const byte*)"\xae\x17" "lv_disp_drv_t_set_px_cb")
QDEF(MP_QSTR_lv_disp_drv_t_wait_cb, (const byte*)"\x30\x15" "lv_disp_drv_t_wait_cb")
QDEF(MP_QSTR_lv_disp_t, (const byte*)"\x45\x09" "lv_disp_t")
QDEF(MP_QSTR_lv_draw_arc_dsc_t, (const byte*)"\x6f\x11" "lv_draw_arc_dsc_t")
QDEF(MP_QSTR_lv_draw_img_dsc_t, (const byte*)"\x5c\x11" "lv_draw_img_dsc_t")
QDEF(MP_QSTR_lv_draw_label_dsc_t, (const byte*)"\x39\x13" "lv_draw_label_dsc_t")
QDEF(MP_QSTR_lv_draw_label_hint_t, (const byte*)"\x76\x14" "lv_draw_label_hint_t")
QDEF(MP_QSTR_lv_draw_line_dsc_t, (const byte*)"\xf1\x12" "lv_draw_line_dsc_t")
QDEF(MP_QSTR_lv_draw_mask_angle_param_cfg_t, (const byte*)"\x53\x1e" "lv_draw_mask_angle_param_cfg_t")
QDEF(MP_QSTR_lv_draw_mask_angle_param_t, (const byte*)"\xce\x1a" "lv_draw_mask_angle_param_t")
QDEF(MP_QSTR_lv_draw_mask_fade_param_cfg_t, (const byte*)"\xf4\x1d" "lv_draw_mask_fade_param_cfg_t")
QDEF(MP_QSTR_lv_draw_mask_fade_param_t, (const byte*)"\x29\x19" "lv_draw_mask_fade_param_t")
QDEF(MP_QSTR_lv_draw_mask_line_param_cfg_t, (const byte*)"\xfc\x1d" "lv_draw_mask_line_param_cfg_t")
QDEF(MP_QSTR_lv_draw_mask_line_param_t, (const byte*)"\x21\x19" "lv_draw_mask_line_param_t")
QDEF(MP_QSTR_lv_draw_mask_map_param_cfg_t, (const byte*)"\x6e\x1c" "lv_draw_mask_map_param_cfg_t")
QDEF(MP_QSTR_lv_draw_mask_map_param_t, (const byte*)"\xb3\x18" "lv_draw_mask_map_param_t")
QDEF(MP_QSTR_lv_draw_mask_radius_param_cfg_t, (const byte*)"\x0a\x1f" "lv_draw_mask_radius_param_cfg_t")
QDEF(MP_QSTR_lv_draw_mask_radius_param_t, (const byte*)"\x97\x1b" "lv_draw_mask_radius_param_t")
QDEF(MP_QSTR_lv_draw_rect_dsc_t, (const byte*)"\x9f\x12" "lv_draw_rect_dsc_t")
QDEF(MP_QSTR_lv_event_t, (const byte*)"\x87\x0a" "lv_event_t")
QDEF(MP_QSTR_lv_font_glyph_dsc_t, (const byte*)"\xc6\x13" "lv_font_glyph_dsc_t")
QDEF(MP_QSTR_lv_font_t, (const byte*)"\xb8\x09" "lv_font_t")
QDEF(MP_QSTR_lv_font_t_get_glyph_bitmap, (const byte*)"\xf8\x1a" "lv_font_t_get_glyph_bitmap")
QDEF(MP_QSTR_lv_font_t_get_glyph_dsc, (const byte*)"\x6f\x17" "lv_font_t_get_glyph_dsc")
QDEF(MP_QSTR_lv_fs_dir_t, (const byte*)"\x1e\x0b" "lv_fs_dir_t")
QDEF(MP_QSTR_lv_fs_drv_t, (const byte*)"\xa1\x0b" "lv_fs_drv_t")
QDEF(MP_QSTR_lv_fs_drv_t_close_cb, (const byte*)"\xf6\x14" "lv_fs_drv_t_close_cb")
QDEF(MP_QSTR_lv_fs_drv_t_dir_close_cb, (const byte*)"\x16\x18" "lv_fs_drv_t_dir_close_cb")
QDEF(MP_QSTR_lv_fs_drv_t_dir_open_cb, (const byte*)"\x94\x17" "lv_fs_drv_t_dir_open_cb")
QDEF(MP_QSTR_lv_fs_drv_t_dir_read_cb, (const byte*)"\x72\x17" "lv_fs_drv_t_dir_read_cb")
QDEF(MP_QSTR_lv_fs_drv_t_open_cb, (const byte*)"\x34\x13" "lv_fs_drv_t_open_cb")
QDEF(MP_QSTR_lv_fs_drv_t_read_cb, (const byte*)"\x92\x13" "lv_fs_drv_t_read_cb")
QDEF(MP_QSTR_lv_fs_drv_t_ready_cb, (const byte*)"\x0b\x14" "lv_fs_drv_t_ready_cb")
QDEF(MP_QSTR_lv_fs_drv_t_seek_cb, (const byte*)"\x78\x13" "lv_fs_drv_t_seek_cb")
QDEF(MP_QSTR_lv_fs_drv_t_tell_cb, (const byte*)"\xd1\x13" "lv_fs_drv_t_tell_cb")
QDEF(MP_QSTR_lv_fs_drv_t_write_cb, (const byte*)"\x3d\x14" "lv_fs_drv_t_write_cb")
QDEF(MP_QSTR_lv_fs_file_t, (const byte*)"\xc7\x0c" "lv_fs_file_t")
QDEF(MP_QSTR_lv_group_t, (const byte*)"\xf4\x0a" "lv_group_t")
QDEF(MP_QSTR_lv_group_t_focus_cb, (const byte*)"\x39\x13" "lv_group_t_focus_cb")
QDEF(MP_QSTR_lv_hit_test_info_t, (const byte*)"\x46\x12" "lv_hit_test_info_t")
QDEF(MP_QSTR_lv_img_decoder_dsc_t, (const byte*)"\xa2\x14" "lv_img_decoder_dsc_t")
QDEF(MP_QSTR_lv_img_decoder_t, (const byte*)"\x49\x10" "lv_img_decoder_t")
QDEF(MP_QSTR_lv_img_decoder_t_close_cb, (const byte*)"\xde\x19" "lv_img_decoder_t_close_cb")
QDEF(MP_QSTR_lv_img_decoder_t_info_cb, (const byte*)"\x66\x18" "lv_img_decoder_t_info_cb")
QDEF(MP_QSTR_lv_img_decoder_t_open_cb, (const byte*)"\xdc\x18" "lv_img_decoder_t_open_cb")
QDEF(MP_QSTR_lv_img_decoder_t_read_line_cb, (const byte*)"\x8b\x1d" "lv_img_decoder_t_read_line_cb")
QDEF(MP_QSTR_lv_img_dsc_t, (const byte*)"\xc3\x0c" "lv_img_dsc_t")
QDEF(MP_QSTR_lv_img_header_t, (const byte*)"\x48\x0f" "lv_img_header_t")
QDEF(MP_QSTR_lv_indev_data_t, (const byte*)"\x74\x0f" "lv_indev_data_t")
QDEF(MP_QSTR_lv_indev_drv_t, (const byte*)"\x64\x0e" "lv_indev_drv_t")
QDEF(MP_QSTR_lv_indev_drv_t_feedback_cb, (const byte*)"\x8c\x1a" "lv_indev_drv_t_feedback_cb")
QDEF(MP_QSTR_lv_indev_drv_t_read_cb, (const byte*)"\xd7\x16" "lv_indev_drv_t_read_cb")
QDEF(MP_QSTR_lv_indev_t, (const byte*)"\x3b\x0a" "lv_indev_t")
QDEF(MP_QSTR_lv_layout_register_cb, (const byte*)"\xb2\x15" "lv_layout_register_cb")
QDEF(MP_QSTR_lv_ll_t, (const byte*)"\xab\x07" "lv_ll_t")
QDEF(MP_QSTR_lv_log_print_g_cb_t_print_cb, (const byte*)"\x57\x1c" "lv_log_print_g_cb_t_print_cb")
QDEF(MP_QSTR_lv_mem_monitor_t, (const byte*)"\xdd\x10" "lv_mem_monitor_t")
QDEF(MP_QSTR_lv_meter_indicator_t, (const byte*)"\x3e\x14" "lv_meter_indicator_t")
QDEF(MP_QSTR_lv_meter_indicator_type_data_arc_t, (const byte*)"\xd9\x22" "lv_meter_indicator_type_data_arc_t")
QDEF(MP_QSTR_lv_meter_indicator_type_data_needle_img_t, (const byte*)"\xb6\x29" "lv_meter_indicator_type_data_needle_img_t")
QDEF(MP_QSTR_lv_meter_indicator_type_data_needle_line_t, (const byte*)"\x7b\x2a" "lv_meter_indicator_type_data_needle_line_t")
QDEF(MP_QSTR_lv_meter_indicator_type_data_scale_lines_t, (const byte*)"\x33\x2a" "lv_meter_indicator_type_data_scale_lines_t")
QDEF(MP_QSTR_lv_meter_indicator_type_data_t, (const byte*)"\xd6\x1e" "lv_meter_indicator_type_data_t")
QDEF(MP_QSTR_lv_meter_scale_t, (const byte*)"\xe7\x10" "lv_meter_scale_t")
QDEF(MP_QSTR_lv_obj_add_event_cb_event_cb, (const byte*)"\x39\x1c" "lv_obj_add_event_cb_event_cb")
QDEF(MP_QSTR_lv_obj_class_t, (const byte*)"\x7d\x0e" "lv_obj_class_t")
QDEF(MP_QSTR_lv_obj_class_t_constructor_cb, (const byte*)"\x94\x1d" "lv_obj_class_t_constructor_cb")
QDEF(MP_QSTR_lv_obj_class_t_destructor_cb, (const byte*)"\xf7\x1c" "lv_obj_class_t_destructor_cb")
QDEF(MP_QSTR_lv_obj_class_t_event_cb, (const byte*)"\xd0\x17" "lv_obj_class_t_event_cb")
QDEF(MP_QSTR_lv_obj_draw_part_dsc_t, (const byte*)"\xb0\x16" "lv_obj_draw_part_dsc_t")
QDEF(MP_QSTR_lv_obj_t_event_cb, (const byte*)"\xc1\x11" "lv_obj_t_event_cb")
QDEF(MP_QSTR_lv_obj_tree_walk_cb, (const byte*)"\x8e\x13" "lv_obj_tree_walk_cb")
QDEF(MP_QSTR_lv_pinyin_dict_t, (const byte*)"\x47\x10" "lv_pinyin_dict_t")
QDEF(MP_QSTR_lv_point_t, (const byte*)"\x27\x0a" "lv_point_t")
QDEF(MP_QSTR_lv_span_t, (const byte*)"\xe7\x09" "lv_span_t")
QDEF(MP_QSTR_lv_sqrt_res_t, (const byte*)"\xf4\x0d" "lv_sqrt_res_t")
QDEF(MP_QSTR_lv_style_const_prop_t, (const byte*)"\xc4\x15" "lv_style_const_prop_t")
QDEF(MP_QSTR_lv_style_t, (const byte*)"\x1c\x0a" "lv_style_t")
QDEF(MP_QSTR_lv_style_transition_dsc_init_path_cb, (const byte*)"\x05\x24" "lv_style_transition_dsc_init_path_cb")
QDEF(MP_QSTR_lv_style_transition_dsc_t, (const byte*)"\x27\x19" "lv_style_transition_dsc_t")
QDEF(MP_QSTR_lv_style_transition_dsc_t_path_xcb, (const byte*)"\x33\x22" "lv_style_transition_dsc_t_path_xcb")
QDEF(MP_QSTR_lv_style_v_p_t, (const byte*)"\x1a\x0e" "lv_style_v_p_t")
QDEF(MP_QSTR_lv_style_value_t, (const byte*)"\x48\x10" "lv_style_value_t")
QDEF(MP_QSTR_lv_theme_t, (const byte*)"\x7a\x0a" "lv_theme_t")
QDEF(MP_QSTR_lv_theme_t_apply_cb, (const byte*)"\xef\x13" "lv_theme_t_apply_cb")
QDEF(MP_QSTR_lv_timer_create_timer_xcb, (const byte*)"\x02\x19" "lv_timer_create_timer_xcb")
QDEF(MP_QSTR_lv_timer_t, (const byte*)"\x4c\x0a" "lv_timer_t")
QDEF(MP_QSTR_lv_timer_t_timer_cb, (const byte*)"\xea\x13" "lv_timer_t_timer_cb")
QDEF(MP_QSTR_lvgl, (const byte*)"\x74\x04" "lvgl")
QDEF(MP_QSTR_mac, (const byte*)"\xaa\x03" "mac")
QDEF(MP_QSTR_machine, (const byte*)"\x60\x07" "machine")
QDEF(MP_QSTR_makefile, (const byte*)"\xc1\x08" "makefile")
QDEF(MP_QSTR_mark_layout_as_dirty, (const byte*)"\x55\x14" "mark_layout_as_dirty")
QDEF(MP_QSTR_maskaddr, (const byte*)"\x22\x08" "maskaddr")
QDEF(MP_QSTR_match, (const byte*)"\x96\x05" "match")
QDEF(MP_QSTR_math, (const byte*)"\x35\x04" "math")
QDEF(MP_QSTR_max, (const byte*)"\xb1\x03" "max")
QDEF(MP_QSTR_maxCnt, (const byte*)"\x48\x06" "maxCnt")
QDEF(MP_QSTR_max_used, (const byte*)"\x49\x08" "max_used")
QDEF(MP_QSTR_maxsize, (const byte*)"\xd4\x07" "maxsize")
QDEF(MP_QSTR_md5, (const byte*)"\x19\x03" "md5")
QDEF(MP_QSTR_mem, (const byte*)"\x20\x03" "mem")
QDEF(MP_QSTR_mem_alloc, (const byte*)"\x52\x09" "mem_alloc")
QDEF(MP_QSTR_mem_buf_free_all, (const byte*)"\xbb\x10" "mem_buf_free_all")
QDEF(MP_QSTR_mem_buf_get, (const byte*)"\xe7\x0b" "mem_buf_get")
QDEF(MP_QSTR_mem_buf_release, (const byte*)"\xb8\x0f" "mem_buf_release")
QDEF(MP_QSTR_mem_deinit, (const byte*)"\xe4\x0a" "mem_deinit")
QDEF(MP_QSTR_mem_free, (const byte*)"\xcb\x08" "mem_free")
QDEF(MP_QSTR_mem_init, (const byte*)"\x65\x08" "mem_init")
QDEF(MP_QSTR_mem_monitor_t, (const byte*)"\x58\x0d" "mem_monitor_t")
QDEF(MP_QSTR_mem_realloc, (const byte*)"\xa5\x0b" "mem_realloc")
QDEF(MP_QSTR_mem_test, (const byte*)"\x09\x08" "mem_test")
QDEF(MP_QSTR_memcpy, (const byte*)"\x4a\x06" "memcpy")
QDEF(MP_QSTR_memcpy_small, (const byte*)"\xea\x0c" "memcpy_small")
QDEF(MP_QSTR_memoryview, (const byte*)"\x69\x0a" "memoryview")
QDEF(MP_QSTR_memset, (const byte*)"\xe2\x06" "memset")
QDEF(MP_QSTR_memset_00, (const byte*)"\xfd\x09" "memset_00")
QDEF(MP_QSTR_memset_ff, (const byte*)"\xbd\x09" "memset_ff")
QDEF(MP_QSTR_meter, (const byte*)"\xce\x05" "meter")
QDEF(MP_QSTR_meter_class, (const byte*)"\x7f\x0b" "meter_class")
QDEF(MP_QSTR_meter_indicator_t, (const byte*)"\xfb\x11" "meter_indicator_t")
QDEF(MP_QSTR_meter_indicator_type_data_arc_t, (const byte*)"\x9c\x1f" "meter_indicator_type_data_arc_t")
QDEF(MP_QSTR_meter_indicator_type_data_needle_img_t, (const byte*)"\x53\x26" "meter_indicator_type_data_needle_img_t")
QDEF(MP_QSTR_meter_indicator_type_data_needle_line_t, (const byte*)"\x3e\x27" "meter_indicator_type_data_needle_line_t")
QDEF(MP_QSTR_meter_indicator_type_data_scale_lines_t, (const byte*)"\xb6\x27" "meter_indicator_type_data_scale_lines_t")
QDEF(MP_QSTR_meter_indicator_type_data_t, (const byte*)"\x93\x1b" "meter_indicator_type_data_t")
QDEF(MP_QSTR_meter_scale_t, (const byte*)"\xa2\x0d" "meter_scale_t")
QDEF(MP_QSTR_mic_mute, (const byte*)"\xd4\x08" "mic_mute")
QDEF(MP_QSTR_min, (const byte*)"\xaf\x03" "min")
QDEF(MP_QSTR_misc, (const byte*)"\x91\x04" "misc")
QDEF(MP_QSTR_mkdir, (const byte*)"\x9c\x05" "mkdir")
QDEF(MP_QSTR_mkfs, (const byte*)"\x76\x04" "mkfs")
QDEF(MP_QSTR_mktime, (const byte*)"\x96\x06" "mktime")
QDEF(MP_QSTR_mktime_ex, (const byte*)"\xb4\x09" "mktime_ex")
QDEF(MP_QSTR_mode, (const byte*)"\x26\x04" "mode")
QDEF(MP_QSTR_modem, (const byte*)"\x8b\x05" "modem")
QDEF(MP_QSTR_modemConfig, (const byte*)"\xa1\x0b" "modemConfig")
QDEF(MP_QSTR_modemImsConfig, (const byte*)"\xf6\x0e" "modemImsConfig")
QDEF(MP_QSTR_modf, (const byte*)"\x25\x04" "modf")
QDEF(MP_QSTR_modify, (const byte*)"\xf5\x06" "modify")
QDEF(MP_QSTR_module, (const byte*)"\xbf\x06" "module")
QDEF(MP_QSTR_modules, (const byte*)"\xec\x07" "modules")
QDEF(MP_QSTR_monitor, (const byte*)"\x29\x07" "monitor")
QDEF(MP_QSTR_monitor_cb, (const byte*)"\x37\x0a" "monitor_cb")
QDEF(MP_QSTR_month, (const byte*)"\x75\x05" "month")
QDEF(MP_QSTR_mount, (const byte*)"\xa8\x05" "mount")
QDEF(MP_QSTR_move, (const byte*)"\x74\x04" "move")
QDEF(MP_QSTR_move_background, (const byte*)"\xc5\x0f" "move_background")
QDEF(MP_QSTR_move_children_by, (const byte*)"\x9c\x10" "move_children_by")
QDEF(MP_QSTR_move_foreground, (const byte*)"\xf0\x0f" "move_foreground")
QDEF(MP_QSTR_move_to, (const byte*)"\x30\x07" "move_to")
QDEF(MP_QSTR_mpy, (const byte*)"\xc1\x03" "mpy")
QDEF(MP_QSTR_msgbox, (const byte*)"\x29\x06" "msgbox")
QDEF(MP_QSTR_msgbox_class, (const byte*)"\xd8\x0c" "msgbox_class")
QDEF(MP_QSTR_n_size, (const byte*)"\x51\x06" "n_size")
QDEF(MP_QSTR_name, (const byte*)"\xa2\x04" "name")
QDEF(MP_QSTR_namedtuple, (const byte*)"\x1e\x0a" "namedtuple")
QDEF(MP_QSTR_needle_img, (const byte*)"\x1a\x0a" "needle_img")
QDEF(MP_QSTR_needle_line, (const byte*)"\x77\x0b" "needle_line")
QDEF(MP_QSTR_net, (const byte*)"\x7a\x03" "net")
QDEF(MP_QSTR_net_light, (const byte*)"\x1b\x09" "net_light")
QDEF(MP_QSTR_network, (const byte*)"\x5b\x07" "network")
QDEF(MP_QSTR_new_span, (const byte*)"\x0a\x08" "new_span")
QDEF(MP_QSTR_nitzSwitch, (const byte*)"\x5e\x0a" "nitzSwitch")
QDEF(MP_QSTR_nitzTime, (const byte*)"\x19\x08" "nitzTime")
QDEF(MP_QSTR_node, (const byte*)"\xc5\x04" "node")
QDEF(MP_QSTR_nodename, (const byte*)"\x62\x08" "nodename")
QDEF(MP_QSTR_num, (const byte*)"\x73\x03" "num")
QDEF(MP_QSTR_obj, (const byte*)"\x02\x03" "obj")
QDEF(MP_QSTR_obj_class, (const byte*)"\xb3\x09" "obj_class")
QDEF(MP_QSTR_obj_class_t, (const byte*)"\xb8\x0b" "obj_class_t")
QDEF(MP_QSTR_obj_draw_part_dsc_t, (const byte*)"\x35\x13" "obj_draw_part_dsc_t")
QDEF(MP_QSTR_obj_focus, (const byte*)"\x51\x09" "obj_focus")
QDEF(MP_QSTR_obj_ll, (const byte*)"\x9d\x06" "obj_ll")
QDEF(MP_QSTR_oct, (const byte*)"\xfd\x03" "oct")
QDEF(MP_QSTR_off, (const byte*)"\x8a\x03" "off")
QDEF(MP_QSTR_ofs_x, (const byte*)"\x58\x05" "ofs_x")
QDEF(MP_QSTR_ofs_y, (const byte*)"\x59\x05" "ofs_y")
QDEF(MP_QSTR_on, (const byte*)"\x64\x02" "on")
QDEF(MP_QSTR_opa, (const byte*)"\xdb\x03" "opa")
QDEF(MP_QSTR_opa_bottom, (const byte*)"\x0b\x0a" "opa_bottom")
QDEF(MP_QSTR_opa_top, (const byte*)"\xaf\x07" "opa_top")
QDEF(MP_QSTR_open_cb, (const byte*)"\xcf\x07" "open_cb")
QDEF(MP_QSTR_operatorName, (const byte*)"\x82\x0c" "operatorName")
QDEF(MP_QSTR_opt_level, (const byte*)"\x87\x09" "opt_level")
QDEF(MP_QSTR_origo, (const byte*)"\x79\x05" "origo")
QDEF(MP_QSTR_osTimer, (const byte*)"\x9e\x07" "osTimer")
QDEF(MP_QSTR_outer, (const byte*)"\xdc\x05" "outer")
QDEF(MP_QSTR_outline_color, (const byte*)"\xe7\x0d" "outline_color")
QDEF(MP_QSTR_outline_opa, (const byte*)"\xe4\x0b" "outline_opa")
QDEF(MP_QSTR_outline_pad, (const byte*)"\xcf\x0b" "outline_pad")
QDEF(MP_QSTR_outline_width, (const byte*)"\x9c\x0d" "outline_width")
QDEF(MP_QSTR_p1, (const byte*)"\x44\x02" "p1")
QDEF(MP_QSTR_p2, (const byte*)"\x47\x02" "p2")
QDEF(MP_QSTR_pack, (const byte*)"\xbc\x04" "pack")
QDEF(MP_QSTR_pack_into, (const byte*)"\x1f\x09" "pack_into")
QDEF(MP_QSTR_palette_darken, (const byte*)"\xf0\x0e" "palette_darken")
QDEF(MP_QSTR_palette_lighten, (const byte*)"\xd2\x0f" "palette_lighten")
QDEF(MP_QSTR_palette_main, (const byte*)"\x4c\x0c" "palette_main")
QDEF(MP_QSTR_param, (const byte*)"\xca\x05" "param")
QDEF(MP_QSTR_parent, (const byte*)"\x99\x06" "parent")
QDEF(MP_QSTR_parity, (const byte*)"\x42\x06" "parity")
QDEF(MP_QSTR_part, (const byte*)"\xd2\x04" "part")
QDEF(MP_QSTR_password, (const byte*)"\x9a\x08" "password")
QDEF(MP_QSTR_path, (const byte*)"\x88\x04" "path")
QDEF(MP_QSTR_path_bounce, (const byte*)"\x47\x0b" "path_bounce")
QDEF(MP_QSTR_path_cb, (const byte*)"\x36\x07" "path_cb")
QDEF(MP_QSTR_path_ease_in, (const byte*)"\x1d\x0c" "path_ease_in")
QDEF(MP_QSTR_path_ease_in_out, (const byte*)"\xec\x10" "path_ease_in_out")
QDEF(MP_QSTR_path_ease_out, (const byte*)"\xb4\x0d" "path_ease_out")
QDEF(MP_QSTR_path_linear, (const byte*)"\x2a\x0b" "path_linear")
QDEF(MP_QSTR_path_overshoot, (const byte*)"\xb6\x0e" "path_overshoot")
QDEF(MP_QSTR_path_step, (const byte*)"\x85\x09" "path_step")
QDEF(MP_QSTR_path_xcb, (const byte*)"\x2e\x08" "path_xcb")
QDEF(MP_QSTR_pause, (const byte*)"\xd7\x05" "pause")
QDEF(MP_QSTR_paused, (const byte*)"\xd3\x06" "paused")
QDEF(MP_QSTR_pct, (const byte*)"\xa2\x03" "pct")
QDEF(MP_QSTR_peek, (const byte*)"\x7e\x04" "peek")
QDEF(MP_QSTR_pend_throw, (const byte*)"\xf3\x0a" "pend_throw")
QDEF(MP_QSTR_period, (const byte*)"\xa0\x06" "period")
QDEF(MP_QSTR_periodcnt, (const byte*)"\x99\x09" "periodcnt")
QDEF(MP_QSTR_ph_key, (const byte*)"\xf5\x06" "ph_key")
QDEF(MP_QSTR_phase, (const byte*)"\x6a\x05" "phase")
QDEF(MP_QSTR_pi, (const byte*)"\x1c\x02" "pi")
QDEF(MP_QSTR_pin, (const byte*)"\xf2\x03" "pin")
QDEF(MP_QSTR_pinyin_dict_t, (const byte*)"\x82\x0d" "pinyin_dict_t")
QDEF(MP_QSTR_pinyin_get_btn_id, (const byte*)"\xb0\x11" "pinyin_get_btn_id")
QDEF(MP_QSTR_pinyin_get_cand_panel, (const byte*)"\xdb\x15" "pinyin_get_cand_panel")
QDEF(MP_QSTR_pinyin_get_comb_panel, (const byte*)"\xb0\x15" "pinyin_get_comb_panel")
QDEF(MP_QSTR_pinyin_get_dict, (const byte*)"\x01\x0f" "pinyin_get_dict")
QDEF(MP_QSTR_pinyin_get_kb, (const byte*)"\x53\x0d" "pinyin_get_kb")
QDEF(MP_QSTR_pinyin_set_btn_id, (const byte*)"\x24\x11" "pinyin_set_btn_id")
QDEF(MP_QSTR_pinyin_set_dict, (const byte*)"\x94\x0f" "pinyin_set_dict")
QDEF(MP_QSTR_pinyin_set_keyboard, (const byte*)"\x43\x13" "pinyin_set_keyboard")
QDEF(MP_QSTR_pinyin_set_mode, (const byte*)"\x8d\x0f" "pinyin_set_mode")
QDEF(MP_QSTR_pivot, (const byte*)"\x91\x05" "pivot")
QDEF(MP_QSTR_platform, (const byte*)"\x3a\x08" "platform")
QDEF(MP_QSTR_play, (const byte*)"\x21\x04" "play")
QDEF(MP_QSTR_playStream, (const byte*)"\x9d\x0a" "playStream")
QDEF(MP_QSTR_playback_delay, (const byte*)"\x40\x0e" "playback_delay")
QDEF(MP_QSTR_playback_now, (const byte*)"\xa3\x0c" "playback_now")
QDEF(MP_QSTR_playback_time, (const byte*)"\xe0\x0d" "playback_time")
QDEF(MP_QSTR_pm, (const byte*)"\x18\x02" "pm")
QDEF(MP_QSTR_pname, (const byte*)"\x72\x05" "pname")
QDEF(MP_QSTR_png_init, (const byte*)"\xd9\x08" "png_init")
QDEF(MP_QSTR_point, (const byte*)"\xe9\x05" "point")
QDEF(MP_QSTR_point_id, (const byte*)"\x3b\x08" "point_id")
QDEF(MP_QSTR_point_t, (const byte*)"\x62\x07" "point_t")
QDEF(MP_QSTR_pointer, (const byte*)"\x9e\x07" "pointer")
QDEF(MP_QSTR_points_init, (const byte*)"\xff\x0b" "points_init")
QDEF(MP_QSTR_polar, (const byte*)"\x05\x05" "polar")
QDEF(MP_QSTR_poll, (const byte*)"\x9a\x04" "poll")
QDEF(MP_QSTR_pool, (const byte*)"\xb9\x04" "pool")
QDEF(MP_QSTR_pool_end, (const byte*)"\xe9\x08" "pool_end")
QDEF(MP_QSTR_pool_start, (const byte*)"\x66\x0a" "pool_start")
QDEF(MP_QSTR_pop_head, (const byte*)"\x1d\x08" "pop_head")
QDEF(MP_QSTR_popleft, (const byte*)"\x71\x07" "popleft")
QDEF(MP_QSTR_port, (const byte*)"\x5c\x04" "port")
QDEF(MP_QSTR_pos, (const byte*)"\x29\x03" "pos")
QDEF(MP_QSTR_pos_set, (const byte*)"\x54\x07" "pos_set")
QDEF(MP_QSTR_powerDown, (const byte*)"\xc8\x09" "powerDown")
QDEF(MP_QSTR_powerDownReason, (const byte*)"\x2c\x0f" "powerDownReason")
QDEF(MP_QSTR_powerKeyEventRegister, (const byte*)"\x48\x15" "powerKeyEventRegister")
QDEF(MP_QSTR_powerOnReason, (const byte*)"\x3f\x0d" "powerOnReason")
QDEF(MP_QSTR_powerRestart, (const byte*)"\x6d\x0c" "powerRestart")
QDEF(MP_QSTR_pr_timestamp, (const byte*)"\x36\x0c" "pr_timestamp")
QDEF(MP_QSTR_prev, (const byte*)"\xb4\x04" "prev")
QDEF(MP_QSTR_prev_scr, (const byte*)"\x49\x08" "prev_scr")
QDEF(MP_QSTR_print_exception, (const byte*)"\x1c\x0f" "print_exception")
QDEF(MP_QSTR_priority, (const byte*)"\x77\x08" "priority")
QDEF(MP_QSTR_proc, (const byte*)"\xeb\x04" "proc")
QDEF(MP_QSTR_progsize, (const byte*)"\x0a\x08" "progsize")
QDEF(MP_QSTR_prop, (const byte*)"\xf8\x04" "prop")
QDEF(MP_QSTR_prop1, (const byte*)"\xc9\x05" "prop1")
QDEF(MP_QSTR_prop_cnt, (const byte*)"\x3e\x08" "prop_cnt")
QDEF(MP_QSTR_property, (const byte*)"\xc2\x08" "property")
QDEF(MP_QSTR_props, (const byte*)"\x8b\x05" "props")
QDEF(MP_QSTR_protocol, (const byte*)"\x33\x08" "protocol")
QDEF(MP_QSTR_ptr, (const byte*)"\x53\x03" "ptr")
QDEF(MP_QSTR_ptr_val, (const byte*)"\xf7\x07" "ptr_val")
QDEF(MP_QSTR_public_key, (const byte*)"\x0c\x0a" "public_key")
QDEF(MP_QSTR_pull, (const byte*)"\x80\x04" "pull")
QDEF(MP_QSTR_push_head, (const byte*)"\x6c\x09" "push_head")
QDEF(MP_QSTR_push_sorted, (const byte*)"\x1f\x0b" "push_sorted")
QDEF(MP_QSTR_py, (const byte*)"\x0c\x02" "py")
QDEF(MP_QSTR_py_mb, (const byte*)"\x7c\x05" "py_mb")
QDEF(MP_QSTR_qpyver, (const byte*)"\xbc\x06" "qpyver")
QDEF(MP_QSTR_r, (const byte*)"\xd7\x01" "r")
QDEF(MP_QSTR_r_mod, (const byte*)"\xee\x05" "r_mod")
QDEF(MP_QSTR_radians, (const byte*)"\x87\x07" "radians")
QDEF(MP_QSTR_radius, (const byte*)"\xfd\x06" "radius")
QDEF(MP_QSTR_rand, (const byte*)"\x9c\x04" "rand")
QDEF(MP_QSTR_randint, (const byte*)"\xaf\x07" "randint")
QDEF(MP_QSTR_random, (const byte*)"\xbe\x06" "random")
QDEF(MP_QSTR_randrange, (const byte*)"\xa3\x09" "randrange")
QDEF(MP_QSTR_raw_end, (const byte*)"\x31\x07" "raw_end")
QDEF(MP_QSTR_rb, (const byte*)"\xd5\x02" "rb")
QDEF(MP_QSTR_readOnce, (const byte*)"\xd0\x08" "readOnce")
QDEF(MP_QSTR_readPhonebook, (const byte*)"\xe2\x0d" "readPhonebook")
QDEF(MP_QSTR_read_cb, (const byte*)"\x69\x07" "read_cb")
QDEF(MP_QSTR_read_count, (const byte*)"\xcb\x0a" "read_count")
QDEF(MP_QSTR_read_level, (const byte*)"\xbe\x0a" "read_level")
QDEF(MP_QSTR_read_line, (const byte*)"\x86\x09" "read_line")
QDEF(MP_QSTR_read_line_cb, (const byte*)"\xf8\x0c" "read_line_cb")
QDEF(MP_QSTR_read_timer, (const byte*)"\xef\x0a" "read_timer")
QDEF(MP_QSTR_readblocks, (const byte*)"\x2d\x0a" "readblocks")
QDEF(MP_QSTR_readbuf, (const byte*)"\x86\x07" "readbuf")
QDEF(MP_QSTR_readjust_scroll, (const byte*)"\x1d\x0f" "readjust_scroll")
QDEF(MP_QSTR_readlines, (const byte*)"\x6a\x09" "readlines")
QDEF(MP_QSTR_readonly, (const byte*)"\x03\x08" "readonly")
QDEF(MP_QSTR_readsize, (const byte*)"\xd2\x08" "readsize")
QDEF(MP_QSTR_ready, (const byte*)"\xee\x05" "ready")
QDEF(MP_QSTR_ready_cb, (const byte*)"\xd0\x08" "ready_cb")
QDEF(MP_QSTR_real, (const byte*)"\xbf\x04" "real")
QDEF(MP_QSTR_recolor, (const byte*)"\x6f\x07" "recolor")
QDEF(MP_QSTR_recolor_opa, (const byte*)"\x6e\x0b" "recolor_opa")
QDEF(MP_QSTR_rect, (const byte*)"\xe5\x04" "rect")
QDEF(MP_QSTR_rect_dsc, (const byte*)"\x8e\x08" "rect_dsc")
QDEF(MP_QSTR_recv, (const byte*)"\xe7\x04" "recv")
QDEF(MP_QSTR_recvfrom, (const byte*)"\x91\x08" "recvfrom")
QDEF(MP_QSTR_red, (const byte*)"\x76\x03" "red")
QDEF(MP_QSTR_refocus_policy, (const byte*)"\xc1\x0e" "refocus_policy")
QDEF(MP_QSTR_refr_mode, (const byte*)"\x3a\x09" "refr_mode")
QDEF(MP_QSTR_refr_now, (const byte*)"\x2f\x08" "refr_now")
QDEF(MP_QSTR_refr_pos, (const byte*)"\xb5\x08" "refr_pos")
QDEF(MP_QSTR_refr_size, (const byte*)"\x3c\x09" "refr_size")
QDEF(MP_QSTR_refr_timer, (const byte*)"\x1e\x0a" "refr_timer")
QDEF(MP_QSTR_refresh, (const byte*)"\x98\x07" "refresh")
QDEF(MP_QSTR_refresh_ext_draw_size, (const byte*)"\xab\x15" "refresh_ext_draw_size")
QDEF(MP_QSTR_refresh_self_size, (const byte*)"\x21\x11" "refresh_self_size")
QDEF(MP_QSTR_refresh_style, (const byte*)"\xd0\x0d" "refresh_style")
QDEF(MP_QSTR_regaddr, (const byte*)"\x06\x07" "regaddr")
QDEF(MP_QSTR_regaddr_len, (const byte*)"\x7e\x0b" "regaddr_len")
QDEF(MP_QSTR_register, (const byte*)"\xac\x08" "register")
QDEF(MP_QSTR_register_callback, (const byte*)"\x3a\x11" "register_callback")
QDEF(MP_QSTR_reject, (const byte*)"\xea\x06" "reject")
QDEF(MP_QSTR_release, (const byte*)"\xec\x07" "release")
QDEF(MP_QSTR_remove_all_objs, (const byte*)"\xb6\x0f" "remove_all_objs")
QDEF(MP_QSTR_remove_event_cb, (const byte*)"\x8e\x0f" "remove_event_cb")
QDEF(MP_QSTR_remove_event_dsc, (const byte*)"\xdb\x10" "remove_event_dsc")
QDEF(MP_QSTR_remove_local_style_prop, (const byte*)"\x7b\x17" "remove_local_style_prop")
QDEF(MP_QSTR_remove_prop, (const byte*)"\x21\x0b" "remove_prop")
QDEF(MP_QSTR_remove_series, (const byte*)"\x07\x0d" "remove_series")
QDEF(MP_QSTR_remove_style, (const byte*)"\x6b\x0c" "remove_style")
QDEF(MP_QSTR_remove_style_all, (const byte*)"\xf5\x10" "remove_style_all")
QDEF(MP_QSTR_rename, (const byte*)"\x35\x06" "rename")
QDEF(MP_QSTR_repeat_cnt, (const byte*)"\x34\x0a" "repeat_cnt")
QDEF(MP_QSTR_repeat_count, (const byte*)"\x4e\x0c" "repeat_count")
QDEF(MP_QSTR_repeat_delay, (const byte*)"\xf8\x0c" "repeat_delay")
QDEF(MP_QSTR_replEnable, (const byte*)"\xef\x0a" "replEnable")
QDEF(MP_QSTR_replUpdatePassswd, (const byte*)"\xce\x11" "replUpdatePassswd")
QDEF(MP_QSTR_report_style_change, (const byte*)"\xda\x13" "report_style_change")
QDEF(MP_QSTR_res, (const byte*)"\x61\x03" "res")
QDEF(MP_QSTR_reserved, (const byte*)"\x01\x08" "reserved")
QDEF(MP_QSTR_reset, (const byte*)"\x10\x05" "reset")
QDEF(MP_QSTR_reset_disable, (const byte*)"\x1b\x0d" "reset_disable")
QDEF(MP_QSTR_reset_long_press, (const byte*)"\x7d\x10" "reset_long_press")
QDEF(MP_QSTR_reset_query, (const byte*)"\xe5\x0b" "reset_query")
QDEF(MP_QSTR_resume, (const byte*)"\x5c\x06" "resume")
QDEF(MP_QSTR_reversed, (const byte*)"\xa1\x08" "reversed")
QDEF(MP_QSTR_rmdir, (const byte*)"\x45\x05" "rmdir")
QDEF(MP_QSTR_roller, (const byte*)"\xaf\x06" "roller")
QDEF(MP_QSTR_roller_class, (const byte*)"\x5e\x0c" "roller_class")
QDEF(MP_QSTR_root_cert, (const byte*)"\xfc\x09" "root_cert")
QDEF(MP_QSTR_rotated, (const byte*)"\x38\x07" "rotated")
QDEF(MP_QSTR_rotation, (const byte*)"\xf1\x08" "rotation")
QDEF(MP_QSTR_round_end, (const byte*)"\x37\x09" "round_end")
QDEF(MP_QSTR_round_start, (const byte*)"\xb8\x0b" "round_start")
QDEF(MP_QSTR_rounded, (const byte*)"\x86\x07" "rounded")
QDEF(MP_QSTR_rounder_cb, (const byte*)"\x2e\x0a" "rounder_cb")
QDEF(MP_QSTR_run_round, (const byte*)"\x91\x09" "run_round")
QDEF(MP_QSTR_rx_auto_disable, (const byte*)"\x54\x0f" "rx_auto_disable")
QDEF(MP_QSTR_s, (const byte*)"\xd6\x01" "s")
QDEF(MP_QSTR_samplerate, (const byte*)"\xc1\x0a" "samplerate")
QDEF(MP_QSTR_scale, (const byte*)"\x7d\x05" "scale")
QDEF(MP_QSTR_scale_lines, (const byte*)"\x7f\x0b" "scale_lines")
QDEF(MP_QSTR_schedule, (const byte*)"\xe0\x08" "schedule")
QDEF(MP_QSTR_scr_act, (const byte*)"\xce\x07" "scr_act")
QDEF(MP_QSTR_scr_load, (const byte*)"\x3e\x08" "scr_load")
QDEF(MP_QSTR_scr_load_anim, (const byte*)"\xaa\x0d" "scr_load_anim")
QDEF(MP_QSTR_scr_to_load, (const byte*)"\xda\x0b" "scr_to_load")
QDEF(MP_QSTR_screen_cnt, (const byte*)"\x8f\x0a" "screen_cnt")
QDEF(MP_QSTR_screen_transp, (const byte*)"\x3c\x0d" "screen_transp")
QDEF(MP_QSTR_screens, (const byte*)"\x5a\x07" "screens")
QDEF(MP_QSTR_scroll_area, (const byte*)"\xe0\x0b" "scroll_area")
QDEF(MP_QSTR_scroll_by, (const byte*)"\xac\x09" "scroll_by")
QDEF(MP_QSTR_scroll_dir, (const byte*)"\x48\x0a" "scroll_dir")
QDEF(MP_QSTR_scroll_limit, (const byte*)"\x62\x0c" "scroll_limit")
QDEF(MP_QSTR_scroll_obj, (const byte*)"\xf0\x0a" "scroll_obj")
QDEF(MP_QSTR_scroll_sum, (const byte*)"\x9c\x0a" "scroll_sum")
QDEF(MP_QSTR_scroll_throw, (const byte*)"\x81\x0c" "scroll_throw")
QDEF(MP_QSTR_scroll_throw_vect, (const byte*)"\x9a\x11" "scroll_throw_vect")
QDEF(MP_QSTR_scroll_throw_vect_ori, (const byte*)"\x91\x15" "scroll_throw_vect_ori")
QDEF(MP_QSTR_scroll_to, (const byte*)"\xec\x09" "scroll_to")
QDEF(MP_QSTR_scroll_to_view, (const byte*)"\x5e\x0e" "scroll_to_view")
QDEF(MP_QSTR_scroll_to_view_recursive, (const byte*)"\xbb\x18" "scroll_to_view_recursive")
QDEF(MP_QSTR_scroll_to_x, (const byte*)"\xeb\x0b" "scroll_to_x")
QDEF(MP_QSTR_scroll_to_y, (const byte*)"\xea\x0b" "scroll_to_y")
QDEF(MP_QSTR_scrollbar_invalidate, (const byte*)"\xa7\x14" "scrollbar_invalidate")
QDEF(MP_QSTR_sdkver, (const byte*)"\xb8\x06" "sdkver")
QDEF(MP_QSTR_search, (const byte*)"\xab\x06" "search")
QDEF(MP_QSTR_security, (const byte*)"\x93\x08" "security")
QDEF(MP_QSTR_seed, (const byte*)"\x92\x04" "seed")
QDEF(MP_QSTR_seek, (const byte*)"\x9d\x04" "seek")
QDEF(MP_QSTR_seek_cb, (const byte*)"\x83\x07" "seek_cb")
QDEF(MP_QSTR_sel_bg_color, (const byte*)"\x47\x0c" "sel_bg_color")
QDEF(MP_QSTR_sel_color, (const byte*)"\x3d\x09" "sel_color")
QDEF(MP_QSTR_sel_end, (const byte*)"\x6f\x07" "sel_end")
QDEF(MP_QSTR_sel_start, (const byte*)"\xe0\x09" "sel_start")
QDEF(MP_QSTR_select, (const byte*)"\x8d\x06" "select")
QDEF(MP_QSTR_semphore, (const byte*)"\x5e\x08" "semphore")
QDEF(MP_QSTR_sendData, (const byte*)"\xc9\x08" "sendData")
QDEF(MP_QSTR_sendSync, (const byte*)"\x7e\x08" "sendSync")
QDEF(MP_QSTR_send_data, (const byte*)"\x96\x09" "send_data")
QDEF(MP_QSTR_send_dtmf, (const byte*)"\x7d\x09" "send_dtmf")
QDEF(MP_QSTR_sendall, (const byte*)"\x38\x07" "sendall")
QDEF(MP_QSTR_sendto, (const byte*)"\x22\x06" "sendto")
QDEF(MP_QSTR_ser, (const byte*)"\x21\x03" "ser")
QDEF(MP_QSTR_setApn, (const byte*)"\x58\x06" "setApn")
QDEF(MP_QSTR_setAsynMode, (const byte*)"\x81\x0b" "setAsynMode")
QDEF(MP_QSTR_setAutoConnect, (const byte*)"\x36\x0e" "setAutoConnect")
QDEF(MP_QSTR_setCallback, (const byte*)"\x0e\x0b" "setCallback")
QDEF(MP_QSTR_setConfig, (const byte*)"\x8d\x09" "setConfig")
QDEF(MP_QSTR_setDnsserver, (const byte*)"\x5b\x0c" "setDnsserver")
QDEF(MP_QSTR_setModemFun, (const byte*)"\x94\x0b" "setModemFun")
QDEF(MP_QSTR_setPDPContext, (const byte*)"\x5c\x0d" "setPDPContext")
QDEF(MP_QSTR_setRecvCallback, (const byte*)"\x8c\x0f" "setRecvCallback")
QDEF(MP_QSTR_setSpeakerpaCallback, (const byte*)"\x24\x14" "setSpeakerpaCallback")
QDEF(MP_QSTR_setSpeakerpaSwitch, (const byte*)"\x9f\x12" "setSpeakerpaSwitch")
QDEF(MP_QSTR_setTimeZone, (const byte*)"\x0c\x0b" "setTimeZone")
QDEF(MP_QSTR_setTimeZoneEx, (const byte*)"\x91\x0d" "setTimeZoneEx")
QDEF(MP_QSTR_setToneVolume, (const byte*)"\xdf\x0d" "setToneVolume")
QDEF(MP_QSTR_setVolume, (const byte*)"\x2f\x09" "setVolume")
QDEF(MP_QSTR_set_accepted_chars, (const byte*)"\xed\x12" "set_accepted_chars")
QDEF(MP_QSTR_set_act, (const byte*)"\x0e\x07" "set_act")
QDEF(MP_QSTR_set_addr, (const byte*)"\xab\x08" "set_addr")
QDEF(MP_QSTR_set_alarm, (const byte*)"\x6b\x09" "set_alarm")
QDEF(MP_QSTR_set_align, (const byte*)"\xd5\x09" "set_align")
QDEF(MP_QSTR_set_all_value, (const byte*)"\xad\x0d" "set_all_value")
QDEF(MP_QSTR_set_angle, (const byte*)"\xf9\x09" "set_angle")
QDEF(MP_QSTR_set_angles, (const byte*)"\x6a\x0a" "set_angles")
QDEF(MP_QSTR_set_anim_speed, (const byte*)"\x6b\x0e" "set_anim_speed")
QDEF(MP_QSTR_set_anim_time, (const byte*)"\x19\x0d" "set_anim_time")
QDEF(MP_QSTR_set_antialias, (const byte*)"\xfc\x0d" "set_antialias")
QDEF(MP_QSTR_set_apply_cb, (const byte*)"\xd2\x0c" "set_apply_cb")
QDEF(MP_QSTR_set_arc_color, (const byte*)"\xea\x0d" "set_arc_color")
QDEF(MP_QSTR_set_arc_opa, (const byte*)"\xe9\x0b" "set_arc_opa")
QDEF(MP_QSTR_set_arc_rounded, (const byte*)"\xb4\x0f" "set_arc_rounded")
QDEF(MP_QSTR_set_arc_width, (const byte*)"\x51\x0d" "set_arc_width")
QDEF(MP_QSTR_set_axis_tick, (const byte*)"\x51\x0d" "set_axis_tick")
QDEF(MP_QSTR_set_base_dir, (const byte*)"\xcd\x0c" "set_base_dir")
QDEF(MP_QSTR_set_bg_angles, (const byte*)"\x90\x0d" "set_bg_angles")
QDEF(MP_QSTR_set_bg_color, (const byte*)"\x5f\x0c" "set_bg_color")
QDEF(MP_QSTR_set_bg_end_angle, (const byte*)"\x93\x10" "set_bg_end_angle")
QDEF(MP_QSTR_set_bg_grad_color, (const byte*)"\x30\x11" "set_bg_grad_color")
QDEF(MP_QSTR_set_bg_grad_dir, (const byte*)"\x52\x0f" "set_bg_grad_dir")
QDEF(MP_QSTR_set_bg_grad_stop, (const byte*)"\x35\x10" "set_bg_grad_stop")
QDEF(MP_QSTR_set_bg_image, (const byte*)"\xc5\x0c" "set_bg_image")
QDEF(MP_QSTR_set_bg_img_opa, (const byte*)"\x60\x0e" "set_bg_img_opa")
QDEF(MP_QSTR_set_bg_img_recolor, (const byte*)"\xd4\x12" "set_bg_img_recolor")
QDEF(MP_QSTR_set_bg_img_recolor_opa, (const byte*)"\x15\x16" "set_bg_img_recolor_opa")
QDEF(MP_QSTR_set_bg_img_src, (const byte*)"\x3c\x0e" "set_bg_img_src")
QDEF(MP_QSTR_set_bg_img_tiled, (const byte*)"\xee\x10" "set_bg_img_tiled")
QDEF(MP_QSTR_set_bg_main_stop, (const byte*)"\x8e\x10" "set_bg_main_stop")
QDEF(MP_QSTR_set_bg_opa, (const byte*)"\x5c\x0a" "set_bg_opa")
QDEF(MP_QSTR_set_bg_start_angle, (const byte*)"\x1c\x12" "set_bg_start_angle")
QDEF(MP_QSTR_set_blend_mode, (const byte*)"\xa5\x0e" "set_blend_mode")
QDEF(MP_QSTR_set_border_color, (const byte*)"\x76\x10" "set_border_color")
QDEF(MP_QSTR_set_border_opa, (const byte*)"\x75\x0e" "set_border_opa")
QDEF(MP_QSTR_set_border_post, (const byte*)"\x53\x0f" "set_border_post")
QDEF(MP_QSTR_set_border_side, (const byte*)"\x90\x0f" "set_border_side")
QDEF(MP_QSTR_set_border_width, (const byte*)"\xcd\x10" "set_border_width")
QDEF(MP_QSTR_set_brightness, (const byte*)"\x91\x0e" "set_brightness")
QDEF(MP_QSTR_set_btn_ctrl, (const byte*)"\x16\x0c" "set_btn_ctrl")
QDEF(MP_QSTR_set_btn_ctrl_all, (const byte*)"\x48\x10" "set_btn_ctrl_all")
QDEF(MP_QSTR_set_btn_width, (const byte*)"\x19\x0d" "set_btn_width")
QDEF(MP_QSTR_set_buffer, (const byte*)"\x78\x0a" "set_buffer")
QDEF(MP_QSTR_set_button_points, (const byte*)"\xce\x11" "set_button_points")
QDEF(MP_QSTR_set_callback, (const byte*)"\x91\x0c" "set_callback")
QDEF(MP_QSTR_set_cb, (const byte*)"\xf9\x06" "set_cb")
QDEF(MP_QSTR_set_cell_value, (const byte*)"\x6a\x0e" "set_cell_value")
QDEF(MP_QSTR_set_change_rate, (const byte*)"\xe3\x0f" "set_change_rate")
QDEF(MP_QSTR_set_channel, (const byte*)"\x5b\x0b" "set_channel")
QDEF(MP_QSTR_set_clip_corner, (const byte*)"\xd6\x0f" "set_clip_corner")
QDEF(MP_QSTR_set_close_cb, (const byte*)"\x30\x0c" "set_close_cb")
QDEF(MP_QSTR_set_close_pa_delay, (const byte*)"\x0a\x12" "set_close_pa_delay")
QDEF(MP_QSTR_set_col_cnt, (const byte*)"\x9e\x0b" "set_col_cnt")
QDEF(MP_QSTR_set_col_width, (const byte*)"\xa1\x0d" "set_col_width")
QDEF(MP_QSTR_set_color, (const byte*)"\x25\x09" "set_color")
QDEF(MP_QSTR_set_color_filter_dsc, (const byte*)"\xd1\x14" "set_color_filter_dsc")
QDEF(MP_QSTR_set_color_filter_opa, (const byte*)"\xfb\x14" "set_color_filter_opa")
QDEF(MP_QSTR_set_content_height, (const byte*)"\x51\x12" "set_content_height")
QDEF(MP_QSTR_set_content_width, (const byte*)"\x28\x11" "set_content_width")
QDEF(MP_QSTR_set_cover_res, (const byte*)"\x4e\x0d" "set_cover_res")
QDEF(MP_QSTR_set_ctrl_map, (const byte*)"\xd2\x0c" "set_ctrl_map")
QDEF(MP_QSTR_set_cursor, (const byte*)"\x12\x0a" "set_cursor")
QDEF(MP_QSTR_set_cursor_click_pos, (const byte*)"\xd0\x14" "set_cursor_click_pos")
QDEF(MP_QSTR_set_cursor_point, (const byte*)"\xe1\x10" "set_cursor_point")
QDEF(MP_QSTR_set_cursor_pos, (const byte*)"\x21\x0e" "set_cursor_pos")
QDEF(MP_QSTR_set_custom_exec_cb, (const byte*)"\xd1\x12" "set_custom_exec_cb")
QDEF(MP_QSTR_set_day_names, (const byte*)"\x0f\x0d" "set_day_names")
QDEF(MP_QSTR_set_default, (const byte*)"\x33\x0b" "set_default")
QDEF(MP_QSTR_set_default_NIC, (const byte*)"\x28\x0f" "set_default_NIC")
QDEF(MP_QSTR_set_default_network_card, (const byte*)"\xd9\x18" "set_default_network_card")
QDEF(MP_QSTR_set_delay, (const byte*)"\xad\x09" "set_delay")
QDEF(MP_QSTR_set_digit_format, (const byte*)"\x73\x10" "set_digit_format")
QDEF(MP_QSTR_set_dir, (const byte*)"\x07\x07" "set_dir")
QDEF(MP_QSTR_set_div_line_count, (const byte*)"\x4e\x12" "set_div_line_count")
QDEF(MP_QSTR_set_dns, (const byte*)"\x61\x07" "set_dns")
QDEF(MP_QSTR_set_down, (const byte*)"\xaa\x08" "set_down")
QDEF(MP_QSTR_set_duration, (const byte*)"\xe6\x0c" "set_duration")
QDEF(MP_QSTR_set_early_apply, (const byte*)"\x10\x0f" "set_early_apply")
QDEF(MP_QSTR_set_editing, (const byte*)"\x64\x0b" "set_editing")
QDEF(MP_QSTR_set_end_angle, (const byte*)"\x29\x0d" "set_end_angle")
QDEF(MP_QSTR_set_exec_cb, (const byte*)"\x9d\x0b" "set_exec_cb")
QDEF(MP_QSTR_set_ext_click_area, (const byte*)"\x08\x12" "set_ext_click_area")
QDEF(MP_QSTR_set_ext_draw_size, (const byte*)"\x94\x11" "set_ext_draw_size")
QDEF(MP_QSTR_set_ext_x_array, (const byte*)"\xd0\x0f" "set_ext_x_array")
QDEF(MP_QSTR_set_ext_y_array, (const byte*)"\x91\x0f" "set_ext_y_array")
QDEF(MP_QSTR_set_flex_align, (const byte*)"\x9d\x0e" "set_flex_align")
QDEF(MP_QSTR_set_flex_cross_place, (const byte*)"\x2a\x14" "set_flex_cross_place")
QDEF(MP_QSTR_set_flex_flow, (const byte*)"\x62\x0d" "set_flex_flow")
QDEF(MP_QSTR_set_flex_grow, (const byte*)"\x1d\x0d" "set_flex_grow")
QDEF(MP_QSTR_set_flex_main_place, (const byte*)"\xff\x13" "set_flex_main_place")
QDEF(MP_QSTR_set_flex_track_place, (const byte*)"\x3b\x14" "set_flex_track_place")
QDEF(MP_QSTR_set_focus_cb, (const byte*)"\x4a\x0c" "set_focus_cb")
QDEF(MP_QSTR_set_get_value_cb, (const byte*)"\xe4\x10" "set_get_value_cb")
QDEF(MP_QSTR_set_grid_align, (const byte*)"\x32\x0e" "set_grid_align")
QDEF(MP_QSTR_set_grid_cell, (const byte*)"\xb9\x0d" "set_grid_cell")
QDEF(MP_QSTR_set_grid_cell_column_pos, (const byte*)"\xe3\x18" "set_grid_cell_column_pos")
QDEF(MP_QSTR_set_grid_cell_column_span, (const byte*)"\xc3\x19" "set_grid_cell_column_span")
QDEF(MP_QSTR_set_grid_cell_row_pos, (const byte*)"\x7f\x15" "set_grid_cell_row_pos")
QDEF(MP_QSTR_set_grid_cell_row_span, (const byte*)"\xdf\x16" "set_grid_cell_row_span")
QDEF(MP_QSTR_set_grid_cell_x_align, (const byte*)"\x8c\x15" "set_grid_cell_x_align")
QDEF(MP_QSTR_set_grid_cell_y_align, (const byte*)"\xcd\x15" "set_grid_cell_y_align")
QDEF(MP_QSTR_set_grid_column_align, (const byte*)"\x7b\x15" "set_grid_column_align")
QDEF(MP_QSTR_set_grid_column_dsc_array, (const byte*)"\x84\x19" "set_grid_column_dsc_array")
QDEF(MP_QSTR_set_grid_dsc_array, (const byte*)"\x0d\x12" "set_grid_dsc_array")
QDEF(MP_QSTR_set_grid_row_align, (const byte*)"\xc7\x12" "set_grid_row_align")
QDEF(MP_QSTR_set_grid_row_dsc_array, (const byte*)"\x38\x16" "set_grid_row_dsc_array")
QDEF(MP_QSTR_set_group, (const byte*)"\xc7\x09" "set_group")
QDEF(MP_QSTR_set_height, (const byte*)"\x67\x0a" "set_height")
QDEF(MP_QSTR_set_highlighted_dates, (const byte*)"\x11\x15" "set_highlighted_dates")
QDEF(MP_QSTR_set_hsv, (const byte*)"\xd5\x07" "set_hsv")
QDEF(MP_QSTR_set_img_opa, (const byte*)"\x9a\x0b" "set_img_opa")
QDEF(MP_QSTR_set_img_recolor, (const byte*)"\xee\x0f" "set_img_recolor")
QDEF(MP_QSTR_set_img_recolor_opa, (const byte*)"\x6f\x13" "set_img_recolor_opa")
QDEF(MP_QSTR_set_indent, (const byte*)"\x64\x0a" "set_indent")
QDEF(MP_QSTR_set_indicator_end_value, (const byte*)"\x9d\x17" "set_indicator_end_value")
QDEF(MP_QSTR_set_indicator_start_value, (const byte*)"\x92\x19" "set_indicator_start_value")
QDEF(MP_QSTR_set_indicator_value, (const byte*)"\x8d\x13" "set_indicator_value")
QDEF(MP_QSTR_set_info_cb, (const byte*)"\xc8\x0b" "set_info_cb")
QDEF(MP_QSTR_set_insert_replace, (const byte*)"\x1c\x12" "set_insert_replace")
QDEF(MP_QSTR_set_layout, (const byte*)"\x82\x0a" "set_layout")
QDEF(MP_QSTR_set_left_value, (const byte*)"\x57\x0e" "set_left_value")
QDEF(MP_QSTR_set_line_color, (const byte*)"\xd4\x0e" "set_line_color")
QDEF(MP_QSTR_set_line_dash_gap, (const byte*)"\xde\x11" "set_line_dash_gap")
QDEF(MP_QSTR_set_line_dash_width, (const byte*)"\x4e\x13" "set_line_dash_width")
QDEF(MP_QSTR_set_line_opa, (const byte*)"\xd7\x0c" "set_line_opa")
QDEF(MP_QSTR_set_line_rounded, (const byte*)"\x8a\x10" "set_line_rounded")
QDEF(MP_QSTR_set_line_width, (const byte*)"\x2f\x0e" "set_line_width")
QDEF(MP_QSTR_set_local_ip, (const byte*)"\x73\x0c" "set_local_ip")
QDEF(MP_QSTR_set_local_style_prop, (const byte*)"\x3f\x14" "set_local_style_prop")
QDEF(MP_QSTR_set_long_mode, (const byte*)"\xae\x0d" "set_long_mode")
QDEF(MP_QSTR_set_map, (const byte*)"\x44\x07" "set_map")
QDEF(MP_QSTR_set_max_height, (const byte*)"\x6c\x0e" "set_max_height")
QDEF(MP_QSTR_set_max_length, (const byte*)"\x4f\x0e" "set_max_length")
QDEF(MP_QSTR_set_max_width, (const byte*)"\xf5\x0d" "set_max_width")
QDEF(MP_QSTR_set_min_height, (const byte*)"\xb2\x0e" "set_min_height")
QDEF(MP_QSTR_set_min_width, (const byte*)"\x6b\x0d" "set_min_width")
QDEF(MP_QSTR_set_mode, (const byte*)"\x3b\x08" "set_mode")
QDEF(MP_QSTR_set_mode_fixed, (const byte*)"\xb2\x0e" "set_mode_fixed")
QDEF(MP_QSTR_set_next_value, (const byte*)"\xcb\x0e" "set_next_value")
QDEF(MP_QSTR_set_next_value2, (const byte*)"\x19\x0f" "set_next_value2")
QDEF(MP_QSTR_set_offset_x, (const byte*)"\x52\x0c" "set_offset_x")
QDEF(MP_QSTR_set_offset_y, (const byte*)"\x53\x0c" "set_offset_y")
QDEF(MP_QSTR_set_one_checked, (const byte*)"\x64\x0f" "set_one_checked")
QDEF(MP_QSTR_set_one_line, (const byte*)"\x6d\x0c" "set_one_line")
QDEF(MP_QSTR_set_opa, (const byte*)"\x26\x07" "set_opa")
QDEF(MP_QSTR_set_open_cb, (const byte*)"\x32\x0b" "set_open_cb")
QDEF(MP_QSTR_set_open_pa_delay, (const byte*)"\x08\x11" "set_open_pa_delay")
QDEF(MP_QSTR_set_options, (const byte*)"\x88\x0b" "set_options")
QDEF(MP_QSTR_set_options_static, (const byte*)"\x2f\x12" "set_options_static")
QDEF(MP_QSTR_set_outline_color, (const byte*)"\x5a\x11" "set_outline_color")
QDEF(MP_QSTR_set_outline_opa, (const byte*)"\x59\x0f" "set_outline_opa")
QDEF(MP_QSTR_set_outline_pad, (const byte*)"\x72\x0f" "set_outline_pad")
QDEF(MP_QSTR_set_outline_width, (const byte*)"\xa1\x11" "set_outline_width")
QDEF(MP_QSTR_set_overflow, (const byte*)"\xe4\x0c" "set_overflow")
QDEF(MP_QSTR_set_pa, (const byte*)"\x49\x06" "set_pa")
QDEF(MP_QSTR_set_pad_all, (const byte*)"\xd3\x0b" "set_pad_all")
QDEF(MP_QSTR_set_pad_bottom, (const byte*)"\x9d\x0e" "set_pad_bottom")
QDEF(MP_QSTR_set_pad_column, (const byte*)"\x24\x0e" "set_pad_column")
QDEF(MP_QSTR_set_pad_gap, (const byte*)"\x64\x0b" "set_pad_gap")
QDEF(MP_QSTR_set_pad_hor, (const byte*)"\xe7\x0b" "set_pad_hor")
QDEF(MP_QSTR_set_pad_left, (const byte*)"\x29\x0c" "set_pad_left")
QDEF(MP_QSTR_set_pad_right, (const byte*)"\x32\x0d" "set_pad_right")
QDEF(MP_QSTR_set_pad_row, (const byte*)"\x78\x0b" "set_pad_row")
QDEF(MP_QSTR_set_pad_top, (const byte*)"\xf9\x0b" "set_pad_top")
QDEF(MP_QSTR_set_pad_ver, (const byte*)"\xb3\x0b" "set_pad_ver")
QDEF(MP_QSTR_set_palette, (const byte*)"\x25\x0b" "set_palette")
QDEF(MP_QSTR_set_parent, (const byte*)"\xc4\x0a" "set_parent")
QDEF(MP_QSTR_set_password_mode, (const byte*)"\x5b\x11" "set_password_mode")
QDEF(MP_QSTR_set_password_show_time, (const byte*)"\x31\x16" "set_password_show_time")
QDEF(MP_QSTR_set_path_cb, (const byte*)"\x4b\x0b" "set_path_cb")
QDEF(MP_QSTR_set_period, (const byte*)"\xbd\x0a" "set_period")
QDEF(MP_QSTR_set_pivot, (const byte*)"\xec\x09" "set_pivot")
QDEF(MP_QSTR_set_placeholder_text, (const byte*)"\x19\x14" "set_placeholder_text")
QDEF(MP_QSTR_set_playback_delay, (const byte*)"\x9d\x12" "set_playback_delay")
QDEF(MP_QSTR_set_playback_time, (const byte*)"\x9d\x11" "set_playback_time")
QDEF(MP_QSTR_set_point_count, (const byte*)"\x28\x0f" "set_point_count")
QDEF(MP_QSTR_set_points, (const byte*)"\xe7\x0a" "set_points")
QDEF(MP_QSTR_set_pos, (const byte*)"\x54\x07" "set_pos")
QDEF(MP_QSTR_set_prop, (const byte*)"\xe5\x08" "set_prop")
QDEF(MP_QSTR_set_px, (const byte*)"\x50\x06" "set_px")
QDEF(MP_QSTR_set_px_cb, (const byte*)"\x6e\x09" "set_px_cb")
QDEF(MP_QSTR_set_radius, (const byte*)"\xe0\x0a" "set_radius")
QDEF(MP_QSTR_set_range, (const byte*)"\x67\x09" "set_range")
QDEF(MP_QSTR_set_read_line_cb, (const byte*)"\xa5\x10" "set_read_line_cb")
QDEF(MP_QSTR_set_ready_cb, (const byte*)"\xcd\x0c" "set_ready_cb")
QDEF(MP_QSTR_set_recolor, (const byte*)"\x92\x0b" "set_recolor")
QDEF(MP_QSTR_set_refocus_policy, (const byte*)"\x5c\x12" "set_refocus_policy")
QDEF(MP_QSTR_set_repeat_count, (const byte*)"\x13\x10" "set_repeat_count")
QDEF(MP_QSTR_set_repeat_delay, (const byte*)"\x65\x10" "set_repeat_delay")
QDEF(MP_QSTR_set_rgb, (const byte*)"\xcf\x07" "set_rgb")
QDEF(MP_QSTR_set_ring_back_tone_cb, (const byte*)"\x6f\x15" "set_ring_back_tone_cb")
QDEF(MP_QSTR_set_rollover, (const byte*)"\x4b\x0c" "set_rollover")
QDEF(MP_QSTR_set_rotation, (const byte*)"\x6c\x0c" "set_rotation")
QDEF(MP_QSTR_set_row_cnt, (const byte*)"\x54\x0b" "set_row_cnt")
QDEF(MP_QSTR_set_scale_major_ticks, (const byte*)"\xdd\x15" "set_scale_major_ticks")
QDEF(MP_QSTR_set_scale_range, (const byte*)"\x01\x0f" "set_scale_range")
QDEF(MP_QSTR_set_scale_ticks, (const byte*)"\x59\x0f" "set_scale_ticks")
QDEF(MP_QSTR_set_scroll_dir, (const byte*)"\x95\x0e" "set_scroll_dir")
QDEF(MP_QSTR_set_scroll_snap_x, (const byte*)"\x81\x11" "set_scroll_snap_x")
QDEF(MP_QSTR_set_scroll_snap_y, (const byte*)"\x80\x11" "set_scroll_snap_y")
QDEF(MP_QSTR_set_scrollbar_mode, (const byte*)"\x38\x12" "set_scrollbar_mode")
QDEF(MP_QSTR_set_selected, (const byte*)"\x31\x0c" "set_selected")
QDEF(MP_QSTR_set_selected_btn, (const byte*)"\x36\x10" "set_selected_btn")
QDEF(MP_QSTR_set_selected_highlight, (const byte*)"\x7e\x16" "set_selected_highlight")
QDEF(MP_QSTR_set_series_color, (const byte*)"\x81\x10" "set_series_color")
QDEF(MP_QSTR_set_shadow_color, (const byte*)"\x3c\x10" "set_shadow_color")
QDEF(MP_QSTR_set_shadow_ofs_x, (const byte*)"\xfc\x10" "set_shadow_ofs_x")
QDEF(MP_QSTR_set_shadow_ofs_y, (const byte*)"\xfd\x10" "set_shadow_ofs_y")
QDEF(MP_QSTR_set_shadow_opa, (const byte*)"\x3f\x0e" "set_shadow_opa")
QDEF(MP_QSTR_set_shadow_spread, (const byte*)"\x70\x11" "set_shadow_spread")
QDEF(MP_QSTR_set_shadow_width, (const byte*)"\x87\x10" "set_shadow_width")
QDEF(MP_QSTR_set_showed_date, (const byte*)"\xf1\x0f" "set_showed_date")
QDEF(MP_QSTR_set_size, (const byte*)"\x3d\x08" "set_size")
QDEF(MP_QSTR_set_size_mode, (const byte*)"\x61\x0d" "set_size_mode")
QDEF(MP_QSTR_set_src, (const byte*)"\x7a\x07" "set_src")
QDEF(MP_QSTR_set_src_change, (const byte*)"\x03\x0e" "set_src_change")
QDEF(MP_QSTR_set_start_angle, (const byte*)"\x26\x0f" "set_start_angle")
QDEF(MP_QSTR_set_start_cb, (const byte*)"\x06\x0c" "set_start_cb")
QDEF(MP_QSTR_set_start_value, (const byte*)"\x6c\x0f" "set_start_value")
QDEF(MP_QSTR_set_step, (const byte*)"\x8a\x08" "set_step")
QDEF(MP_QSTR_set_style_align, (const byte*)"\xdd\x0f" "set_style_align")
QDEF(MP_QSTR_set_style_anim_speed, (const byte*)"\x63\x14" "set_style_anim_speed")
QDEF(MP_QSTR_set_style_anim_time, (const byte*)"\x11\x13" "set_style_anim_time")
QDEF(MP_QSTR_set_style_arc_color, (const byte*)"\xe2\x13" "set_style_arc_color")
QDEF(MP_QSTR_set_style_arc_img_src, (const byte*)"\x41\x15" "set_style_arc_img_src")
QDEF(MP_QSTR_set_style_arc_opa, (const byte*)"\xe1\x11" "set_style_arc_opa")
QDEF(MP_QSTR_set_style_arc_rounded, (const byte*)"\xbc\x15" "set_style_arc_rounded")
QDEF(MP_QSTR_set_style_arc_width, (const byte*)"\x59\x13" "set_style_arc_width")
QDEF(MP_QSTR_set_style_base_dir, (const byte*)"\xc5\x12" "set_style_base_dir")
QDEF(MP_QSTR_set_style_bg_color, (const byte*)"\x57\x12" "set_style_bg_color")
QDEF(MP_QSTR_set_style_bg_grad_color, (const byte*)"\x38\x17" "set_style_bg_grad_color")
QDEF(MP_QSTR_set_style_bg_grad_dir, (const byte*)"\x5a\x15" "set_style_bg_grad_dir")
QDEF(MP_QSTR_set_style_bg_grad_stop, (const byte*)"\x3d\x16" "set_style_bg_grad_stop")
QDEF(MP_QSTR_set_style_bg_img_opa, (const byte*)"\x68\x14" "set_style_bg_img_opa")
QDEF(MP_QSTR_set_style_bg_img_recolor, (const byte*)"\xdc\x18" "set_style_bg_img_recolor")
QDEF(MP_QSTR_set_style_bg_img_recolor_opa, (const byte*)"\x1d\x1c" "set_style_bg_img_recolor_opa")
QDEF(MP_QSTR_set_style_bg_img_src, (const byte*)"\x34\x14" "set_style_bg_img_src")
QDEF(MP_QSTR_set_style_bg_img_tiled, (const byte*)"\xe6\x16" "set_style_bg_img_tiled")
QDEF(MP_QSTR_set_style_bg_main_stop, (const byte*)"\x86\x16" "set_style_bg_main_stop")
QDEF(MP_QSTR_set_style_bg_opa, (const byte*)"\x54\x10" "set_style_bg_opa")
QDEF(MP_QSTR_set_style_blend_mode, (const byte*)"\xad\x14" "set_style_blend_mode")
QDEF(MP_QSTR_set_style_border_color, (const byte*)"\x7e\x16" "set_style_border_color")
QDEF(MP_QSTR_set_style_border_opa, (const byte*)"\x7d\x14" "set_style_border_opa")
QDEF(MP_QSTR_set_style_border_post, (const byte*)"\x5b\x15" "set_style_border_post")
QDEF(MP_QSTR_set_style_border_side, (const byte*)"\x98\x15" "set_style_border_side")
QDEF(MP_QSTR_set_style_border_width, (const byte*)"\xc5\x16" "set_style_border_width")
QDEF(MP_QSTR_set_style_clip_corner, (const byte*)"\xde\x15" "set_style_clip_corner")
QDEF(MP_QSTR_set_style_color_filter_dsc, (const byte*)"\xd9\x1a" "set_style_color_filter_dsc")
QDEF(MP_QSTR_set_style_color_filter_opa, (const byte*)"\xf3\x1a" "set_style_color_filter_opa")
QDEF(MP_QSTR_set_style_flex_cross_place, (const byte*)"\x22\x1a" "set_style_flex_cross_place")
QDEF(MP_QSTR_set_style_flex_flow, (const byte*)"\x6a\x13" "set_style_flex_flow")
QDEF(MP_QSTR_set_style_flex_grow, (const byte*)"\x15\x13" "set_style_flex_grow")
QDEF(MP_QSTR_set_style_flex_main_place, (const byte*)"\xf7\x19" "set_style_flex_main_place")
QDEF(MP_QSTR_set_style_flex_track_place, (const byte*)"\x33\x1a" "set_style_flex_track_place")
QDEF(MP_QSTR_set_style_grid_cell_column_pos, (const byte*)"\xeb\x1e" "set_style_grid_cell_column_pos")
QDEF(MP_QSTR_set_style_grid_cell_column_span, (const byte*)"\xcb\x1f" "set_style_grid_cell_column_span")
QDEF(MP_QSTR_set_style_grid_cell_row_pos, (const byte*)"\x77\x1b" "set_style_grid_cell_row_pos")
QDEF(MP_QSTR_set_style_grid_cell_row_span, (const byte*)"\xd7\x1c" "set_style_grid_cell_row_span")
QDEF(MP_QSTR_set_style_grid_cell_x_align, (const byte*)"\x84\x1b" "set_style_grid_cell_x_align")
QDEF(MP_QSTR_set_style_grid_cell_y_align, (const byte*)"\xc5\x1b" "set_style_grid_cell_y_align")
QDEF(MP_QSTR_set_style_grid_column_align, (const byte*)"\x73\x1b" "set_style_grid_column_align")
QDEF(MP_QSTR_set_style_grid_column_dsc_array, (const byte*)"\x8c\x1f" "set_style_grid_column_dsc_array")
QDEF(MP_QSTR_set_style_grid_row_align, (const byte*)"\xcf\x18" "set_style_grid_row_align")
QDEF(MP_QSTR_set_style_grid_row_dsc_array, (const byte*)"\x30\x1c" "set_style_grid_row_dsc_array")
QDEF(MP_QSTR_set_style_height, (const byte*)"\x6f\x10" "set_style_height")
QDEF(MP_QSTR_set_style_img_opa, (const byte*)"\x92\x11" "set_style_img_opa")
QDEF(MP_QSTR_set_style_img_recolor, (const byte*)"\xe6\x15" "set_style_img_recolor")
QDEF(MP_QSTR_set_style_img_recolor_opa, (const byte*)"\x67\x19" "set_style_img_recolor_opa")
QDEF(MP_QSTR_set_style_layout, (const byte*)"\x8a\x10" "set_style_layout")
QDEF(MP_QSTR_set_style_line_color, (const byte*)"\xdc\x14" "set_style_line_color")
QDEF(MP_QSTR_set_style_line_dash_gap, (const byte*)"\xd6\x17" "set_style_line_dash_gap")
QDEF(MP_QSTR_set_style_line_dash_width, (const byte*)"\x46\x19" "set_style_line_dash_width")
QDEF(MP_QSTR_set_style_line_opa, (const byte*)"\xdf\x12" "set_style_line_opa")
QDEF(MP_QSTR_set_style_line_rounded, (const byte*)"\x82\x16" "set_style_line_rounded")
QDEF(MP_QSTR_set_style_line_width, (const byte*)"\x27\x14" "set_style_line_width")
QDEF(MP_QSTR_set_style_max_height, (const byte*)"\x64\x14" "set_style_max_height")
QDEF(MP_QSTR_set_style_max_width, (const byte*)"\xfd\x13" "set_style_max_width")
QDEF(MP_QSTR_set_style_min_height, (const byte*)"\xba\x14" "set_style_min_height")
QDEF(MP_QSTR_set_style_min_width, (const byte*)"\x63\x13" "set_style_min_width")
QDEF(MP_QSTR_set_style_opa, (const byte*)"\x2e\x0d" "set_style_opa")
QDEF(MP_QSTR_set_style_outline_color, (const byte*)"\x52\x17" "set_style_outline_color")
QDEF(MP_QSTR_set_style_outline_opa, (const byte*)"\x51\x15" "set_style_outline_opa")
QDEF(MP_QSTR_set_style_outline_pad, (const byte*)"\x7a\x15" "set_style_outline_pad")
QDEF(MP_QSTR_set_style_outline_width, (const byte*)"\xa9\x17" "set_style_outline_width")
QDEF(MP_QSTR_set_style_pad_all, (const byte*)"\xdb\x11" "set_style_pad_all")
QDEF(MP_QSTR_set_style_pad_bottom, (const byte*)"\x95\x14" "set_style_pad_bottom")
QDEF(MP_QSTR_set_style_pad_column, (const byte*)"\x2c\x14" "set_style_pad_column")
QDEF(MP_QSTR_set_style_pad_gap, (const byte*)"\x6c\x11" "set_style_pad_gap")
QDEF(MP_QSTR_set_style_pad_hor, (const byte*)"\xef\x11" "set_style_pad_hor")
QDEF(MP_QSTR_set_style_pad_left, (const byte*)"\x21\x12" "set_style_pad_left")
QDEF(MP_QSTR_set_style_pad_right, (const byte*)"\x3a\x13" "set_style_pad_right")
QDEF(MP_QSTR_set_style_pad_row, (const byte*)"\x70\x11" "set_style_pad_row")
QDEF(MP_QSTR_set_style_pad_top, (const byte*)"\xf1\x11" "set_style_pad_top")
QDEF(MP_QSTR_set_style_pad_ver, (const byte*)"\xbb\x11" "set_style_pad_ver")
QDEF(MP_QSTR_set_style_radius, (const byte*)"\xe8\x10" "set_style_radius")
QDEF(MP_QSTR_set_style_shadow_color, (const byte*)"\x34\x16" "set_style_shadow_color")
QDEF(MP_QSTR_set_style_shadow_ofs_x, (const byte*)"\xf4\x16" "set_style_shadow_ofs_x")
QDEF(MP_QSTR_set_style_shadow_ofs_y, (const byte*)"\xf5\x16" "set_style_shadow_ofs_y")
QDEF(MP_QSTR_set_style_shadow_opa, (const byte*)"\x37\x14" "set_style_shadow_opa")
QDEF(MP_QSTR_set_style_shadow_spread, (const byte*)"\x78\x17" "set_style_shadow_spread")
QDEF(MP_QSTR_set_style_shadow_width, (const byte*)"\x8f\x16" "set_style_shadow_width")
QDEF(MP_QSTR_set_style_size, (const byte*)"\x35\x0e" "set_style_size")
QDEF(MP_QSTR_set_style_text_align, (const byte*)"\x3f\x14" "set_style_text_align")
QDEF(MP_QSTR_set_style_text_color, (const byte*)"\x0f\x14" "set_style_text_color")
QDEF(MP_QSTR_set_style_text_decor, (const byte*)"\x8d\x14" "set_style_text_decor")
QDEF(MP_QSTR_set_style_text_font, (const byte*)"\xa1\x13" "set_style_text_font")
QDEF(MP_QSTR_set_style_text_letter_space, (const byte*)"\xf7\x1b" "set_style_text_letter_space")
QDEF(MP_QSTR_set_style_text_line_space, (const byte*)"\x47\x19" "set_style_text_line_space")
QDEF(MP_QSTR_set_style_text_opa, (const byte*)"\x0c\x12" "set_style_text_opa")
QDEF(MP_QSTR_set_style_transform_angle, (const byte*)"\xe2\x19" "set_style_transform_angle")
QDEF(MP_QSTR_set_style_transform_height, (const byte*)"\x5c\x1a" "set_style_transform_height")
QDEF(MP_QSTR_set_style_transform_width, (const byte*)"\x05\x19" "set_style_transform_width")
QDEF(MP_QSTR_set_style_transform_zoom, (const byte*)"\x54\x18" "set_style_transform_zoom")
QDEF(MP_QSTR_set_style_transition, (const byte*)"\xbf\x14" "set_style_transition")
QDEF(MP_QSTR_set_style_translate_x, (const byte*)"\x31\x15" "set_style_translate_x")
QDEF(MP_QSTR_set_style_translate_y, (const byte*)"\x30\x15" "set_style_translate_y")
QDEF(MP_QSTR_set_style_width, (const byte*)"\xd6\x0f" "set_style_width")
QDEF(MP_QSTR_set_style_x, (const byte*)"\x28\x0b" "set_style_x")
QDEF(MP_QSTR_set_style_y, (const byte*)"\x29\x0b" "set_style_y")
QDEF(MP_QSTR_set_symbol, (const byte*)"\xbe\x0a" "set_symbol")
QDEF(MP_QSTR_set_text, (const byte*)"\xc5\x08" "set_text")
QDEF(MP_QSTR_set_text_align, (const byte*)"\x37\x0e" "set_text_align")
QDEF(MP_QSTR_set_text_color, (const byte*)"\x07\x0e" "set_text_color")
QDEF(MP_QSTR_set_text_decor, (const byte*)"\x85\x0e" "set_text_decor")
QDEF(MP_QSTR_set_text_font, (const byte*)"\xa9\x0d" "set_text_font")
QDEF(MP_QSTR_set_text_font_v2, (const byte*)"\x12\x10" "set_text_font_v2")
QDEF(MP_QSTR_set_text_letter_space, (const byte*)"\xff\x15" "set_text_letter_space")
QDEF(MP_QSTR_set_text_line_space, (const byte*)"\x4f\x13" "set_text_line_space")
QDEF(MP_QSTR_set_text_opa, (const byte*)"\x04\x0c" "set_text_opa")
QDEF(MP_QSTR_set_text_sel_end, (const byte*)"\x70\x10" "set_text_sel_end")
QDEF(MP_QSTR_set_text_sel_start, (const byte*)"\xff\x12" "set_text_sel_start")
QDEF(MP_QSTR_set_text_selection, (const byte*)"\x7a\x12" "set_text_selection")
QDEF(MP_QSTR_set_text_static, (const byte*)"\x42\x0f" "set_text_static")
QDEF(MP_QSTR_set_textarea, (const byte*)"\x52\x0c" "set_textarea")
QDEF(MP_QSTR_set_theme, (const byte*)"\xc9\x09" "set_theme")
QDEF(MP_QSTR_set_tile, (const byte*)"\x4c\x08" "set_tile")
QDEF(MP_QSTR_set_tile_id, (const byte*)"\xbe\x0b" "set_tile_id")
QDEF(MP_QSTR_set_time, (const byte*)"\x6d\x08" "set_time")
QDEF(MP_QSTR_set_today_date, (const byte*)"\x14\x0e" "set_today_date")
QDEF(MP_QSTR_set_transform_angle, (const byte*)"\xea\x13" "set_transform_angle")
QDEF(MP_QSTR_set_transform_height, (const byte*)"\x54\x14" "set_transform_height")
QDEF(MP_QSTR_set_transform_width, (const byte*)"\x0d\x13" "set_transform_width")
QDEF(MP_QSTR_set_transform_zoom, (const byte*)"\x5c\x12" "set_transform_zoom")
QDEF(MP_QSTR_set_transition, (const byte*)"\xb7\x0e" "set_transition")
QDEF(MP_QSTR_set_translate_x, (const byte*)"\x39\x0f" "set_translate_x")
QDEF(MP_QSTR_set_translate_y, (const byte*)"\x38\x0f" "set_translate_y")
QDEF(MP_QSTR_set_type, (const byte*)"\xc0\x08" "set_type")
QDEF(MP_QSTR_set_up, (const byte*)"\xbd\x06" "set_up")
QDEF(MP_QSTR_set_update_mode, (const byte*)"\x75\x0f" "set_update_mode")
QDEF(MP_QSTR_set_user_data, (const byte*)"\x66\x0d" "set_user_data")
QDEF(MP_QSTR_set_value, (const byte*)"\xb3\x09" "set_value")
QDEF(MP_QSTR_set_value_by_id, (const byte*)"\x85\x0f" "set_value_by_id")
QDEF(MP_QSTR_set_value_by_id2, (const byte*)"\x17\x10" "set_value_by_id2")
QDEF(MP_QSTR_set_values, (const byte*)"\x60\x0a" "set_values")
QDEF(MP_QSTR_set_var, (const byte*)"\x1d\x07" "set_var")
QDEF(MP_QSTR_set_visible_row_count, (const byte*)"\xbf\x15" "set_visible_row_count")
QDEF(MP_QSTR_set_width, (const byte*)"\xde\x09" "set_width")
QDEF(MP_QSTR_set_worktype, (const byte*)"\xa1\x0c" "set_worktype")
QDEF(MP_QSTR_set_wrap, (const byte*)"\x8c\x08" "set_wrap")
QDEF(MP_QSTR_set_x, (const byte*)"\x20\x05" "set_x")
QDEF(MP_QSTR_set_x_start_point, (const byte*)"\x4c\x11" "set_x_start_point")
QDEF(MP_QSTR_set_y, (const byte*)"\x21\x05" "set_y")
QDEF(MP_QSTR_set_y_invert, (const byte*)"\x6c\x0c" "set_y_invert")
QDEF(MP_QSTR_set_zoom, (const byte*)"\x6f\x08" "set_zoom")
QDEF(MP_QSTR_set_zoom_x, (const byte*)"\x68\x0a" "set_zoom_x")
QDEF(MP_QSTR_set_zoom_y, (const byte*)"\x69\x0a" "set_zoom_y")
QDEF(MP_QSTR_setblocking, (const byte*)"\x6e\x0b" "setblocking")
QDEF(MP_QSTR_setsockopt, (const byte*)"\x38\x0a" "setsockopt")
QDEF(MP_QSTR_setter, (const byte*)"\x04\x06" "setter")
QDEF(MP_QSTR_settimeout, (const byte*)"\xdc\x0a" "settimeout")
QDEF(MP_QSTR_sha1, (const byte*)"\x8e\x04" "sha1")
QDEF(MP_QSTR_sha256, (const byte*)"\x2e\x06" "sha256")
QDEF(MP_QSTR_shadow_color, (const byte*)"\xa1\x0c" "shadow_color")
QDEF(MP_QSTR_shadow_ofs_x, (const byte*)"\xe1\x0c" "shadow_ofs_x")
QDEF(MP_QSTR_shadow_ofs_y, (const byte*)"\xe0\x0c" "shadow_ofs_y")
QDEF(MP_QSTR_shadow_opa, (const byte*)"\xa2\x0a" "shadow_opa")
QDEF(MP_QSTR_shadow_spread, (const byte*)"\x0d\x0d" "shadow_spread")
QDEF(MP_QSTR_shadow_width, (const byte*)"\x5a\x0c" "shadow_width")
QDEF(MP_QSTR_short_val, (const byte*)"\x33\x09" "short_val")
QDEF(MP_QSTR_side, (const byte*)"\x7e\x04" "side")
QDEF(MP_QSTR_sim, (const byte*)"\xb2\x03" "sim")
QDEF(MP_QSTR_sin, (const byte*)"\xb1\x03" "sin")
QDEF(MP_QSTR_single, (const byte*)"\x3f\x06" "single")
QDEF(MP_QSTR_sinh, (const byte*)"\xb9\x04" "sinh")
QDEF(MP_QSTR_sip, (const byte*)"\xaf\x03" "sip")
QDEF(MP_QSTR_size, (const byte*)"\x20\x04" "size")
QDEF(MP_QSTR_sizeof, (const byte*)"\x49\x06" "sizeof")
QDEF(MP_QSTR_sjpeg_init, (const byte*)"\x6b\x0a" "sjpeg_init")
QDEF(MP_QSTR_slaveaddr, (const byte*)"\x7b\x09" "slaveaddr")
QDEF(MP_QSTR_sleep, (const byte*)"\xea\x05" "sleep")
QDEF(MP_QSTR_sleep_ms, (const byte*)"\x0b\x08" "sleep_ms")
QDEF(MP_QSTR_sleep_us, (const byte*)"\x13\x08" "sleep_us")
QDEF(MP_QSTR_slice, (const byte*)"\xb5\x05" "slice")
QDEF(MP_QSTR_slider, (const byte*)"\x60\x06" "slider")
QDEF(MP_QSTR_slider_class, (const byte*)"\xd1\x0c" "slider_class")
QDEF(MP_QSTR_snapshot_buf_size_needed, (const byte*)"\x09\x18" "snapshot_buf_size_needed")
QDEF(MP_QSTR_snapshot_free, (const byte*)"\x42\x0d" "snapshot_free")
QDEF(MP_QSTR_snapshot_take, (const byte*)"\x0d\x0d" "snapshot_take")
QDEF(MP_QSTR_snapshot_take_to_buf, (const byte*)"\x47\x14" "snapshot_take_to_buf")
QDEF(MP_QSTR_socket, (const byte*)"\x60\x06" "socket")
QDEF(MP_QSTR_span_t, (const byte*)"\x02\x06" "span_t")
QDEF(MP_QSTR_spangroup, (const byte*)"\x76\x09" "spangroup")
QDEF(MP_QSTR_spangroup_class, (const byte*)"\xc7\x0f" "spangroup_class")
QDEF(MP_QSTR_speed, (const byte*)"\x62\x05" "speed")
QDEF(MP_QSTR_spi_clk, (const byte*)"\x14\x07" "spi_clk")
QDEF(MP_QSTR_spi_port, (const byte*)"\x29\x08" "spi_port")
QDEF(MP_QSTR_spinbox, (const byte*)"\x74\x07" "spinbox")
QDEF(MP_QSTR_spinbox_class, (const byte*)"\xc5\x0d" "spinbox_class")
QDEF(MP_QSTR_spinner, (const byte*)"\x38\x07" "spinner")
QDEF(MP_QSTR_spinner_class, (const byte*)"\x89\x0d" "spinner_class")
QDEF(MP_QSTR_spx, (const byte*)"\xde\x03" "spx")
QDEF(MP_QSTR_sqrt, (const byte*)"\x21\x04" "sqrt")
QDEF(MP_QSTR_sqrt_res_t, (const byte*)"\xd1\x0a" "sqrt_res_t")
QDEF(MP_QSTR_src, (const byte*)"\x07\x03" "src")
QDEF(MP_QSTR_src_get_type, (const byte*)"\xe9\x0c" "src_get_type")
QDEF(MP_QSTR_src_type, (const byte*)"\x20\x08" "src_type")
QDEF(MP_QSTR_ssid, (const byte*)"\x08\x04" "ssid")
QDEF(MP_QSTR_ssl_params, (const byte*)"\xea\x0a" "ssl_params")
QDEF(MP_QSTR_stack_size, (const byte*)"\x31\x0a" "stack_size")
QDEF(MP_QSTR_start_angle, (const byte*)"\x1b\x0b" "start_angle")
QDEF(MP_QSTR_start_cb, (const byte*)"\x9b\x08" "start_cb")
QDEF(MP_QSTR_start_cb_called, (const byte*)"\x27\x0f" "start_cb_called")
QDEF(MP_QSTR_start_line, (const byte*)"\xb4\x0a" "start_line")
QDEF(MP_QSTR_start_new_thread, (const byte*)"\xd7\x10" "start_new_thread")
QDEF(MP_QSTR_start_point, (const byte*)"\xb6\x0b" "start_point")
QDEF(MP_QSTR_start_value, (const byte*)"\x51\x0b" "start_value")
QDEF(MP_QSTR_stat, (const byte*)"\xd7\x04" "stat")
QDEF(MP_QSTR_state, (const byte*)"\xd2\x05" "state")
QDEF(MP_QSTR_static_flag, (const byte*)"\x6e\x0b" "static_flag")
QDEF(MP_QSTR_stations, (const byte*)"\x2c\x08" "stations")
QDEF(MP_QSTR_status, (const byte*)"\x71\x06" "status")
QDEF(MP_QSTR_statvfs, (const byte*)"\x14\x07" "statvfs")
QDEF(MP_QSTR_stderr, (const byte*)"\xa3\x06" "stderr")
QDEF(MP_QSTR_stdin, (const byte*)"\x21\x05" "stdin")
QDEF(MP_QSTR_stdout, (const byte*)"\x08\x06" "stdout")
QDEF(MP_QSTR_steep, (const byte*)"\xf2\x05" "steep")
QDEF(MP_QSTR_step_next, (const byte*)"\xaf\x09" "step_next")
QDEF(MP_QSTR_step_prev, (const byte*)"\xd9\x09" "step_prev")
QDEF(MP_QSTR_stopAll, (const byte*)"\xfc\x07" "stopAll")
QDEF(MP_QSTR_stopPlayStream, (const byte*)"\xa5\x0e" "stopPlayStream")
QDEF(MP_QSTR_stop_session, (const byte*)"\x3c\x0c" "stop_session")
QDEF(MP_QSTR_stop_thread, (const byte*)"\x4c\x0b" "stop_thread")
QDEF(MP_QSTR_str_val, (const byte*)"\xb4\x07" "str_val")
QDEF(MP_QSTR_struct, (const byte*)"\x12\x06" "struct")
QDEF(MP_QSTR_style, (const byte*)"\xd2\x05" "style")
QDEF(MP_QSTR_style_const_prop_t, (const byte*)"\xa1\x12" "style_const_prop_t")
QDEF(MP_QSTR_style_get_selector_part, (const byte*)"\xd9\x17" "style_get_selector_part")
QDEF(MP_QSTR_style_get_selector_state, (const byte*)"\x39\x18" "style_get_selector_state")
QDEF(MP_QSTR_style_prop_get_default, (const byte*)"\xcd\x16" "style_prop_get_default")
QDEF(MP_QSTR_style_register_prop, (const byte*)"\x86\x13" "style_register_prop")
QDEF(MP_QSTR_style_t, (const byte*)"\x99\x07" "style_t")
QDEF(MP_QSTR_style_transition_dsc_t, (const byte*)"\x82\x16" "style_transition_dsc_t")
QDEF(MP_QSTR_style_v_p_t, (const byte*)"\x9f\x0b" "style_v_p_t")
QDEF(MP_QSTR_style_value_t, (const byte*)"\xcd\x0d" "style_value_t")
QDEF(MP_QSTR_sub_part_ptr, (const byte*)"\x01\x0c" "sub_part_ptr")
QDEF(MP_QSTR_subpx, (const byte*)"\x29\x05" "subpx")
QDEF(MP_QSTR_sw_rotate, (const byte*)"\x87\x09" "sw_rotate")
QDEF(MP_QSTR_switch, (const byte*)"\xb7\x06" "switch")
QDEF(MP_QSTR_switch_class, (const byte*)"\x46\x0c" "switch_class")
QDEF(MP_QSTR_symmetric_difference, (const byte*)"\xce\x14" "symmetric_difference")
QDEF(MP_QSTR_symmetric_difference_update, (const byte*)"\x60\x1b" "symmetric_difference_update")
QDEF(MP_QSTR_sync, (const byte*)"\xa2\x04" "sync")
QDEF(MP_QSTR_sys, (const byte*)"\xbc\x03" "sys")
QDEF(MP_QSTR_sys_layer, (const byte*)"\x60\x09" "sys_layer")
QDEF(MP_QSTR_sysname, (const byte*)"\x9b\x07" "sysname")
QDEF(MP_QSTR_table, (const byte*)"\x7b\x05" "table")
QDEF(MP_QSTR_table_class, (const byte*)"\x0a\x0b" "table_class")
QDEF(MP_QSTR_tabview, (const byte*)"\x5f\x07" "tabview")
QDEF(MP_QSTR_tabview_class, (const byte*)"\xae\x0d" "tabview_class")
QDEF(MP_QSTR_tail, (const byte*)"\x75\x04" "tail")
QDEF(MP_QSTR_tan, (const byte*)"\xfe\x03" "tan")
QDEF(MP_QSTR_tanh, (const byte*)"\xd6\x04" "tanh")
QDEF(MP_QSTR_target, (const byte*)"\xf4\x06" "target")
QDEF(MP_QSTR_task_handler, (const byte*)"\x2f\x0c" "task_handler")
QDEF(MP_QSTR_tell, (const byte*)"\x14\x04" "tell")
QDEF(MP_QSTR_tell_cb, (const byte*)"\x2a\x07" "tell_cb")
QDEF(MP_QSTR_text, (const byte*)"\x98\x04" "text")
QDEF(MP_QSTR_text_is_selected, (const byte*)"\xab\x10" "text_is_selected")
QDEF(MP_QSTR_textarea, (const byte*)"\x0f\x08" "textarea")
QDEF(MP_QSTR_textarea_class, (const byte*)"\x7e\x0e" "textarea_class")
QDEF(MP_QSTR_theme, (const byte*)"\x34\x05" "theme")
QDEF(MP_QSTR_theme_apply, (const byte*)"\xbf\x0b" "theme_apply")
QDEF(MP_QSTR_theme_basic_init, (const byte*)"\xd4\x10" "theme_basic_init")
QDEF(MP_QSTR_theme_default_init, (const byte*)"\x65\x12" "theme_default_init")
QDEF(MP_QSTR_theme_default_is_inited, (const byte*)"\xc1\x17" "theme_default_is_inited")
QDEF(MP_QSTR_theme_get_color_primary, (const byte*)"\x2c\x17" "theme_get_color_primary")
QDEF(MP_QSTR_theme_get_color_secondary, (const byte*)"\xda\x19" "theme_get_color_secondary")
QDEF(MP_QSTR_theme_get_font_large, (const byte*)"\xb3\x14" "theme_get_font_large")
QDEF(MP_QSTR_theme_get_font_normal, (const byte*)"\x3d\x15" "theme_get_font_normal")
QDEF(MP_QSTR_theme_get_font_small, (const byte*)"\x91\x14" "theme_get_font_small")
QDEF(MP_QSTR_theme_get_from_obj, (const byte*)"\xcc\x12" "theme_get_from_obj")
QDEF(MP_QSTR_theme_mono_init, (const byte*)"\x8d\x0f" "theme_mono_init")
QDEF(MP_QSTR_theme_t, (const byte*)"\x3f\x07" "theme_t")
QDEF(MP_QSTR_threadIsRunning, (const byte*)"\x76\x0f" "threadIsRunning")
QDEF(MP_QSTR_threshold, (const byte*)"\xf2\x09" "threshold")
QDEF(MP_QSTR_tick_cnt, (const byte*)"\x76\x08" "tick_cnt")
QDEF(MP_QSTR_tick_color, (const byte*)"\x32\x0a" "tick_color")
QDEF(MP_QSTR_tick_elaps, (const byte*)"\xa4\x0a" "tick_elaps")
QDEF(MP_QSTR_tick_get, (const byte*)"\x99\x08" "tick_get")
QDEF(MP_QSTR_tick_hz, (const byte*)"\x7d\x07" "tick_hz")
QDEF(MP_QSTR_tick_inc, (const byte*)"\xeb\x08" "tick_inc")
QDEF(MP_QSTR_tick_length, (const byte*)"\x33\x0b" "tick_length")
QDEF(MP_QSTR_tick_major_color, (const byte*)"\x76\x10" "tick_major_color")
QDEF(MP_QSTR_tick_major_length, (const byte*)"\x77\x11" "tick_major_length")
QDEF(MP_QSTR_tick_major_nth, (const byte*)"\xf9\x0e" "tick_major_nth")
QDEF(MP_QSTR_tick_major_width, (const byte*)"\xcd\x10" "tick_major_width")
QDEF(MP_QSTR_tick_width, (const byte*)"\x89\x0a" "tick_width")
QDEF(MP_QSTR_ticks_add, (const byte*)"\x9d\x09" "ticks_add")
QDEF(MP_QSTR_ticks_cpu, (const byte*)"\x1a\x09" "ticks_cpu")
QDEF(MP_QSTR_ticks_diff, (const byte*)"\xb1\x0a" "ticks_diff")
QDEF(MP_QSTR_ticks_ms, (const byte*)"\x42\x08" "ticks_ms")
QDEF(MP_QSTR_ticks_us, (const byte*)"\x5a\x08" "ticks_us")
QDEF(MP_QSTR_tileview, (const byte*)"\xbc\x08" "tileview")
QDEF(MP_QSTR_tileview_class, (const byte*)"\x0d\x0e" "tileview_class")
QDEF(MP_QSTR_tileview_tile_class, (const byte*)"\x06\x13" "tileview_tile_class")
QDEF(MP_QSTR_time, (const byte*)"\xf0\x04" "time")
QDEF(MP_QSTR_time_to_open, (const byte*)"\x3f\x0c" "time_to_open")
QDEF(MP_QSTR_timer_cb, (const byte*)"\x7c\x08" "timer_cb")
QDEF(MP_QSTR_timer_create, (const byte*)"\x59\x0c" "timer_create")
QDEF(MP_QSTR_timer_create_basic, (const byte*)"\x5c\x12" "timer_create_basic")
QDEF(MP_QSTR_timer_enable, (const byte*)"\x9c\x0c" "timer_enable")
QDEF(MP_QSTR_timer_get_idle, (const byte*)"\xb0\x0e" "timer_get_idle")
QDEF(MP_QSTR_timer_handler, (const byte*)"\x45\x0d" "timer_handler")
QDEF(MP_QSTR_timer_t, (const byte*)"\x49\x07" "timer_t")
QDEF(MP_QSTR_toggle, (const byte*)"\xb7\x06" "toggle")
QDEF(MP_QSTR_top_layer, (const byte*)"\x72\x09" "top_layer")
QDEF(MP_QSTR_total_size, (const byte*)"\x9d\x0a" "total_size")
QDEF(MP_QSTR_transform, (const byte*)"\x49\x09" "transform")
QDEF(MP_QSTR_tree_walk, (const byte*)"\x6d\x09" "tree_walk")
QDEF(MP_QSTR_trig_activity, (const byte*)"\x3f\x0d" "trig_activity")
QDEF(MP_QSTR_trigo_cos, (const byte*)"\x42\x09" "trigo_cos")
QDEF(MP_QSTR_trigo_sin, (const byte*)"\x89\x09" "trigo_sin")
QDEF(MP_QSTR_trunc, (const byte*)"\x5b\x05" "trunc")
QDEF(MP_QSTR_txt, (const byte*)"\xdd\x03" "txt")
QDEF(MP_QSTR_txt_get_size, (const byte*)"\x8e\x0c" "txt_get_size")
QDEF(MP_QSTR_txt_get_width, (const byte*)"\x0d\x0d" "txt_get_width")
QDEF(MP_QSTR_type_data, (const byte*)"\x72\x09" "type_data")
QDEF(MP_QSTR_types, (const byte*)"\x4e\x05" "types")
QDEF(MP_QSTR_uarray, (const byte*)"\x89\x06" "uarray")
QDEF(MP_QSTR_ubinascii, (const byte*)"\xc4\x09" "ubinascii")
QDEF(MP_QSTR_uchar_val, (const byte*)"\xec\x09" "uchar_val")
QDEF(MP_QSTR_ucollections, (const byte*)"\x15\x0c" "ucollections")
QDEF(MP_QSTR_uctypes, (const byte*)"\xf8\x07" "uctypes")
QDEF(MP_QSTR_uerrno, (const byte*)"\xb4\x06" "uerrno")
QDEF(MP_QSTR_uhashlib, (const byte*)"\x65\x08" "uhashlib")
QDEF(MP_QSTR_uint_val, (const byte*)"\xc7\x08" "uint_val")
QDEF(MP_QSTR_uio, (const byte*)"\xb6\x03" "uio")
QDEF(MP_QSTR_ujson, (const byte*)"\xe8\x05" "ujson")
QDEF(MP_QSTR_umount, (const byte*)"\xdd\x06" "umount")
QDEF(MP_QSTR_uname, (const byte*)"\xb7\x05" "uname")
QDEF(MP_QSTR_uname2, (const byte*)"\xa5\x06" "uname2")
QDEF(MP_QSTR_unblockPin, (const byte*)"\xa0\x0a" "unblockPin")
QDEF(MP_QSTR_underline_position, (const byte*)"\xc5\x12" "underline_position")
QDEF(MP_QSTR_underline_thickness, (const byte*)"\x6a\x13" "underline_thickness")
QDEF(MP_QSTR_unhexlify, (const byte*)"\xb1\x09" "unhexlify")
QDEF(MP_QSTR_uniform, (const byte*)"\x01\x07" "uniform")
QDEF(MP_QSTR_union, (const byte*)"\xf6\x05" "union")
QDEF(MP_QSTR_unpack, (const byte*)"\x07\x06" "unpack")
QDEF(MP_QSTR_unpack_from, (const byte*)"\x0e\x0b" "unpack_from")
QDEF(MP_QSTR_unregister, (const byte*)"\x17\x0a" "unregister")
QDEF(MP_QSTR_uos, (const byte*)"\xec\x03" "uos")
QDEF(MP_QSTR_update_layout, (const byte*)"\x31\x0d" "update_layout")
QDEF(MP_QSTR_update_snap, (const byte*)"\xa7\x0b" "update_snap")
QDEF(MP_QSTR_urandom, (const byte*)"\xab\x07" "urandom")
QDEF(MP_QSTR_ure, (const byte*)"\x87\x03" "ure")
QDEF(MP_QSTR_url, (const byte*)"\x8e\x03" "url")
QDEF(MP_QSTR_url1, (const byte*)"\x7f\x04" "url1")
QDEF(MP_QSTR_url2, (const byte*)"\x7c\x04" "url2")
QDEF(MP_QSTR_useAttachApn, (const byte*)"\x32\x0c" "useAttachApn")
QDEF(MP_QSTR_use_generic_set_px_cb, (const byte*)"\xbc\x15" "use_generic_set_px_cb")
QDEF(MP_QSTR_used_cnt, (const byte*)"\xc4\x08" "used_cnt")
QDEF(MP_QSTR_used_pct, (const byte*)"\xfa\x08" "used_pct")
QDEF(MP_QSTR_uselect, (const byte*)"\x58\x07" "uselect")
QDEF(MP_QSTR_user_data, (const byte*)"\x9b\x09" "user_data")
QDEF(MP_QSTR_ushort_val, (const byte*)"\x06\x0a" "ushort_val")
QDEF(MP_QSTR_usocket, (const byte*)"\x75\x07" "usocket")
QDEF(MP_QSTR_ustruct, (const byte*)"\x47\x07" "ustruct")
QDEF(MP_QSTR_usys, (const byte*)"\xc9\x04" "usys")
QDEF(MP_QSTR_utils, (const byte*)"\xd2\x05" "utils")
QDEF(MP_QSTR_utime, (const byte*)"\xe5\x05" "utime")
QDEF(MP_QSTR_uzlib, (const byte*)"\x6d\x05" "uzlib")
QDEF(MP_QSTR_v, (const byte*)"\xd3\x01" "v")
QDEF(MP_QSTR_v_p, (const byte*)"\x9c\x03" "v_p")
QDEF(MP_QSTR_value1, (const byte*)"\x3f\x06" "value1")
QDEF(MP_QSTR_values_and_props, (const byte*)"\x78\x10" "values_and_props")
QDEF(MP_QSTR_var, (const byte*)"\xe0\x03" "var")
QDEF(MP_QSTR_vect, (const byte*)"\x61\x04" "vect")
QDEF(MP_QSTR_ver_res, (const byte*)"\x1f\x07" "ver_res")
QDEF(MP_QSTR_verify, (const byte*)"\xd2\x06" "verify")
QDEF(MP_QSTR_verifyPin, (const byte*)"\x25\x09" "verifyPin")
QDEF(MP_QSTR_version, (const byte*)"\xbf\x07" "version")
QDEF(MP_QSTR_version_info, (const byte*)"\x6e\x0c" "version_info")
QDEF(MP_QSTR_version_major, (const byte*)"\x7b\x0d" "version_major")
QDEF(MP_QSTR_version_minor, (const byte*)"\x77\x0d" "version_minor")
QDEF(MP_QSTR_version_patch, (const byte*)"\x6e\x0d" "version_patch")
QDEF(MP_QSTR_vertex_p, (const byte*)"\x62\x08" "vertex_p")
QDEF(MP_QSTR_w, (const byte*)"\xd2\x01" "w")
QDEF(MP_QSTR_wait_cb, (const byte*)"\xf0\x07" "wait_cb")
QDEF(MP_QSTR_wait_release, (const byte*)"\x78\x0c" "wait_release")
QDEF(MP_QSTR_wait_until_release, (const byte*)"\xcd\x12" "wait_until_release")
QDEF(MP_QSTR_waiting, (const byte*)"\xee\x07" "waiting")
QDEF(MP_QSTR_wakelock_lock, (const byte*)"\x02\x0d" "wakelock_lock")
QDEF(MP_QSTR_wakelock_unlock, (const byte*)"\x79\x0f" "wakelock_unlock")
QDEF(MP_QSTR_webserver, (const byte*)"\xf0\x09" "webserver")
QDEF(MP_QSTR_width, (const byte*)"\x23\x05" "width")
QDEF(MP_QSTR_width_def, (const byte*)"\x9b\x09" "width_def")
QDEF(MP_QSTR_width_mod, (const byte*)"\x9a\x09" "width_mod")
QDEF(MP_QSTR_win, (const byte*)"\xb5\x03" "win")
QDEF(MP_QSTR_win_class, (const byte*)"\x44\x09" "win_class")
QDEF(MP_QSTR_wrap, (const byte*)"\x51\x04" "wrap")
QDEF(MP_QSTR_writeOnce, (const byte*)"\xbf\x09" "writeOnce")
QDEF(MP_QSTR_writePhonebook, (const byte*)"\xad\x0e" "writePhonebook")
QDEF(MP_QSTR_write_cb, (const byte*)"\x26\x08" "write_cb")
QDEF(MP_QSTR_write_read, (const byte*)"\xf5\x0a" "write_read")
QDEF(MP_QSTR_writeblocks, (const byte*)"\x02\x0b" "writeblocks")
QDEF(MP_QSTR_writebuf, (const byte*)"\x49\x08" "writebuf")
QDEF(MP_QSTR_x, (const byte*)"\xdd\x01" "x")
QDEF(MP_QSTR_x1, (const byte*)"\x4c\x02" "x1")
QDEF(MP_QSTR_x2, (const byte*)"\x4f\x02" "x2")
QDEF(MP_QSTR_x_axis_sec, (const byte*)"\x4b\x0a" "x_axis_sec")
QDEF(MP_QSTR_x_ext_buf_assigned, (const byte*)"\x5a\x12" "x_ext_buf_assigned")
QDEF(MP_QSTR_x_points, (const byte*)"\xdd\x08" "x_points")
QDEF(MP_QSTR_xy_steep, (const byte*)"\x6c\x08" "xy_steep")
QDEF(MP_QSTR_y, (const byte*)"\xdc\x01" "y")
QDEF(MP_QSTR_y1, (const byte*)"\x6d\x02" "y1")
QDEF(MP_QSTR_y2, (const byte*)"\x6e\x02" "y2")
QDEF(MP_QSTR_y_axis_sec, (const byte*)"\xaa\x0a" "y_axis_sec")
QDEF(MP_QSTR_y_bottom, (const byte*)"\x6c\x08" "y_bottom")
QDEF(MP_QSTR_y_ext_buf_assigned, (const byte*)"\xfb\x12" "y_ext_buf_assigned")
QDEF(MP_QSTR_y_points, (const byte*)"\x7c\x08" "y_points")
QDEF(MP_QSTR_y_top, (const byte*)"\xe8\x05" "y_top")
QDEF(MP_QSTR_year, (const byte*)"\x4a\x04" "year")
QDEF(MP_QSTR_yx_steep, (const byte*)"\x4c\x08" "yx_steep")
QDEF(MP_QSTR_zoom, (const byte*)"\xf2\x04" "zoom")
