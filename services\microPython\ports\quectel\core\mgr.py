import utime,osTimer,sim,atcmd,net,modem,audio,ql_fs,pm,SecureData,uping
from machine import Pin, ExtInt,UART
from misc import Power, ADC,USB,USBNET
import EventMesh,ui
from common import Abstract,LogAdapter,Lock
import log
import lvgl as lv
import dataCall
import uos,misc
from machine import RTC
import _thread,ntptime,gc
import network,ethernet
from WebSrv import WebSrv
import api
import binascii
class SOCKET_ERROR_ENUM(object):
    ERR_AGAIN = -1
    ERR_SUCCESS = 0
    ERR_NOMEM = 1
    ERR_PROTOCOL = 2
    ERR_INVAL = 3
    ERR_NO_CONN = 4
    ERR_CONN_REFUSED = 5
    ERR_NOT_FOUND = 6
    ERR_CONN_LOST = 7
    ERR_PAYLOAD_SIZE = 9
    ERR_NOT_SUPPORTED = 10
    ERR_UNKNOWN = 13
    ERR_ERRNO = 14

class TIMER_MODE(object):
    ONE_SHOT = 0 # 单次 TIMER_MODE.ONE_SHOT
    PERIODIC =1  # 周期 TIMER_MODE.PERIODIC

BATTERY_OCV_TABLE = {
    "nix_coy_mnzo2": {
        55: {
            4152: 100, 4083: 95, 4023: 90, 3967: 85, 3915: 80, 3864: 75, 3816: 70, 3773: 65, 3737: 60, 3685: 55,
            3656: 50, 3638: 45, 3625: 40, 3612: 35, 3596: 30, 3564: 25, 3534: 20, 3492: 15, 3457: 10, 3410: 5, 3380: 0,
        },
        20: {
            4143: 100, 4079: 95, 4023: 90, 3972: 85, 3923: 80, 3876: 75, 3831: 70, 3790: 65, 3754: 60, 3720: 55,
            3680: 50, 3652: 45, 3634: 40, 3621: 35, 3608: 30, 3595: 25, 3579: 20, 3548: 15, 3511: 10, 3468: 5, 3430: 0,
        },
        0: {
            4147: 100, 4089: 95, 4038: 90, 3990: 85, 3944: 80, 3899: 75, 3853: 70, 3811: 65, 3774: 60, 3741: 55,
            3708: 50, 3675: 45, 3651: 40, 3633: 35, 3620: 30, 3608: 25, 3597: 20, 3585: 15, 3571: 10, 3550: 5, 3500: 0,
        },
    },
}

BATTERY_TEMP_CHARACTERISTICS_TABLE = {
    1060: -10, 
    1054: -9, 1047: -8, 1040: -7, 1032: -6, 1024: -5, 1017: -4, 1008:-3, 1000: -2, 991: -1,
    982:  0, 973: 1, 964: 2, 954: 3, 944: 4, 934: 5, 924: 6, 913: 7, 902: 8, 891: 9, 880: 10, 
    869: 11, 857: 12, 845: 13, 833: 14, 821: 15, 809: 16, 796: 17, 784: 18, 771: 19, 758: 20, 
    746: 21, 733: 22, 720: 23, 707: 24, 694: 25, 681: 26, 667: 27, 654: 28, 641: 29, 628: 30, 
    615: 31, 602: 32, 590: 33, 577: 34, 564: 35, 551: 36, 539: 37, 527: 38, 514: 39, 502: 40,
    490: 41, 478: 42, 467: 43, 455: 44, 444: 45, 428: 46, 417: 47, 406: 48, 395: 49, 385: 50,
    375: 51, 365: 52, 355: 53, 345: 54, 336: 55
}

class Battery(object):
    """This class is for battery info.

    This class can get battery voltage and energy.
    if adc_args is not None, use cbc to read battery

    adc_args: (adc_num, adc_period, factor)

        adc_num: ADC channel num
        adc_period: Cyclic read ADC cycle period
        factor: calculation coefficient
    """

    def __init__(self, adc_args=None, chrg_gpion=None, stdby_gpion=None):
        self.__energy = 100
        self.__temp = 55
        # print("adc_args:",adc_args,"chrg_gpion",chrg_gpion,"stdby_gpion",stdby_gpion)
        # ADC params
        self.__adc = None
        if adc_args:
            self.__adc_num, self.__adc_period, self.__factor = adc_args
            if not isinstance(self.__adc_num, int):
                raise TypeError("adc_args adc_num is not int number.")
            if not isinstance(self.__adc_period, int):
                raise TypeError("adc_args adc_period is not int number.")
            if not isinstance(self.__factor, float):
                raise TypeError("adc_args factor is not int float.")
            self.__adc = ADC()

        # Charge params
        self.__charge_callback = None
        self.__charge_status = None
        self.__chrg_gpion = chrg_gpion
        self.__stdby_gpion = stdby_gpion
        self.__chrg_gpio = None
        self.__stdby_gpio = None
        self.__chrg_exint = None
        self.__stdby_exint = None
        self.battery_voltage_value = 0
        if self.__chrg_gpion is not None and self.__stdby_gpion is not None:
            self.__init_charge()
    
    def __chrg_callback(self, args):
        self.__update_charge_status()
        if self.__charge_callback is not None:
            self.__charge_callback(self.__charge_status)

    def __stdby_callback(self, args):
        self.__update_charge_status()
        if self.__charge_callback is not None:
            self.__charge_callback(self.__charge_status)

    def __update_charge_status(self):
        if self.__chrg_gpio.read() == 1 and self.__stdby_gpio.read() == 1:
            self.__charge_status = 0
        elif self.__chrg_gpio.read() == 0 and self.__stdby_gpio.read() == 1:
            self.__charge_status = 1
        elif self.__chrg_gpio.read() == 1 and self.__stdby_gpio.read() == 0:
            self.__charge_status = 2
        else:
            raise TypeError("CHRG and STDBY cannot be 0 at the same time!")

    def __init_charge(self):
        self.__chrg_gpio = Pin(self.__chrg_gpion, Pin.IN, Pin.PULL_DISABLE)
        self.__stdby_gpio = Pin(self.__stdby_gpion, Pin.IN, Pin.PULL_DISABLE)
        self.__chrg_exint = ExtInt(self.__chrg_gpion, ExtInt.IRQ_RISING_FALLING, ExtInt.PULL_PU, self.__chrg_callback)
        self.__stdby_exint = ExtInt(self.__stdby_gpion, ExtInt.IRQ_RISING_FALLING, ExtInt.PULL_PU,
                                    self.__stdby_callback)
        self.__chrg_exint.enable()
        self.__stdby_exint.enable()
        self.__update_charge_status()

    def __get_soc_from_dict(self, key, volt_arg):
        """Get battery energy from map"""
        if key in BATTERY_OCV_TABLE["nix_coy_mnzo2"]:
            volts = sorted(BATTERY_OCV_TABLE["nix_coy_mnzo2"][key].keys(), reverse=True)
            pre_volt = 0
            volt_not_under = 0  # Determine whether the voltage is lower than the minimum voltage value of soc.
            for volt in volts:
                if volt_arg > volt:
                    volt_not_under = 1
                    soc1 = BATTERY_OCV_TABLE["nix_coy_mnzo2"][key].get(volt, 0)
                    soc2 = BATTERY_OCV_TABLE["nix_coy_mnzo2"][key].get(pre_volt, 0)
                    break
                else:
                    pre_volt = volt
            if pre_volt == 0:  # Input Voltarg > Highest Voltarg
                return soc1
            elif volt_not_under == 0:
                return 0
            else:
                return soc2 - (soc2 - soc1) * (pre_volt - volt_arg) // (pre_volt - volt)

    def __get_soc(self, temp, volt_arg, bat_type="nix_coy_mnzo2"):
        """Get battery energy by temperature and voltage"""
        if bat_type == "nix_coy_mnzo2":
            if temp > 30:
                return self.__get_soc_from_dict(55, volt_arg)
            elif temp < 10:
                return self.__get_soc_from_dict(0, volt_arg)
            else:
                return self.__get_soc_from_dict(20, volt_arg)

    def __get_power_vbatt(self):
        return int(sum([Power.getVbatt() for i in range(100)]) / 100)

    def __get_adc_vbatt(self):
        print("Battery ADC open")
        self.__adc.open()
        utime.sleep_ms(self.__adc_period)
        adc_list = list()
        for i in range(self.__adc_period):
            adc_list.append(self.__adc.read(self.__adc_num))
            utime.sleep_ms(self.__adc_period)
        adc_list.remove(min(adc_list))
        adc_list.remove(max(adc_list))
        adc_value = int(sum(adc_list) / len(adc_list))
        self.__adc.close()
        vbatt_value = adc_value * (self.__factor + 1)
        return vbatt_value

    def set_temp(self, temp):
        """Set now temperature."""
        if isinstance(temp, int) or isinstance(temp, float):
            self.__temp = temp
            return True
        return False

    def get_voltage(self):
        """Get battery voltage"""
        if self.__adc is None:
            return self.__get_power_vbatt()
        else:
            return self.__get_adc_vbatt()

    def get_battery_voltage_value(self):
        return self.battery_voltage_value

    def get_energy(self):
        """Get battery energy"""
        self.battery_voltage_value = self.get_voltage()
        EventMesh.publish("uart_log","get_energy:"+str(self.battery_voltage_value))
        self.__energy = self.__get_soc(self.__temp, self.battery_voltage_value)
        return self.__energy

    def set_charge_callback(self, charge_callback):
        if self.__chrg_gpion is not None and self.__stdby_gpion is not None:
            if callable(charge_callback):
                self.__charge_callback = charge_callback
                return True
        return False

    def get_charge_status(self):
        return self.__charge_status

"""电池管理器"""
class BatteryManager(Abstract):

    def __init__(self):
        self.battery = Battery()
        self.tempture_value = 0
        self.tempture_beyond = 0
        self.tempture_read_timer = osTimer()

    def post_processor_after_instantiation(self):
        EventMesh.subscribe("get_battery_energy", self.get_energy)
        EventMesh.subscribe("get_tempture_valid", self.get_tempture_valid)
        EventMesh.subscribe("get_battery_voltage",self.get_battery_voltage)
        EventMesh.subscribe("get_battery_celsius",self.get_battery_celsius)
        self.tempture_read_timer.start(10000, 1, self.tempture_read_handle)

    def get_battery_sta(self, event=None, msg=None):
        self.tempture_adc = ADC()
        self.tempture_adc.open()
        temp_value = 0
        min_value = 2000
        max_value = 0
        for i in range(10):
            read_value = self.tempture_adc.read(self.tempture_adc.ADC1)
            if min_value >= read_value:
                min_value = read_value
            if max_value <= read_value:
                max_value = read_value
            temp_value += read_value
        self.tempture_adc.close()
        self.tempture_value = (temp_value-max_value-min_value)/8
        print("电池温度：",self.tempture_value)
        return self.tempture_value

    def get_battery_celsius(self, event=None, msg=None):
        sign = 0
        self.get_battery_sta()
        print("电池温度：",self.tempture_value)
        if self.tempture_value >= 1080:# TODO:待定
            return -11
        if self.tempture_value > 1060:
            return -12
        if self.tempture_value < 336:
            if self.tempture_value < 200 and self.tempture_value > 125:   # 插电池(老硬件)
                return 16
            elif self.tempture_value < 305 and self.tempture_value > 285: # 未插电池(老硬件)
                return -11
            else:
                return -13
        if self.tempture_value == 336:
            return 55
        # 对BATTERY_TEMP_CHARACTERISTICS_TABLE字典降序排序
        damn_dict = sorted(BATTERY_TEMP_CHARACTERISTICS_TABLE.keys(), reverse=True)
        # 根据BATTERY_TEMP_CHARACTERISTICS_TABLE表获取温度值
        for key in damn_dict:
            if self.tempture_value >= key:
                print("KEY值",key)
                _voltageValue = key
                _realTemp = BATTERY_TEMP_CHARACTERISTICS_TABLE[key]
                # 根据中间电压值计算温度取上一个还是下一个
                _voltageValueAvg = (_voltageValue + _voltageValueUp) / 2
                if self.tempture_value > _voltageValueAvg:
                    return _realTemp1
                else:
                    return _realTemp
            else:
                _voltageValueUp = key
                _realTemp1 = BATTERY_TEMP_CHARACTERISTICS_TABLE.get(key)

    def tempture_read_handle(self,args):
        self.tempture_adc = ADC()
        self.tempture_adc.open()
        temp_value = 0
        for i in range(10):
            temp_value += self.tempture_adc.read(self.tempture_adc.ADC1)
        self.tempture_adc.close()
        self.tempture_value = temp_value/10
        # print("温度>>>>>>>>>>>>>>>>>>>>",self.tempture_value)
        if self.tempture_value>=982:  
            if self.tempture_value > 1100:  # 未插电池(新硬件)
                self.tempture_beyond = 0
                EventMesh.publish("control_charge_pin","1")
            else:   #温度过低
                self.tempture_beyond = 1 
                EventMesh.publish("control_charge_pin","0")
                if EventMesh.publish("get_usb_state") == 1:  # 如果插着适配器则弹窗提示
                    EventMesh.publish("window_show", "Abnormal battery temperature")
                    EventMesh.publish("update_screen_battery",EventMesh.publish("get_battery_img"))
        elif self.tempture_value<=444:  
            if self.tempture_value < 305 and self.tempture_value > 285:  # 未插电池(老硬件)
                self.tempture_beyond = 0
                EventMesh.publish("control_charge_pin","1")
            elif self.tempture_value < 200 and self.tempture_value > 125:  # 插电池(老硬件)
                self.tempture_beyond = 0
                EventMesh.publish("control_charge_pin","1")
            else:  # 温度过高
                self.tempture_beyond = 1    #暂时关闭温度检测控制充电
                EventMesh.publish("uart_log","tempture disvalid:"+str(self.tempture_value))
                EventMesh.publish("control_charge_pin","0")
                EventMesh.publish("update_screen_battery",EventMesh.publish("get_battery_img"))
        else:
            self.tempture_beyond = 0
            EventMesh.publish("control_charge_pin","1")
            
        self.tempture_read_timer.stop()
        # if EventMesh.publish("get_IsgoTomain","0") == 0:
        if EventMesh.publish("get_charging_state") == 1:
            self.tempture_read_timer.start(1000,1, self.tempture_read_handle)
        else:
            self.tempture_read_timer.start(60000,1, self.tempture_read_handle)

    def get_tempture_valid(self, event=None, msg=None):
        return self.tempture_beyond

    def get_battery_voltage(self, event=None, msg=None):
        voltage = self.battery.get_battery_voltage_value()
        # print("get_battery_voltage:",voltage)
        EventMesh.publish("uart_log","get_battery_voltage_value :"+str(voltage))
        return voltage

    def get_energy(self, event=None, msg=None):
        battery = self.battery.get_energy()
        return battery

class LowPowerManager(Abstract):
    class Mode:
        LOWER_POWER = 0
        ROUSE_LOWER_POWER = 1
        DEPTH_LOWER_POWER = 2

    def __init__(self):
        self.lpm_name = "lower_power"
        # self.lpm = pm.create_wakelock(self.lpm_name, len(self.lpm_name))
        self.lower_power_state = 1
        self.lock = Lock()
        self.timer_run_flag = 0
        self.timer_new_run_flag = 0
        self.call_time_cnt = 0
        self.call_time_cnt_new = 0
        self.on_call_time = "00:00:00"
        self.on_call_time_new = "00:00:00"
        self.on_call_timer = osTimer()

    def post_processor_after_instantiation(self):
        EventMesh.subscribe("lower_power", self.lower_power)
        EventMesh.subscribe("get_lower_power_state", self.get_lower_power_state)
        EventMesh.subscribe("oncall_timer_start", self.oncall_timer_start)
        EventMesh.subscribe("oncall_timer_stop", self.oncall_timer_stop)
        print("刷新率最高：",misc.fast_extint(1))

    def get_lower_power_state(self, event, msg):
        return self.lower_power_state

    # 低功耗
    def weak(self):
        EventMesh.publish("uart_log","enter sleep mode!!!!!!!!!!!")
        EventMesh.publish("audio_disable")
        print("self.lower_power_state::",self.lower_power_state)
        if self.lower_power_state != 0:
            print("进低功耗1")
            misc.fast_extint(0)
            pm.autosleep(1)
            lv.autoSleep(1)
        self.lower_power_state = 0

    # 唤醒
    def rouse(self):
        EventMesh.publish("uart_log","quit sleep mode!!!!!!!!!!!")
        if self.lower_power_state == 0:
            print("退出低功耗1")
            pm.autosleep(0)
            lv.autoSleep(0)
            misc.fast_extint(1)
        self.lower_power_state = 1

    def lower_power(self, event, mode):
        if int(mode[0]) == self.Mode.LOWER_POWER:
            self.weak()
        elif int(mode[0]) == self.Mode.ROUSE_LOWER_POWER:
            self.rouse()

    def oncall_timer_start(self, topic=None, data=None):
        """开启通话计时器"""
        if self.timer_run_flag==0 and self.timer_new_run_flag==0:
            self.on_call_timer.start(1000, 1, self.call_timer_task)        
        if data == 0:
            self.call_time_cnt = 0
            self.timer_run_flag =1
        elif data == 1:
            self.call_time_cnt_new = 0
            self.timer_new_run_flag =1

    def oncall_timer_stop(self, topic=None, data=None):
        """关闭通话计时器"""
        if data == 0:
            print("stop timee 1")
            self.call_time_cnt = 0
            self.timer_run_flag =0
            self.on_call_time = "00:00:00"
        elif data == 1:
            print("stop timee 2")
            self.call_time_cnt_new = 0
            self.timer_new_run_flag =0
            self.on_call_time_new = "00:00:00"
        if self.timer_run_flag==0 and self.timer_new_run_flag==0:
            EventMesh.publish("audio_disable")
            # EventMesh.publish("set_speaker_state_open")
            self.on_call_timer.stop()

    def call_timer_task(self, args):
        if self.timer_run_flag == 1:
            self.call_time_cnt += 1 
            self.on_call_time = "{0:02d}:{1:02d}:{2:02d}".format(int(self.call_time_cnt/3600), int(self.call_time_cnt//60%60), int(self.call_time_cnt%60))
            EventMesh.publish("oncall_update_time", self.on_call_time)
        if self.timer_new_run_flag == 1:
            self.call_time_cnt_new += 1 
            self.on_call_time_new = "{0:02d}:{1:02d}:{2:02d}".format(int(self.call_time_cnt_new/3600), int(self.call_time_cnt_new//60%60), int(self.call_time_cnt_new%60))
            EventMesh.publish("oncall_new_update_time", self.on_call_time_new)

class DeviceInfoManager(Abstract):
    """
    设备信息管理
    """
    AT = b'AT'

    def __init__(self):
        self.timer_push_schedule = osTimer()
        self.simcard_status_timer = osTimer()
        self.poweron_up_timer = osTimer()
        self.rtc=RTC()
        self.SIM_CARD_READY = 0
        self.simcard_timer_enable = 0
        self.count = 0
        self.tempture_state = 0
        self.tempture_value = 151
        self.time_flag = False
        self.timer_num = 0
        self.dis_operator=""
        # self.battery_push_schedule = osTimer()
        self.check_battery_list = []
        self.battery_voltage = 10000
        self.battery_close_count = 0
        self.battery_waring_count = 0
        self.waring_display_count = 0
        self.pin_open_status = 0
        self.judge_operator_flag = 0
        self.rndis_open = 0
        self.phonebook = []
        self.weekday = ["一", "二", "三", "四", "五", "六", "天"]
        # self.UART_open = 1
        # self.AT_UART = UART(UART.UART3, 115200, 8, 0, 1, 0)
        # self.UAAT_UARTRT.set_callback(self.uart_cb)

        self.__no = UART.UART3
        self.__bate = 115200
        self.__data_bits = 8
        self.__parity = 0
        self.__stop_bits = 1
        self.__flow_control = 0
        self.__uart = None
        self.UART_open = 0
        #电信
        self.VAL_DianXin_MCC_MNC=[
            "46003",
            "46005",
            "46011"
        ]
        #移动
        self.VAL_MOBILE_MCC_MNC=[
            "46000",
            "46002",
            "46004",
            "46007",
            "46008",
            "46013"
        ]
        #联通
        self.VAL_UNICOM_MCC_MNC= [
            "46001",
            "46006",     
            "46009"  
        ]
        #广电
        self.VAL_GUANGDIAN_MCC_MNC=[
            "46015"
        ]

        #SLT
        self.VAL_SLT_MCC_MNC=[
            "41301",
            "41312"
        ]

        self.log = LogAdapter(self.__class__.__name__)
        log.basicConfig(level=log.INFO)
        # gc.enable()
        # gc.threshold(int(gc.mem_free() * 80 // 100)) 
        
        # self.Testlog = log.getLogger("UARTLog")
        # uart = UART(UART.UART0, 115200, 8, 0, 1, 0)
        # log.set_output(uart)
        # self.Testlog.info("Log start!!!!!!!!!!")

    def post_processor_after_instantiation(self):
        """订阅此类所有的事件到 EventMesh中"""
        select_value = EventMesh.publish("persistent_config_get","netSelect")
        if select_value == None:
            select_value =["0","",""]
        if int(select_value[0]) == 1:
            print("手动设置网络")
            resp = bytearray(64)
            if atcmd.sendSync('AT+COPS=1,2,{},7\r\n'.format(select_value[2]),resp,'',60)!=0:
                print("发送AT+COPS失败")        
        EventMesh.subscribe("screen_get_sig", self.get_signal)
        EventMesh.subscribe("screen_get_time", self.get_time)
        EventMesh.subscribe("about_get_imei", self.get_imei)
        EventMesh.subscribe("about_get_iccid", self.get_ic_cid)
        EventMesh.subscribe("about_get_imsi", self.get_imsi)
        EventMesh.subscribe("about_get_pin_status", self.get_pin_status)
        EventMesh.subscribe("about_set_pin_status", self.set_pin_status)
        EventMesh.subscribe("about_get_phonenum", self.get_phone_num)
        EventMesh.subscribe("get_fw_version", self.get_device_fw_version)
        EventMesh.subscribe("get_stamp", self.get_stamp)
        EventMesh.subscribe("get_date", self.get_date)
        EventMesh.subscribe("get_RTC_date", self.get_RTC_date)
        EventMesh.subscribe("get_device_ope", self.get_device_ope)
        EventMesh.subscribe("get_battery_img", self.get_battery_path)
        EventMesh.subscribe("restart_simcard_status_timer", self.watch_simcard_status)
        EventMesh.subscribe("uart_to_usb", self.uart_close_to_usb)
        EventMesh.subscribe("usb_to_uart", self.usb_close_to_uart)
        EventMesh.subscribe("uart_write", self.uart_write)
        EventMesh.subscribe("notify_rejudge", self.reset_rejudge)
        EventMesh.subscribe("readPhonebook", self.readPhonebook)
        EventMesh.subscribe("writePhonebook", self.writePhonebook)
        EventMesh.subscribe("delPhonebook", self.delPhonebook)
        EventMesh.subscribe("pin_disenable_for_update", self.pin_disenable_for_update)
        EventMesh.subscribe("pin_resume_for_update", self.pin_resume_for_update)
        EventMesh.subscribe("uart_log", self.uart_log)
        self.timer_push_schedule_start()
        ret = dataCall.setPDPContext(1, 0, '', '', '', 0)
        print("dataCall.setPDPContext.ret:",ret)
        # self.usb_close_to_uart()
    
    def initialization(self):
        print("开始webUI初始化")
        _thread.start_new_thread(self.webui_init,())
        return True

    def webui_init(self):
        while True:
            info = dataCall.getInfo(1, 0)
            if info[2][2] != '0.0.0.0':
                print('data:', info)
                print('Wait for data calling') 
                # USBNET.set_worktype(USBNET.Type_RNDIS)
                # USBNET.open()
                USBNET.close()
                # eth_init()
                print('USBNET open successed.')
                self.srv = WebSrv(ip='***********', port=80, templatePath="/usr/www/wifi.html", staticPath="/usr/www/", staticPrefix="/static/")
                self.MaxWebSocketRecvLen = 256
                self.srv.WebSocketThreaded = True
                self.srv.AcceptWebSocketCallback = self._acceptWebSocketCallback
                self.srv.Start(threaded=True)
                EventMesh.publish("_set_wifi")
                break
            else:
                utime.sleep(1)

    def _acceptWebSocketCallback(self,webSocket, httpClient):
        global wbsock
        wbsock = webSocket
        print("WS ACCEPT")
        webSocket.RecvTextCallback = self._recvTextCallback
        webSocket.RecvBinaryCallback = self._recvBinaryCallback
        webSocket.ClosedCallback = self._closedCallback

    def _recvTextCallback(self,webSocket, msg):
        print("WS RECV TEXT : %s" % msg)
        webSocket.SendText("Reply for %s" % msg)

    def _recvBinaryCallback(self,webSocket, data):
        print("WS RECV DATA : %s" % data)

    def _closedCallback(self,webSocket):
        print("WS CLOSED")
        global wbsock
        wbsock = None  


    def pin_disenable_for_update(self, event=None, msg=None):
        pin_code = EventMesh.publish("persistent_config_get","PinCode")
        if pin_code != None:
            if pin_code != "":
                if sim.changePin(pin_code, "1234") == 0:
                    print("pin_disenable_for_update CHANGE PIN SUCCESS!!!")
                    EventMesh.publish("persistent_config_store",{"PinCode_resume":pin_code})
                if sim.disablePin("1234") == 0:
                    print("pin_disenable_for_update CLOSE PIN NEW SUCCESS!!!")

    def pin_resume_for_update(self, event=None, msg=None):
        pin_code = EventMesh.publish("persistent_config_get","PinCode_resume")
        if pin_code != None:
            if pin_code != "":
                ret = sim.enablePin("1234")
                if ret == 0:
                    if sim.changePin("1234", pin_code) == 0:
                        EventMesh.publish("persistent_config_store",{"PinCode_resume":""})
                        print("status 0 CHANGE PIN SUCCESS!!!")

    def reset_rejudge(self, topic=None, data=None):
        self.judge_operator_flag = 1

    def uart_write(self, topic=None, data=None):
        EventMesh.publish("uart_log","UART write data:"+data)
        self.__uart.write(data.encode())

    def read(self, number):
        data = self.__uart.read(number)
        if data and isinstance(data, bytes):
            data = data.decode().strip("\r\n")
        return data

    def check(self, data):
        if not data.startswith(self.AT):#检查开头字符串
            return False
        return True

    def at_test_atok(self, topic=None, data=None):
        self.uart_write(data='OK\r\n')

    def at_parse(self, topic=None, data=None):
        EventMesh.publish("uart_log","parse data: {}".format(data))
        if not self.check(data):
            return False
        if len(data) == 2:
            EventMesh.publish("uart_log","=======2======")
            self.at_test_atok()
        else:
            if "=" in data:
                data_list = data.split("=")
                if data_list[0] == "AT+SWITCHUART":
                    if data_list[1] == "0":
                        at_result = '\r\n+SWITCHUART:0\r\n'
                        self.uart_write(data =at_result)
                        utime.sleep_ms(20)
                        self.uart_close_to_usb()
                elif data_list[0] == "AT*MRD_SN":
                    if data_list[1].split(",")[0] == "W":
                        sn_string = data_list[1].split(",")[3]
                        sn_string = sn_string.rstrip("\r\n")
                        EventMesh.publish("NV_config_set",["netAccess",sn_string])
                        self.at_test_atok()
                    else:
                        self.uart_write(data="*MRD_SN:101,Fri Nov 12 00:00:00 2010,{}\r\n".format(EventMesh.publish("NV_config_get", "netAccess")))
                        self.at_test_atok()
                elif data_list[0] == "AT+AWSECW":
                    operator = data_list[1].split(",")[1]
                    EventMesh.publish("uart_log","operator:"+operator)
                    EventMesh.publish("NV_config_set",["lockOperator",operator])
                    self.at_test_atok()
                    net.setModemFun(1)
                    utime.sleep(2)
                    EventMesh.publish("notify_rejudge")
                elif data_list[0] == "AT+AWSECR":
                    operator = EventMesh.publish("NV_config_get","lockOperator")
                    if operator == None:
                        operator = 4
                    at_result = "\r\n+AWSECR:{}\r\n".format(operator)
                    self.uart_write(data=at_result)
                    self.at_test_atok()
                elif data_list[0] == "AT*PROD":
                    if data_list[1] == "1":
                        resp = bytearray(64)
                        if atcmd.sendSync('AT*PROD=1\r\n',resp,'',60) == 0:
                            self.at_test_atok()
                        else:
                            self.uart_write(data=resp.decode("utf-8"))
                elif data_list[0] == "AT+EGMR":
                    resp = bytearray(64)
                    if data_list[1].split(",")[0] == "1":
                        imei = data_list[1].split(",")[2]
                        imei = imei.rstrip("\r\n")
                        if atcmd.sendSync('AT+EGMR=1,7,{}\r\n'.format(imei),resp,'',60) == 0:
                            self.at_test_atok()
                            wifi_info=EventMesh.publish("persistent_config_get","wifi_info")
                            wifi_info["SSID"]="KT4_{}".format(imei[-5:-1])
                            EventMesh.publish("persistent_config_store",{"wifi_info":wifi_info})
                        else:
                            self.uart_write(data=resp.decode("utf-8"))
                    else:
                        self.uart_write(data='+EGMR:\"{}\"\r\n'.format(modem.getDevImei()))
                        self.at_test_atok()
                elif data_list[0] == "AT*NVMFLUSH":
                    if data_list[1] == "1":
                        resp = bytearray(64)
                        if atcmd.sendSync('AT*NVMFLUSH=1\r\n',resp,'',60) == 0:
                            self.at_test_atok()
                        else:
                            self.uart_write(data=resp.decode("utf-8"))
                elif data_list[0] == "AT+APNCONFIGR":
                    if data_list[1] == "0":
                        apn_status = dataCall.getPDPContext(1)
                        if apn_status != -1:
                            at_result = "\r\n+APNCONFIGR:{}\r\n".format(apn_status)
                            self.uart_write(data =at_result)
                            self.at_test_atok()
                        else:
                            print('Get PDP Context fail')
                            EventMesh.publish("uart_log","Get PDP Context fail")
                            at_result = "\r\n+APNCONFIGR:context ERROR\r\n"
                            self.uart_write(data =at_result)
                elif data_list[0] == "AT+APNCONFIGW":
                    second_list = data_list[1].split(",")
                    usrConfig_apn = second_list[0]
                    usrConfig_user = second_list[1]
                    usrConfig_pas = second_list[2]
                    usrConfig = {'apn': usrConfig_apn, 'username': usrConfig_user, 'password': usrConfig_pas}
                    pdpCtx = dataCall.getPDPContext(1)   #(ipType, apn, username, password, authType)
                    if pdpCtx != -1:
                        if pdpCtx[1] != usrConfig['apn']:
                            #(profileID , ipType, apn, username, password, authType)
                            ret = dataCall.setPDPContext(1, pdpCtx[0], usrConfig['apn'], usrConfig['username'], usrConfig['password'], pdpCtx[4])   
                            if ret == 0:
                                EventMesh.publish("uart_log","APN success")
                                at_result = "\r\n+APNCONFIGW:{},{},{}\r\n".format(usrConfig_apn, usrConfig_user, usrConfig_pas)
                                self.uart_write(data =at_result)
                                utime.sleep(2)
                                EventMesh.publish("power_close")
                                Power.powerRestart()  
                            else:
                                EventMesh.publish("uart_log","APN FAIL")
                                at_result = "\r\n+APNCONFIGW:ERROR\r\n"
                                self.uart_write(data =at_result)
                        else:
                            EventMesh.publish("uart_log","APN Configured ")
                    else:
                        print('Get PDP Context fail')
                        EventMesh.publish("uart_log","Get PDP Context fail")
                        at_result = "\r\n+APNCONFIGW:context error\r\n"
                        self.uart_write(data =at_result)
                        
                elif data_list[0] == "AT*CGDFLT": 
                    pdpCtx = dataCall.getPDPContext(1)
                    if pdpCtx != -1:
                        if data_list[1] == "0":  #IPV4
                            ret = dataCall.setPDPContext(1, 0, pdpCtx[1], pdpCtx[2], pdpCtx[3], pdpCtx[4])
                        if data_list[1] == "1":  #IPV6
                            ret = dataCall.setPDPContext(1, 1, pdpCtx[1], pdpCtx[2], pdpCtx[3], pdpCtx[4])
                        if data_list[1] == "2":  #IPV4和IPV6
                            ret = dataCall.setPDPContext(1, 2, pdpCtx[1], pdpCtx[2], pdpCtx[3], pdpCtx[4])
                        if ret == 0:
                            EventMesh.publish("uart_log","success")
                            self.at_test_atok()
                            utime.sleep(2)
                            EventMesh.publish("power_close")
                            Power.powerRestart()  
                        else:
                            EventMesh.publish("uart_log","FAIL")
                            at_result = "\r\n+CGDFLT:ERROR\r\n"
                            self.uart_write(data =at_result)
                    else:
                        print('Get PDP Context fail')
                        EventMesh.publish("uart_log","Get PDP Context fail")
                        at_result = "\r\n+CGDFLT:context error\r\n"
                        self.uart_write(data =at_result)
                                
                elif data_list[0] == "AT+RNDIS":
                    if data_list[1] == "1":
                        USBNET.open()
                        USBNET.get_worktype()
                        if USBNET.set_worktype(USBNET.Type_RNDIS) == 0:
                            at_result = '\r\n+RNDIS:RNDIS OPEN success\r\n'
                            self.uart_write(data =at_result)
                            EventMesh.publish("uart_log","RNDIS OPEN success")
                            EventMesh.publish("power_close")
                            Power.powerRestart()
                        else:
                            EventMesh.publish("uart_log","RNDIS OPEN FAIL")
                            at_result = '\r\n+RNDIS:RNDIS OPEN FAIL\r\n'
                            self.uart_write(data =at_result)
                    if data_list[1] == "0":
                        USBNET.open()
                        USBNET.get_worktype()
                        if USBNET.set_worktype(USBNET.Type_ECM) == 0:
                            at_result = '\r\n+RNDIS:RNDIS CLOSE success\r\n'
                            self.uart_write(data =at_result)
                            EventMesh.publish("uart_log","RNDIS CLOSE success")
                            EventMesh.publish("power_close")
                            Power.powerRestart()       
                        else:
                            EventMesh.publish("uart_log","RNDIS CLOSE FAIL")
                            at_result = '\r\n+RNDIS: RNDIS CLOSE FAIL\r\n'
                            self.uart_write(data =at_result)
                            EventMesh.publish("uart_log","RNDIS CLOSE FAIL")
                    if data_list[1] == "2":
                        USB_net=USBNET.open()
                        if USB_net==-1:
                            self.uart_write(data ="error\r\n")
                        else:
                            self.uart_write(data ="OK\r\n")
                elif data_list[0] == "AT+CCLKR":  
                    if data_list[1] == "0": #查询时间
                        rtc = RTC()
                        local_time = rtc.datetime()
                        at_result = "\r\n+CCLK:{}\r\n".format(local_time)
                        self.uart_write(data =at_result)
                        self.at_test_atok()
                        
                elif data_list[0] == "AT+CCLKW":   #设置时间
                        second_list = data_list[1].split(",")
                        # second_list[0]  # 2023/12/12
                        # second_list[1]  # 14:10:55
                        # second_list[2]  # week
                        week = int(second_list[2])
                        third_list = second_list[0].split("/")
                        year = int(third_list[0])
                        month = int(third_list[1])
                        day = int(third_list[2])
                        
                        fouth_list = second_list[1].split(":")
                        hour = int(fouth_list[0])
                        minute = int(fouth_list[1])
                        second = int(fouth_list[2])
                        
                        rtc = RTC()
                        set_time = rtc.datetime([year, month, day, week, hour, minute, second, 0])
                        if set_time == 0:
                            at_result = "\r\n+CCLKW:{}/{}/{},week:{},{}:{}:{}\r\n".format(year, month, day, second_list[2], hour, minute, second)
                            self.uart_write(data =at_result)
                        else:
                            EventMesh.publish("uart_log","CCLKW FAIL")
                            at_result = "\r\n+CCLKW:ERROR\r\n"
                            self.uart_write(data =at_result)
                            
                elif data_list[0] == "AT+WIFIMACWRITE":  # WIFIMAC地址
                    wifimac=EventMesh.publish("persistent_config_get","wifimac")
                    if data_list[1] == "1":  # 读MAC地址
                        if not wifimac:
                            at_result = '\r\nERROR\r\n'
                            self.uart_write(data=at_result)
                        else:
                            at_result = '\r\nMAC:{},OK\r\n'.format(wifimac)
                            self.uart_write(data=at_result)
                    else:  # 写MAC地址
                        mac = data_list[1]
                        if len(mac) == 12:
                            if mac[0:4] == "e811" or mac[0:4] =="E811":
                                mac = ":".join(mac[i:i+2] for i in range(0, 12, 2))
                                wifimac=mac
                                EventMesh.publish("persistent_config_store",{"wifimac":wifimac})
                                self.uart_write(data='\r\nOK\r\n')
                            else:
                                self.uart_write(data='\r\nERROR\r\n')
                        else:
                            self.uart_write(data='\r\nERROR\r\n')
                
                elif data_list[0] == "AT+ETHMACWRITE":  # ETHMAC地址
                    ethmac = EventMesh.publish("persistent_config_get","ETHMAC")
                    if data_list[1] == "1":  # 读MAC地址
                        if not ethmac:
                            at_result = '\r\nERROR\r\n'
                            self.uart_write(data=at_result)
                        else:
                            at_result = '\r\nMAC:{},OK\r\n'.format(ethmac)
                            self.uart_write(data=at_result)
                    else:  # 写MAC地址
                        mac = data_list[1]
                        if len(mac) == 12 :
                            if mac[0:4] == "e811" or mac[0:4] =="E811":
                                mac = ":".join(mac[i:i+2] for i in range(0, 12, 2))
                                ethmac=mac
                                EventMesh.publish("persistent_config_store",{"ETHMAC":ethmac})
                                self.uart_write(data='\r\nOK\r\n')
                            else:
                                self.uart_write(data='\r\nERROR\r\n')
                        else:
                            self.uart_write(data='\r\nERROR\r\n')
                   
    def mac_str_to_bytes(self,mac_str):
    # 分割MAC地址字符串，并去掉每个部分的冒号
        parts = mac_str.split(':')
        # 将每部分转换为十六进制整数，然后转为字节，并组成bytes对象
        bytes_mac = bytes(int(part, 16) for part in parts)
        return bytes_mac
    
    def uart_cb(self, data):
        raw_data = self.read(data[2])
        EventMesh.publish("uart_log",raw_data)
        self.at_parse(data=raw_data)

    def uart_close_to_usb(self, topic=None, data=None):
        if self.UART_open == 1:
            self.__uart.close()
            self.UART_open = 0
    
    def usb_close_to_uart(self, topic=None, data=None):
        if self.UART_open == 0:
            self.__uart = UART(self.__no, self.__bate, self.__data_bits, self.__parity, self.__stop_bits,
                           self.__flow_control)
            EventMesh.publish("uart_log","uart open")
            self.__uart.set_callback(self.uart_cb)
            EventMesh.publish("uart_log","uart callback is set")
            self.UART_open = 1

    def get_lock_Operator_status(self,opt):
        int_lock = 4
        lock_Operator = EventMesh.publish("NV_config_get","lockOperator")
        if lock_Operator != None:
            int_lock = int(lock_Operator)
        # print("operator:",opt," int_lock:",int_lock)
        if opt == "未知运营商":
            return -1
        if int_lock <= 3:
            if opt == "中国联通" and int_lock==2:
                print("match Operator lock OK")
            elif opt == "中国移动" and int_lock==1:
                print("match Operator lock OK")
            elif opt == "中国电信" and int_lock==0:
                print("match Operator lock OK")
            elif opt == "中国广电" and int_lock==3:
                print("match Operator lock OK")
            else:
                return 1
            return 0
        else:
            return 0
        
    def readpb(self):
        while sim.readPhonebook(9, 1, 2, "")==-1:
            utime.sleep(1)
        ntptime.settime(8)
        for i in range (50):
            phone_book_sim = sim.readPhonebook(9, i*10+1, i*10+10, "")[1]
            for idx, item in enumerate(phone_book_sim):
                if item != (0, '', ''):
                    self.phonebook.append(item)
                else:
                    break
        
    def readPhonebook(self, event=None, msg=None):
        return self.phonebook
    
    def writePhonebook(self, event=None, msg=None):
        name,number = msg
        for i in range (50):
            result = sim.readPhonebook(9, i*10+1, i*10+10, "")
            if result[0] == 0:
                self.phonebook.insert(0, [i*10+1, name, number])
                sim.writePhonebook(9, i*10+1, name, number)
                return 0
            if result[0] != 10:
                for idx,item in enumerate(result[1]):
                    if idx+i*10+1 != item[0]:
                        if sim.writePhonebook(9, idx+i*10+1, name, number) != -1:
                            self.phonebook.insert(idx+i*10, [idx+i*10+1, name, number])
                            return 0
                        else:
                            return -1
                idx=idx+1
                # print("writePhonebook.idx:",idx)
                if sim.writePhonebook(9, idx+i*10+1, name, number) != -1:
                    self.phonebook.insert(idx+i*10, [idx+i*10+1, name, number])
                    return 0
        return -2
          
    def delPhonebook(self, event=None, msg=None):
        num, index=msg
        if num == 1000 and index ==1000: #删除全部
            for i in range (500):
                result = sim.readPhonebook(9, i+1, i+1, "")
                if result != -1:
                    ret = sim.writePhonebook(9, i+1, '', '')
                    if ret == -1:
                        return -1
                else:
                    break
            self.phonebook = []
            return 0        
        else:#删除单条
            ret = sim.writePhonebook(9, index, '', '')
            if ret == -1:
                return -1
            self.phonebook.pop(num)
            return 0
    
    def rndis_open_thread(self):
        while True:
            if USBNET.get_worktype() == 3:
                if USBNET.open() == 0:
                    break
            else:
                break
            utime.sleep(2)
        utime.sleep(2)
        ret = dataCall.getPDPContext(1)
        print("dataCall.getPDPContext.ret:",ret)
        print("open USBNET RNDIS finish")

    def get_simcard_status(self, *args):
        status = sim.getStatus()
        if status == 1:
            if self.rndis_open == 0:
                _thread.start_new_thread(self.rndis_open_thread,())
            self.rndis_open = 1
            operator=self.get_device_ope(None,None)
            if operator == "未知运营商" or operator == "未插卡" or operator == "SIM卡暂时未识别":
                return
            # EventMesh.publish("display_operator_info",operator)
            if self.dis_operator != operator or self.judge_operator_flag==1:
                self.judge_operator_flag = 0
                if self.get_lock_Operator_status(operator) == 1:
                    EventMesh.publish("uart_log","##########Lock Operator#########")
                    print("##########Lock Operator#########")
                    net.setModemFun(4)
                    return
                self.judge_operator_flag == 0
                # print("self.dis_operator:",self.dis_operator," operator:",operator)
                self.dis_operator = operator
                #EventMesh.publish("display_operator_info",operator)
                signal = EventMesh.publish("screen_get_sig")
                if signal:
                    EventMesh.publish("signal",signal)
                print("SIM已经准备好")
                EventMesh.publish("pin_resume_for_update")
                if self.SIM_CARD_READY == 0:
                    _thread.start_new_thread(self.readpb,())
                self.SIM_CARD_READY = 1
                self.httpsurl = EventMesh.publish("persistent_config_get","Url")
                print("self.httpsurl::::::::",self.httpsurl)
                if self.httpsurl != None and self.httpsurl!="":
                    call_update_state = EventMesh.publish("get_call_state")
                    call_update_battery = EventMesh.publish("get_battery_energy")
                    call_update_battery_voltage = EventMesh.publish("get_battery_voltage")
                    print("call_update_state = ", call_update_state)
                    if call_update_battery >= 50 and call_update_battery_voltage >= 3700 and call_update_state=="0":
                        EventMesh.publish("update_compulsion")
                    else:
                        self.poweron_up_timer.start(300000, 1, self.poweron_up)
                else:
                    print("無升級")
                EventMesh.publish("start_link")
            if self.SIM_CARD_READY == 1:
                if EventMesh.publish("get_charging_state")!=1 and EventMesh.publish("get_lower_power_state")==0:
                    if self.simcard_timer_enable == 1:
                        self.simcard_status_timer.stop()
                    self.simcard_timer_enable = 1
                    self.simcard_status_timer.start(300000, 1, self.get_simcard_status)
                else:
                    if self.simcard_timer_enable == 1:
                        self.simcard_status_timer.stop()
                    self.simcard_timer_enable = 1
                    self.simcard_status_timer.start(1000, 1, self.get_simcard_status)
        elif status == 20:
            print("SIM卡无效")
        elif status == 21:
            print("未知状态")
        elif status == 2:
            print("SIM卡已锁定,等待CHV1密码")
            self.pin_open_status = 1
            pin_enable = EventMesh.publish("persistent_config_get","pin")
            if pin_enable==None or int(pin_enable)==0:
                EventMesh.publish("persistent_config_store",{"pin": "1"})
            self.simcard_timer_enable = 0
            self.simcard_status_timer.stop()
            EventMesh.publish("load_screen", {"screen": "keypad_input", "meta_info": ["12",None]})
        elif status == 3:
            print("SIM卡已被阻拦,需要CHV1密码解锁密码")
            self.pin_open_status = 1
            pin_enable = EventMesh.publish("persistent_config_get","pin")
            if pin_enable==None or int(pin_enable)==0:
                EventMesh.publish("persistent_config_store",{"pin": "1"})
            self.simcard_timer_enable = 0
            self.simcard_status_timer.stop()
            EventMesh.publish("load_screen", {"screen": "keypad_input", "meta_info": ["13",None]})
        elif status == 8:
            print("SIM卡已锁定,等待CHV2密码")
            self.pin_open_status = 1
            self.simcard_timer_enable = 0
            self.simcard_status_timer.stop()
            EventMesh.publish("load_screen", {"screen": "keypad_input", "meta_info": ["12",None]})
        elif status == 9:
            print("SIM卡已被阻拦,需要CHV2密码解锁密码")
            self.pin_open_status = 1
            self.simcard_timer_enable = 0
            self.simcard_status_timer.stop()
            EventMesh.publish("load_screen", {"screen": "keypad_input", "meta_info": ["13",None]})
            
    def poweron_up(self, args):
        self.httpsurl = EventMesh.publish("persistent_config_get","Url")
        if self.httpsurl != None and self.httpsurl!="":
            call_update_state = EventMesh.publish("get_call_state")
            call_update_battery = EventMesh.publish("get_battery_energy")
            call_update_battery_voltage = EventMesh.publish("get_battery_voltage")
            print("call_update_state = ", call_update_state)
            if call_update_battery >= 50 and call_update_battery_voltage >= 3700 and call_update_state=="0":
                EventMesh.publish("update_compulsion")
            else:
                print("等待5分钟")
        else:
            self.poweron_up_timer.stop()
    def watch_simcard_status(self, event, msg):
        if self.simcard_timer_enable == 1:
            self.simcard_status_timer.stop()
        self.simcard_timer_enable = 1                    
        self.simcard_status_timer.start(1000, 1, self.get_simcard_status)

    def timer_push_schedule_start(self):
        self.timer_num = 50
        # sec = 60 - utime.localtime()[-3]
        # if sec > 0:
        #     self.timer_num = sec
        # else:
        #     self.timer_num = self.timer_num
        # print("DeviceInfoManager timer",self.timer_num)
        # EventMesh.publish("uart_log","DeviceInfoManager timer :"+str(self.timer_num))
        self.timer_push_schedule.start(self.timer_num * 1000, 1, self.publish_event)

    def publish_event(self, *args, **kwargs):
        self.language = EventMesh.publish("persistent_config_get", "language")
        low_power_state = EventMesh.publish("get_lower_power_state")
        charging_state = EventMesh.publish("get_charging_state")
        if low_power_state == 0:
            # if charging_state == 0:   #不知道为啥要加这个判断
            EventMesh.publish("uart_log","low power close update time")
            EventMesh.publish("lower_power", ["1","1"])
        if EventMesh.publish("get_IsgoTomain","0")==1:
            if EventMesh.publish("get_charging_state")==1:
                if self.battery_waring_count != 0:
                    EventMesh.publish("uart_log","input adapter clear waring count")
                self.battery_waring_count = 0
            else:
                voltage = EventMesh.publish("get_battery_voltage")
                if voltage <= 3600:
                    self.battery_waring_count = self.battery_waring_count+1
                    if self.battery_waring_count >= 6:
                        self.battery_waring_count = 6
                        self.battery_voltage = voltage
                        if self.waring_display_count == 0:
                            if self.language =="zh":
                                EventMesh.publish("window_show","电量低,请及时充电")
                            else:
                                EventMesh.publish("window_show","Battery energy is low,please charge in a timely manner")
                            utime.sleep(2)
                            utime.sleep_ms(100)
                            EventMesh.publish("uart_log","<<<battery energy is low,please charge in a timely manner>>>")
                            self.waring_display_count = 10
                        else:
                            self.waring_display_count = self.waring_display_count-1
                    if voltage <= 3450:
                        self.battery_close_count = self.battery_close_count+1
                        if self.battery_close_count >= 6:
                            self.battery_close_count = 0
                            EventMesh.publish("load_screen", {"screen": "welcome","meta_info":1})
                            utime.sleep(2)
                            utime.sleep_ms(100)
                            EventMesh.publish("power_close")
                            Power.powerDown()
                    else:
                        self.battery_close_count = 0
                else:
                    if self.battery_waring_count >= 6:
                        self.battery_waring_count = self.battery_waring_count+1
                        if self.battery_waring_count >= 10:
                            self.battery_waring_count = 0
                            self.waring_display_count = 0
                            self.battery_voltage = voltage
                    else:
                        self.battery_waring_count = 0
                        self.waring_display_count = 0
                        self.battery_voltage = voltage
                    self.battery_close_count = 0
            EventMesh.publish("update_screen_time", self.get_time())
            EventMesh.publish("update_screen_battery", self.get_battery_path())
            EventMesh.publish("signal", self.get_signal())
            EventMesh.publish("update_screen_sms")
            EventMesh.publish("update_screen_miss")
        if low_power_state == 0:
            if charging_state == 0:
                EventMesh.publish("uart_log","low power open update time")
                EventMesh.publish("lower_power", ["0","1"])

    def uart_log(self, *args):
        return
        # self.Testlog.info(args[1])
    
    def get_signal(self, event=None, msg=None):
        return net.csqQueryPoll()

    def get_time(self, event=None, msg=None):
        local_time = utime.localtime()
        time = "{0:02d}:{1:02d}".format(local_time[3], local_time[4])
        date = "{0:02d}/{1:02d}/{2:02d}".format(local_time[0], local_time[1], local_time[2]) + " {}".format(time)
        result = date
        return result

    def get_date(self, event=None, msg=None):
        """获取日期"""
        local_time = utime.localtime()
        time = "{0:02d}:{1:02d}".format(local_time[3], local_time[4])
        date = "{0:02d}/{1:02d}/{2:02d}".format(local_time[0], local_time[1], local_time[2])
        return [date, time]
    
    def get_RTC_date(self, event=None, msg=None):
        return self.rtc.datetime()

    def get_imei(self, event, msg):
        return modem.getDevImei()

    def get_ic_cid(self, event, msg):
        return sim.getIccid()

    def get_imsi(self, event, msg):
        return sim.getImsi()
    
    def get_pin_status(self, event, msg):
        return self.pin_open_status
    
    def set_pin_status(self, event, msg):
        self.pin_open_status = int(msg)
    
    def get_phone_num(self, event, msg):
        return sim.getPhoneNumber()

    def get_battery_path(self, event=None, bat_num=None):
        if bat_num is None:
            bat_num = EventMesh.publish("get_battery_energy")
        battery_level = int(bat_num / 20)
        if battery_level >= 5:
            battery_level = 4
        if battery_level < 0:
            battery_level = 0
        # usb插入充电且没有控制停充                           电池没有充满则要动态显示充电符号
        if EventMesh.publish("get_charging_state")==1 and EventMesh.publish("get_charging_full_state")==0:
            EventMesh.publish("display_charge_icon_start")            
            return None
        else:
            # 电池供电了或者停止充电或者充满了
            if EventMesh.publish("get_charging_state")==1 and EventMesh.publish("get_charging_full_state")==1:
            # if EventMesh.publish("get_charging_full_state") == 1:
                return "U:/static/charge_battery_4.png"
            if EventMesh.publish("get_usb_state") == 1 and EventMesh.publish("get_tempture_valid") == 1: 
                return "U:/static/charge_battery_tep.png"
            if self.battery_voltage < 3600:
                img_path = 'U:/static/charge_battery_low.png' 
            else:
                img_path = 'U:/static/charge_battery_'+str(battery_level)+'.png'
            return img_path

    def get_device_fw_version(self, *args):
        fw_version = modem.getDevFwVersion()
        if isinstance(fw_version, str):
            return fw_version
        return "--"

    def get_device_ope(self, event, msg):
        """获取运营商"""
        sim_sta=sim.getStatus()
        if sim_sta==0 or sim_sta==20 or sim_sta==21:
            return "未插卡"
        
        sim_imsi = EventMesh.publish("about_get_imsi")
        if sim_imsi==-1:
            return "SIM卡暂时未识别"
        if sim_imsi[0:5] in self.VAL_DianXin_MCC_MNC:
            # print("中国电信")
            return "中国电信"
        elif sim_imsi[0:5] in self.VAL_MOBILE_MCC_MNC:
            # print("中国移动")
            return "中国移动"
        elif sim_imsi[0:5] in self.VAL_UNICOM_MCC_MNC:
            # print("中国联通")
            return "中国联通"
        elif sim_imsi[0:5] in self.VAL_GUANGDIAN_MCC_MNC:
            # print("中国广电")
            return "中国广电"
        elif sim_imsi[0:5] in self.VAL_SLT_MCC_MNC:
            return "SLT"
        else:
            try:
                short_eons = net.operatorName()[1]
            except Exception:
                return "未知运营商"
            return short_eons

    def get_stamp(self,event=None, msg=None):
        # 获取时间戳
        return utime.mktime(utime.localtime())


class WifiManager(Abstract):
    """
    wifi信息管理
    """
    def __init__(self):
        wiif_gpio5 = Pin(Pin.GPIO5, Pin.OUT, Pin.PULL_DISABLE, 1)
        self.wifi_en = Pin(Pin.GPIO7, Pin.OUT, Pin.PULL_DISABLE, 1)
        try:
            self.wifi = network.ASR5803W(0, 7, 5, 28, 6)
        except:
            print("wifi模块初始化失败")
        self.wifi_info={
            "SSID":"KT4_{}".format(EventMesh.publish("get_imei_4")),
            "pwd":"12345678",
            "authmode":5,
            "security":4
        }
        self._thread_wifi_openid=-1
        self.update_pass_timer = osTimer()
        self.wifi_en.write(0)
    def post_processor_after_instantiation(self):
        """订阅此类所有的事件到 EventMesh中"""    
        EventMesh.subscribe("wifi_config", self.wifi_config)
        EventMesh.subscribe("wifi_close", self.wifi_close)
        EventMesh.subscribe("wifi_OPEN", self.wifi_OPEN)
        EventMesh.subscribe("get_wifi_sta", self.get_wifi_sta)
        EventMesh.subscribe("_set_wifi", self._set_wifi)

    def initialization(self):
        return True
      
    def _set_wifi(self,topic=None,data=None):
        _thread.start_new_thread(self.set_wifi,())
        
    def set_wifi(self):
        while True:
            if dataCall.getInfo(1,0)[2][2]!="0.0.0.0":
                self.wifi_info=EventMesh.publish("persistent_config_get","wifi_info")
                wifimac=EventMesh.publish("persistent_config_get","wifimac")
                wifimac=bytearray(int(part, 16) for part in wifimac.split(':'))
                wifimac=bytes(wifimac)
                # print("wifi_mac:",wifimac)
                self.wifi_en.write(1)
                self.wifi.config(ssid= self.wifi_info["SSID"],authmode=self.wifi_info["authmode"],security=self.wifi_info["security"],password=self.wifi_info["pwd"],protocol=4,channel=0,b40acs=1,mac=wifimac)
                info = dataCall.getInfo(1, 0)
                if info[2][2] != '0.0.0.0':
                    print("配数据网络")
                    self.wifi.set_default_NIC(info[2][2])
                if EventMesh.publish("persistent_config_get","wifi_value_flag")==1:
                    self.wifi_status=self.wifi.active(True)
                    print("激活状态：：",self.wifi_status)
                    if self.wifi_status==0:
                        # EventMesh.publish("window_show","Activation successful")
                        print("WIFI激活成功")
                        EventMesh.publish("persistent_config_store",{"wifi_value_flag": 1})
                        EventMesh.publish("update_screen_wifi", 1)
                        if self.wifi_info["pwd"]==EventMesh.publish("persistent_config_get","wifi_primitive_pwd"):
                            EventMesh.publish("window_show", "Please Modify Hotspot Password")
                            self.update_pass_timer.start(300*1000,1,self.win_show)
                        else:
                            self.update_pass_timer.stop()
                    else:
                        # EventMesh.publish("window_show","Activation failed")
                        print("激活失败")
                        self.wifi_en.write(0)
                else:
                    print("不用激活wifi")
                    self.wifi_en.write(0)
                    pass
                    # msg = None
                    # print("开始wifi初始化")
                    # self.wifi_config(msg)
            break
    def get_wifi_sta(self,topic,msg):
        return self.wifi.active()
    
    def gc_check(self,args):
        print("heap内存:",_thread.get_heap_size(),"剩余可用的内存:",gc.mem_free())
        # if gc.mem_free()<10000:
        #     gc.collect()
    def wifi_config(self,topic,msg=None):
        self.wifi_info=EventMesh.publish("persistent_config_get","wifi_info")
        wifimac=EventMesh.publish("persistent_config_get","wifimac")
        wifimac=bytearray(int(part, 16) for part in wifimac.split(':'))
        wifimac=bytes(wifimac)
        if not msg:
            # self.IMEI=EventMesh.publish("about_get_imei")
            # self.wifi_info["ssid"]="KT4_{}".format(str(self.IMEI[-4:]))
            self.wifi_en.write(1)
            utime.sleep_ms(100)
            if self.wifi.active()==0:
                self.wifi.config(ssid= self.wifi_info["SSID"],authmode=self.wifi_info["authmode"],security=self.wifi_info["security"],password=self.wifi_info["pwd"],protocol=4,channel=0,b40acs=1,mac=wifimac)
                # print("wifi信息配置成功")
                info = dataCall.getInfo(1, 0)
                if info[2][2] != '0.0.0.0':
                    print("配数据网络")
                    self.wifi.set_default_NIC(info[2][2])
                    self.wifi_status=self.wifi.active(True)
                    print("激活状态：：",self.wifi_status)
                    if self.wifi_status==0:
                        # EventMesh.publish("window_show","Activation successful")
                        print("WIFI激活成功")
                        EventMesh.publish("persistent_config_store",{"wifi_value_flag": 1})
                        EventMesh.publish("update_screen_wifi", 1)
                    else:
                        # EventMesh.publish("window_show","Activation failed")
                        print("激活失败")
                        self.wifi_en.write(0)
            else:
                print("WIFI已激活")
        else:
            EventMesh.publish("window_show", "WiFi Configuring...") 
            # print("msg =", msg)
            self.wifi_en.write(1)
            utime.sleep_ms(50)
            self.wifi.active(False)
            if "authmode" in msg.keys() and "security" in msg.keys():
                if msg.get("pwd") != "":
                    print("web net pwd is not empty!!!!!!!!")
                    self.wifi.config(ssid="{}".format(msg["SSID"]), password="{}".format(msg["pwd"]),authmode=msg['authmode'], security=msg['security'],mac=wifimac)
                else:
                    print("web net pwd is empty!!!!!!!!")
                    self.wifi.config(ssid="{}".format(msg["SSID"]), authmode=0,security=0,password='',protocol=4,channel=0,b40acs=1,mac=wifimac)
            else:
                if msg.get("pwd") != "":
                    print("pwd is not empty!!!!!!!!")
                    self.wifi.config(ssid="{}".format(msg["SSID"]), authmode=5,security=4, password="{}".format(msg["pwd"]),protocol=4,channel=0,b40acs=1,mac=wifimac)
                else:
                    print("pwd is empty!!!!!!!!")
                    self.wifi.config(ssid="{}".format(msg["SSID"]), authmode=0,security=0,password='',protocol=4,channel=0,b40acs=1,mac=wifimac)
            print("wifi信息配置成功")
            info = dataCall.getInfo(1, 0)
            if info[2][2] != '0.0.0.0':
                print("配数据网络")
                self.wifi.set_default_NIC(info[2][2])
            EventMesh.publish("window_show", "WiFi Opening...") 
            self.wifi_status=self.wifi.active(True)
            if self.wifi_status==0:
                # EventMesh.publish("window_show","Activation successful")
                print("WIFI激活成功")
                EventMesh.publish("persistent_config_store",{"wifi_value_flag": 1})
                EventMesh.publish("update_screen_wifi", 1)
                # EventMesh.publish("persistent_config_store", {"wifi_info": wifi_info})
            else:
                # EventMesh.publish("window_show","Activation failed")
                print("激活失败")
                self.wifi_en.write(0)
        if self.wifi_info["pwd"]==EventMesh.publish("persistent_config_get","wifi_primitive_pwd"):
            EventMesh.publish("window_show", "Please Modify Hotspot Password")
            self.update_pass_timer.start(300*1000,1,self.win_show)
        else:
            self.update_pass_timer.stop()

    def wifi_close(self,topic,msg):
        if self.wifi.active(False)==0:
            self.wifi_en.write(0)
            print("关闭WIFI")
            EventMesh.publish("persistent_config_store",{"wifi_value_flag": 0})
            EventMesh.publish("update_screen_wifi", 0)
            self.update_pass_timer.stop()
            # EventMesh.publish("window_show", "WiFi Closed")
            return 0
        else:
            EventMesh.publish("window_show","Closed failed")
            return -1
        
    def wifi_OPEN(self,topic,msg=None):
        if self._thread_wifi_openid==-1:
            self._thread_wifi_openid=_thread.start_new_thread(self._thread_wifi_open,())
            print('增加密码判断，如果密码是初始密码，定时弹窗提示请修改密码')  
            if EventMesh.publish("persistent_config_get","wifi_info")["pwd"]==EventMesh.publish("persistent_config_get","wifi_primitive_pwd"):
                EventMesh.publish("window_show", "Please Modify Hotpots Password")
                self.update_pass_timer.start(300*1000,1,self.win_show)
        else:
            EventMesh.publish("window_show", "Please Wait")

    def win_show(self,args):
        if EventMesh.publish("persistent_config_get","wifi_info")["pwd"]==EventMesh.publish("persistent_config_get","wifi_primitive_pwd"):
            EventMesh.publish("window_show", "Please Modify Hotspot Password")
        else:
            print("密码修改完成，取消定时")
            self.update_pass_timer.stop()
        
    def _thread_wifi_open(self):
        EventMesh.publish("window_show", "WiFi Opening...")   
        print("a")
        self.wifi_en.write(1)
        utime.sleep_ms(50)
        print("b")
        if self.wifi.active(True)==0:
            print("开启WIFI")
            EventMesh.publish("persistent_config_store",{"wifi_value_flag": 1})
            EventMesh.publish("update_screen_wifi", 1)
            EventMesh.publish("window_show", "WiFi Opened")
            self._thread_wifi_openid=-1
        else:
            EventMesh.publish("window_show","Open failed")
            self.wifi_en.write(0)
            self._thread_wifi_openid=-1

ethernet_wnet = None
class EthernetManager(Abstract):
    """
    以太网信息管理
    """
    def __init__(self):
        # self.check_ethernet_link = osTimer()
        self.ETHMAC=EventMesh.publish("persistent_config_get","ETHMAC")
        if self.ETHMAC!=None:
            mac=bytearray(int(part, 16) for part in self.ETHMAC.split(':'))
            mac=bytes(mac)
            self.ethernet_info={
                "mac":mac,
                "ip":"***********"
            }
        else:
            self.ethernet_info={
                "mac":b'\x12\x34\x56\x78\x9a\xbc',
                "ip":"***********"
            }
        self.ETH_en = Pin(Pin.GPIO39, Pin.OUT, Pin.PULL_DISABLE, 1)
        self.link_flag=0
        self.link_flag_count=0
    def post_processor_after_instantiation(self):
        """订阅此类所有的事件到 EventMesh中"""    
        EventMesh.subscribe("eth_open", self.eth_open)
        EventMesh.subscribe("eth_close", self.eth_close)
        EventMesh.subscribe("get_eth_status", self.eth_status)
        EventMesh.subscribe("ipconfig", self.ipconfig) 
        EventMesh.subscribe("YT_sleep", self.YT_sleep)
        EventMesh.subscribe("eth_init", self.eth_init)
        
    def initialization(self):
        # self.eth_init()
        return True
    
    def mac_to_escaped_string(self, mac):
        if ':' in mac:
            return ''.join([chr(int(i, 16)) for i in mac.split(':')])
        elif '-' in mac:
            return ''.join([chr(int(i, 16)) for i in mac.split('-')]) 
        
    def eth_init(self,topic=None,msg=None):
        print('eth init start')
        Pin(Pin.GPIO36, Pin.OUT, Pin.PULL_DISABLE, 1)
        if EventMesh.publish("persistent_config_get","eth_status")=="1":
            global ethernet_wnet
            try:
                ethernet_wnet = ethernet.YT8512H(self.ethernet_info["mac"],self.ethernet_info["ip"],'','')
            except:
                print("重启33")
                utime.sleep(2)
                EventMesh.publish("power_close")
                Power.powerRestart()
            ethernet_wnet.config(autosleep=True)
            ethernet_wnet.set_dns('*******', '***************')
            nic_info = ethernet_wnet.ipconfig()
            Info = dataCall.getInfo(1,0) 
            ethernet_wnet.set_default_NIC(Info[2][2])
            ethernet_wnet.set_up()
            EventMesh.publish("display_of_network_cable_icons", 1)
            print('eth startup success,设备已配置为网关')
            # self.check_ethernet_link.start(3500,1,self.report_check_ethernet_link)
            # self.check_ethernet_id=_thread.start_new_thread(self.check_ethernet_link, ())
        else:
            EventMesh.publish("display_of_network_cable_icons", 0)
            self.ETH_en.write(0)
            pass    
            
    
    def eth_open(self,topic=None,msg=None):
        self._thread_eth_openid=self._thread_eth_open(msg)
        return self._thread_eth_openid
    
    def _thread_eth_open(self,msg):
        EventMesh.publish("audio_tone_stop")
        if EventMesh.publish("persistent_config_get","eth_status")=="1":
            EventMesh.publish("window_show","Ethernet Has Been Opened")
            return 0
        ETH_Reset = Pin(Pin.GPIO36, Pin.OUT, Pin.PULL_PD, 1)
        ETH_Reset.write(1)
        ETH_Reset.__del__()
        gc.collect()
        Pin(Pin.GPIO36, Pin.OUT, Pin.PULL_DISABLE, 1)
        self.ETH_en.write(1)
        try:
            global ethernet_wnet
            ethernet_wnet = ethernet.YT8512H(self.ethernet_info["mac"],self.ethernet_info["ip"],'','')
        except Exception as e:
            print("重启00")
            EventMesh.publish("persistent_config_store",{"eth_status": "1"})
            utime.sleep(2)
            EventMesh.publish("power_close")
            Power.powerRestart()
        print("重启11")
        EventMesh.publish("persistent_config_store",{"eth_status": "1"})
        utime.sleep(2)
        EventMesh.publish("power_close")
        Power.powerRestart()
        ethernet_wnet.config(autosleep=True)
        ethernet_wnet.set_dns('*******', '***************')
        Info = dataCall.getInfo(1,0)
        ethernet_wnet.set_default_NIC(Info[2][2])
        nic_info = ethernet_wnet.ipconfig()
        print("开启网卡:",ethernet_wnet.set_up())
        print("nic_info:",nic_info)
        EventMesh.publish("persistent_config_store",{"eth_status": "1"})
        EventMesh.publish("display_of_network_cable_icons", 1)
        # self.check_ethernet_link.start(3500,1,self.report_check_ethernet_link)
        # if msg==0:
        #     self.check_ethernet_id=_thread.start_new_thread(self.check_ethernet_link, ())
        return 1

    def eth_close(self,topic=None,msg=None):
        print("关闭以太网")
        if EventMesh.publish("persistent_config_get","eth_status")=="0":
            EventMesh.publish("window_show","Ethernet Has Been Closed")
            return 0
        self.link_flag=0
        # if msg==0:
        #     _thread.stop_thread(self.check_ethernet_id)
        EventMesh.publish("persistent_config_store",{"eth_status": "0"})
        # self.check_ethernet_link.stop()
        EventMesh.publish("display_of_network_cable_icons", 0)
        self.eth_dinit()
        return 1
    
    def eth_dinit(self):    
        # 秦工优化：不需要开dinit接口，并且还能优化底层的网卡注销逻辑
        global ethernet_wnet
        ret = ethernet_wnet.set_down()
        print('网卡关闭状态:{}'.format(ret))
        ethernet_wnet.deinit()
        ethernet_wnet = None
        gc.collect()
        ETH_Reset = Pin(Pin.GPIO36, Pin.OUT, Pin.PULL_PD, 0)
        ETH_Reset.write(0)
        utime.sleep_ms(500)
        self.ETH_en.write(0)
        print('网卡关闭')

    def eth_status(self,topic=None,msg="link"):
        try:
            eth_status=ethernet_wnet.status()
        except:
            print("网卡未启动")
            return False
        if not msg:
            return eth_status
        else:
            if msg == "link":
                return eth_status[2] # 网卡网线是否连接 True or False
            if msg == "dev":
                return eth_status[0] # 网卡设备是否连接正常
            if msg == "active":
                return eth_status[1] # 网卡设备是否激活
            
    def check_ethernet_link(self):
        while True:
            global ethernet_wnet
            link = self.eth_status()
            print("网口状态：",link)
            try:
                print("节点信息：：",ethernet_wnet.node(),ethernet_wnet.speed())
            except:
                break
            if link==True:
                if self.link_flag==0:
                    EventMesh.publish("eth_close",1)
                    utime.sleep_ms(50)
                    EventMesh.publish("eth_open",1)
                    self.link_flag=1
                else:
                    print("已连接")
            else:   
                if ethernet_wnet.speed()[1]==0:
                    if ethernet_wnet.node()!=[]:
                        print("ping信息：：",type(uping.ping(ethernet_wnet.node()[0][1], SOURCE=None, COUNT=1,SIZE=64, TIMEOUT=200, quiet=True)))
                        self.link_flag=0
                        print("已断开")
                else:
                    print("继续检测")
            utime.sleep(3)
        
    def ipconfig(self,topic=None,msg=None):
        mac =self.ETHMAC
        if mac==None:
            mac = "00:12:34:56:78:9a"
        print(mac)
        ip = self.ethernet_info["ip"]
        gateway = self.ethernet_info["ip"]
        return [mac,ip,gateway]
    
    def YT_sleep(self,topic=None,msg=None):
        ethernet_wnet.config(autosleep=True)

class RstExtInt(Abstract):
    def __init__(self):
        self.rst_ext = ExtInt(ExtInt.GPIO5, ExtInt.IRQ_FALLING, ExtInt.PULL_PU, self.rst_cb)
        self.rst_ext.enable()

    def rst_cb(self, args):
        print("RstExtInt args: ", args)

################################################################
#                   config manager 相关
# description: 本地存储管理
# auth:pawn
################################################################
class ConfigStoreManager(Abstract):

    def __init__(self):
        self.file_name = "/usr/conf_store.json"
        self.lock = Lock()
        self.log = LogAdapter(self.__class__.__name__)
        self.map = dict(
            # 未接来电
            missed_call=0,
            # 已接来电
            received_call=0,
            # 已拨来电
            dialed_call=0,
            # 通话记录
            call_logs_list=[],
            # 电话本
            phone_book_all_dict={},
            # 中英文切换
            language="en", # zh:中文 en:英文
            # 闹钟列表["0",Alarm_Clock_name,"00:00","铃声1","自定义",[1,2]]
            Alarm_Clock_list=[["0","a","00:00","铃声1","自定义",[1,3,5]],["0","b","00:00","铃声1","每天",[8]],["0","c","00:00","铃声2","单次",[]]],
            Alarm_Clock_open="0",
			mute_state = 0, #MIC静音状态 1 静音 0 恢复通话
            call_ring=1,
            SMS_ring=3,
            Alarm_clock_ring=2,
            volume_ring="5",
            disturb="0",
            wifi_info ={
                "authmode": 5,
                "security": 4,
                "pwd": "12345678",
                "enable": "false",
                "SSID": "KT4_{}".format(EventMesh.publish("get_imei_4"))
            },
            wifi_primitive_pwd="12345678",
            wifimac='00:12:34:56:78:9a',  
            dhcp_info = {
                "lan_ip": "",
                "dhcp_enable": False,
                "miniprange": "",
                "maxiprange": "",
                "lease_time": "",
                "primary_dns": "",
                "secondary_dns": ""
            },
            ETHMAC='00:12:34:56:78:9a',
            user_info = {
                "username": "admin",
                "password": "admin"
            },
            apn_name = "",
            ntp_host = "",
            eth_status="1",#以太网开关标志 "0" 关 
            wifi_value_flag=0, #wifi开关标志 0关 1开
            eth_first_flag=0,
            backlight_switch=1,

        )

    def post_processor_after_instantiation(self):
        # 同步设备信息补充配置参数
        self.init_config()
        while True:
            if "usr" in uos.listdir():
                break
            else:
                utime.sleep_ms(50)
        if ql_fs.path_exists(self.file_name):
            file_map = ql_fs.read_json(self.file_name)
            for k in self.map.keys():
                if k not in file_map:
                    file_map.update({k: self.map.get(k)})
            self.__store(msg=file_map)
            self.map = file_map
        else:
            self.__store()
        EventMesh.subscribe("persistent_config_get", self.__read)
        EventMesh.subscribe("persistent_config_store", self.__store)
        EventMesh.subscribe("save_call_logs_list", self.save_call_logs)
        EventMesh.subscribe("NV_config_get", self.get_NV_config)
        EventMesh.subscribe("NV_config_set", self.set_NV_config)

    def init_config(self):
        if not self.map:
            raise ValueError("ConfigStoreManager map error")
        # 同步通话记录条数
        call_logs_list = self.map.get("call_logs_list")
        missed_call_len = len([sublist for sublist in call_logs_list if sublist[0] == 0])
        received_call_len = len([sublist for sublist in call_logs_list if sublist[0] == 1])
        dialed_call_len = len([sublist for sublist in call_logs_list if sublist[0] == 2])
        self.map["missed_call"] = missed_call_len
        self.map["received_call"] = received_call_len
        self.map["dialed_call"] = dialed_call_len


    def __read(self, event, msg):
        with self.lock:
            return self.map.get(msg)

    def __store(self, event=None, msg=None):
        if msg is None:
            msg = dict()
        with self.lock:
            self.map.update(msg)
            ql_fs.touch(self.file_name, self.map)

    def save_call_logs(self, event=None, list=None):
        call_record_list = list["call_logs_list"]
        # print(call_record_list)
        list_count = len([sublist for sublist in call_record_list if sublist[0] != 5])
        print("list_count:",list_count)
        if list_count <= 200:
            self.__store(msg=list)
        else:
            i = list_count
            while i > 200:
                i -= 1
                call_record_list.pop(i)
            self.__store(msg={"call_logs_list":call_record_list})

    def get_NV_config(self, event, msg):
        # return modem.getDevSN()
        index = 0
        if msg == "netAccess":
            index = 1
        elif msg == "regCTCC":
            index = 2
        elif msg == "lockOperator":
            index = 3
        elif msg == "regCMCC":
            index = 4
        if index != 0:
            bytedata = bytearray(52)
            byte_len = SecureData.Read(index,bytedata,52)
            if byte_len > 0:
                return bytedata.decode('utf-8').split('\x00')[0] 
            else:
                return None
        else:
            return None

    def set_NV_config(self, event, msg):
        EventMesh.publish("uart_log","[0]"+msg[0])
        EventMesh.publish("uart_log","[1]"+msg[1])
        index = 0
        if msg[0] == "netAccess":
            index = 1
        elif msg[0] == "regCTCC":
            index = 2
        elif msg[0] == "lockOperator":
            index = 3
        elif msg[0] == "regCMCC":
            index = 4  
        if index != 0:
            bytedata = bytearray(52)
            bytedata = msg[1].encode()
            SecureData.Store(index,bytedata,52)

class USBManager(Abstract):
    def __init__(self):
        self.usb = USB()
        #控制是否充电的接口
        # self.power_on_pin = Pin(Pin.GPIO44, Pin.OUT, Pin.PULL_DISABLE, 1)
        self.charging_pin = Pin(Pin.GPIO30, Pin.OUT, Pin.PULL_DISABLE, 1)
        #usb是否连接 getStatus接口获取
        self.usb.setCallback(self.usb_callback)
        #电池电量充满检测的I/O口
        self.charging_end_ext = ExtInt(ExtInt.GPIO9, ExtInt.IRQ_RISING_FALLING, ExtInt.PULL_PU, self.battery_exist_interrupt)
        
        self.battery_exist = 1
        self.charge_pin_ext_count= 0
        self.battery_exist_timer = osTimer()

        self.usb_status = 0
        self.usb_check_count = 0
        self.usb_check_enable= 0
        self.usb_debounce_count = 0
        self.usb_check_timer = osTimer()

        self.battery_charge_full = 0
        self.charge_full_detect_count = 0
        self.delay_enable_timer = osTimer()

        self.tempture_beyond = 0
        self.charger_5V_Pin = 0
        self.start_charging_flag = 0

    def post_processor_after_instantiation(self):
        EventMesh.subscribe("get_charging_state", self.get_charging_state)
        EventMesh.subscribe("control_charge_pin", self.control_charge_pin)
        EventMesh.subscribe("get_charging_full_state", self.get_charging_full_state)
        EventMesh.subscribe("get_usb_state", self.get_usb_state)
        EventMesh.subscribe("get_charger_5V_state", self.get_charger_5V_state)
        
        self.delay_enable_timer.start(5000, 0, self.enable_ext_pin)
        _thread.start_new_thread(self.charging_full_detect_thread,())
        _thread.start_new_thread(self.charger_5V_detect,())
        
    def initialization(self, *args, **kwargs):
        return True
    
    # 检测到usb有插拔动作后检测1秒状态稳定后就处理开始充电或停止充电
    def usb_debounce_check(self,args):
        if self.usb_check_enable == 0:
            print("usb debounce timer is stop. why running continue!")
            return
        self.usb_check_count += 1
        if self.usb_check_count >= 10:
            self.usb_check_timer.stop()
            self.usb_check_count = 0
            self.usb_check_enable = 0
            if self.usb_status == 0:  # 如果usb拔出
                EventMesh.publish("uart_log","stop charge display")
                if EventMesh.publish("get_IsgoTomain","0") == 0:
                    utime.sleep_ms(100)
                    EventMesh.publish("power_close")
                    Power.powerDown()
                EventMesh.publish("wake_up")
                EventMesh.publish("update_screen_battery",EventMesh.publish("get_battery_img"))
            else:   # usb插入
                EventMesh.publish("uart_log","start charge display")
                EventMesh.publish("wake_up")
                if EventMesh.publish("get_tempture_valid") == 0:  #如果当前电池温度正常
                    self.start_charging()
                else:   # 温度异常
                    EventMesh.publish("update_screen_battery",EventMesh.publish("get_battery_img"))
            # if self.usb_debounce_count >= 8:
            #     self.usb_debounce_count = 0
            return

    # usb插拔回调给应用
    def usb_callback(self, status):
        if self.usb_check_enable == 1:
            self.usb_check_timer.stop()
        self.usb_check_enable = 1
        self.usb_check_count = 0
        self.usb_debounce_count = 0
        self.usb_status = self.usb.getStatus()
        self.usb_check_timer.start(100,1,self.usb_debounce_check)

    def start_charging(self):
        self.charging_pin.write(1)
        # if EventMesh.publish("get_lower_power_state") == 0:
            # EventMesh.publish("lower_power", ["1","0"])
        EventMesh.publish("display_charge_icon_start")

    def enable_ext_pin(self,args):
        self.charging_end_ext.enable()
        self.battery_exist_timer.start(100, 0, self.check_battery_exist)

    def check_battery_exist(self,*args):
        self.charging_end_ext.disable()
        if self.charge_pin_ext_count >= 10:
            self.battery_exist = 0
        else:
            self.battery_exist = 1
        self.charge_pin_ext_count = 0
        self.battery_exist_timer.start(600000, 0, self.enable_ext_pin)

    def battery_exist_interrupt(self,args):
        self.charge_pin_ext_count += 1

    # 2ms内读取10次i/o口状态如果相同则取出值，如果有变化说明电池可能被拔掉了，则不去取i/o口的值
    def read_pin_debound(self):
        pin_level = self.charging_end_ext.read_level()
        for i in range(10):
            utime.sleep_us(200)
            if pin_level != self.charging_end_ext.read_level():
                return -1
        return pin_level

    # 检测电池充满电的检测脚，10秒为高则充满，10秒低为未充满，电池不在位不检测
    def charging_full_detect_thread(self):
        while True:
            if self.battery_exist == 1:
                if EventMesh.publish("get_charging_state") == 0:
                    utime.sleep(1)
                    self.battery_charge_full = 0
                    self.charge_full_detect_count = 0
                    continue
                pin_level = self.read_pin_debound()
                if pin_level == -1:
                    utime.sleep(1)
                    continue
                if pin_level == 1:
                    self.charge_full_detect_count += 1
                    if self.charge_full_detect_count == 10:
                        print("charge full")
                        EventMesh.publish("uart_log","charge full")
                        self.battery_charge_full = 1
                    elif self.charge_full_detect_count >= 10:
                        self.charge_full_detect_count = 10
                else:
                    if self.charge_full_detect_count >= 10:
                        self.charge_full_detect_count += 1
                        if self.charge_full_detect_count >= 20:
                            self.charge_full_detect_count = 0
                            print("charge running")
                            self.battery_charge_full = 0
                            self.start_charging()
                utime.sleep(1)
            else:
                self.battery_charge_full = 0
                utime.sleep(30)

    # 获取充电状态 包含了usb是否插充电和是否有控制停充
    def get_charging_state(self, event=None, msg=None):
        if (self.charging_pin.read() and self.usb.getStatus()) or (self.charging_pin.read() and self.charger_5V_Pin != 1) :
        # if self.charging_pin.read():    # 此处解决了插入圆孔充电器无法开启充电状态的问题
            return 1
        else:
            return 0
    
    def charger_5V_detect(self):
        while True:
            # 5V电源检测
            pin_level = self.charging_end_ext.read_level()
            for i in range(10):     # 2ms检测10次
                utime.sleep_us(200)
                if pin_level != self.charging_end_ext.read_level():
                    # print("未插电池")
                    self.charger_5V_Pin = -1
                    break
            if self.charger_5V_Pin == -1:   # 未插电池
                if self.start_charging_flag == 0:
                    self.start_charging()
                    self.start_charging_flag = 1
                utime.sleep(3)
                continue
            # print("5V电源检测>>>>>>>>>>:",pin_level)
            self.start_charging_flag = 0
            if pin_level == self.charger_5V_Pin:
                # print("continue 过滤")
                utime.sleep(3)
                continue
            self.charger_5V_Pin = pin_level
            if self.get_usb_state() == 1:
                # print("usb 插入")
                utime.sleep(3)
                continue
            if self.charger_5V_Pin == 1:  # 如果5v电源充满电或拔出
                if EventMesh.publish("get_IsgoTomain","0") == 0:
                    utime.sleep_ms(100)
                    EventMesh.publish("power_close")
                    Power.powerDown()
                # EventMesh.publish("wake_up")
                # EventMesh.publish("window_show","1111111111111111111")
                EventMesh.publish("update_screen_battery",EventMesh.publish("get_battery_img"))
            else:   # 5v电源插入
                EventMesh.publish("wake_up")
                # EventMesh.publish("window_show","2222222222222222222")
                if EventMesh.publish("get_tempture_valid") == 0:  #如果当前电池温度正常
                    self.start_charging()
                else:   # 温度异常
                    EventMesh.publish("update_screen_battery",EventMesh.publish("get_battery_img"))
            utime.sleep(3)
            # return True if pin_level == 0 else False
            
    def get_charger_5V_state(self, topic=None, data=None):
        return self.charger_5V_Pin
    
    def get_usb_state(self, event=None, msg=None):
        return self.usb.getStatus()

    def control_charge_pin(self, event, msg):
        EventMesh.publish("uart_log","control_charge_pin:".format(int(msg)))
        self.charging_pin.write(int(msg))
        # 温度异常才会设置控制是否充电的I/O口,所以直接在这里把温度是否异常的标志进行一个设置
        if int(msg) == 1:
            self.tempture_beyond = 0
        else:
            self.tempture_beyond = 1

    def get_charging_full_state(self, event=None, msg=None):
        return self.battery_charge_full

class AUDIO_FILE_NAME(object):
    ALARM_1 = "U:/static/R_ALARM_1.mp3"
    SMS_1 = "U:/static/audio_msg.mp3"
    CALL_1 = "U:/static/ring_1.mp3"
    POWER_ON = "U:/static/Startup.mp3"
    POWER_DOWN = "U:/static/R_ALARM_2.mp3"
    RING_1="U:/static/ring_1.mp3"
    RING_2="U:/static/ring_2.mp3"
    RING_3="U:/static/ring_3.mp3"
    RING_4="U:/static/ring_4.mp3"

class AudioManager(Abstract):
    """Audio功能"""
    def __init__(self):
        self.audio_obj = audio.Audio(0)
        # self.tts_obj = audio.TTS(0)
        self.tts_priority = 2
        self.tts_breakin = 1
        # self.tts_mode = 2
        self.audio_gpio = None
        self.howler_tone_count = 0
        self.howler_tone_enable = 0
        self.howler_tone_timer = osTimer()

        #配置功放
        self.pa_gpio=Pin(Pin.GPIO46, Pin.OUT, Pin.PULL_DISABLE, 1)
        # self.audio_obj.set_pa(Pin.GPIO46, 1) 
        self.audio_obj.set_open_pa_delay(100)
        self.log = LogAdapter(self.__class__.__name__)

    def post_processor_after_instantiation(self):
        EventMesh.subscribe("audio_enable", self.audio_enable)
        EventMesh.subscribe("audio_disable", self.audio_disable)
        EventMesh.subscribe("audio_PA_status",self.audio_pin_status)
        EventMesh.subscribe("audio_play", self.audio_play)
        EventMesh.subscribe("audio_play_stop", self.audio_play_stop)
        EventMesh.subscribe("audio_tone", self.aud_tone)
        EventMesh.subscribe("audio_key_tone", self.aud_key_tone)
        EventMesh.subscribe("audio_tone_stop", self.audio_tone_stop)
        EventMesh.subscribe("set_audio_volume", self.set_audio_volume)
        EventMesh.subscribe("howler_tone_control", self.howler_tone_control)
        EventMesh.subscribe("get_howler_tone_enable" ,self.get_howler_tone_enable)
        self.audio_gpio = Pin(Pin.GPIO20, Pin.OUT, Pin.PULL_DISABLE, 0)
        self.audio_disable()

    def audio_enable(self, topic=None, data=None):
        # self.audio_obj.set_pa(Pin.GPIO20,2)
        print("开功放")
        self.pa_gpio.write(1)
        self.audio_gpio.write(1)
   

    def audio_disable(self, topic=None, data=None):
        print("关功放")
        self.pa_gpio.write(0)
        self.audio_gpio.write(0)

    def audio_pin_status(self, topic=None, data=None):
        return self.audio_gpio.read()

    def aud_tone(self, event, msg):
        """按键tone音"""
        # print("aud tone play")
        self.howler_tone_control(event=None,msg=0)
        EventMesh.publish("set_audio_volume", 1)
        self.audio_obj.aud_tone_play(16, 0)

    def aud_key_tone(self, event, msg):
        """按键tone音"""
        # print("aud key tone play msg:",msg)
        self.howler_tone_control(event=None,msg=0)
        print(self.audio_obj.aud_tone_play(msg, 200))

    def audio_play(self, topic=None, filename=None):
        self.audio_enable()
        if filename is None:
            return
        self.howler_tone_control(event=None,msg=0)
        state = self.audio_obj.play(self.tts_priority, self.tts_breakin, filename)
        return True if state == 0 else False

    def audio_play_stop(self, topic=None, data=None):
        """audio stop"""
        EventMesh.publish("audio_disable")
        state = self.audio_obj.stop()
        # print("audio_play_stop",state)
        return True if state == 0 else False

    def audio_tone_stop(self, topic=None, data=None):
        """audio stop"""
        state = self.audio_obj.aud_tone_play_stop()
        return True if state == 0 else False
    
    def set_audio_volume(self, topic=None, vol_num=None):
        # 设置audio音量
        if vol_num == 0:
            vol = EventMesh.publish("persistent_config_get", "volume_ring")     #铃声
            if EventMesh.publish("get_call_state")=="4" or EventMesh.publish("get_call_state")=="9":
                vol = "5"
        elif vol_num == 1:
            vol = EventMesh.publish("persistent_config_get", "key_tone")        #按键
        elif vol_num == 2:
            vol = EventMesh.publish("persistent_config_get", "volume_call")     #通话
        elif vol_num == 3:
            vol = EventMesh.publish("persistent_config_get", "volume_SMS")     #短信
        else:
            vol = "11"
        if vol == None:
            vol = "11"
        if vol_num == 2:
            voiceCall.setVolume(int(vol))
        elif vol_num == 0 or vol_num == 3:# 来电  短信  按键
            # if EventMesh.publish("persistent_config_get","disturb")=="1":#免打扰
            #     print("免打扰模式")
            #     self.audio_obj.setVolume(0)
            # else:
            self.audio_obj.setVolume(int(vol))
        else:
            # print("设置按键音量：：",vol)
            self.audio_obj.setVolume(int(vol))
    
    def howler_tone_control(self, event, msg):
        if self.howler_tone_enable == 1:
            self.audio_obj.aud_tone_play_stop()
            self.howler_tone_enable = 0
        # print("howler_tone_control:",msg)
        if msg == 1:
            if EventMesh.publish("get_call_state")=="0" and  EventMesh.publish("get_hook_state")==0:  #  非通话状态并且弹簧弹起时
                # if EventMesh.publish("persistent_config_get","headset_flag") != 1:、
                print("howler_tone_control:",EventMesh.publish("get_call_state"),EventMesh.publish("get_hook_state"))
                self.howler_tone_count = 6
                self.howler_tone_enable = 1
                self.audio_obj.setVolume(7)
                self.audio_obj.aud_tone_play(16, 0)
                # print("howler_tone_control",self.howler_tone_count)
                self.howler_tone_timer.start(700, 0, self.howler_tone_timer_handle)
            else:
                print("howler_tone_control:",EventMesh.publish("get_call_state"),EventMesh.publish("get_hook_state"))
        else:
            self.howler_tone_count = 1
            self.howler_tone_timer.stop()
        if EventMesh.publish("get_current_screen_name") == "AlarmClockring":
            self.howler_tone_count = 1
            self.howler_tone_timer.stop()

    def get_howler_tone_enable(self,topic,msg):
        return self.howler_tone_enable

    def howler_tone_timer_handle(self,args):
        if self.howler_tone_enable == 1:
            self.audio_obj.aud_tone_play_stop()
            self.howler_tone_enable = 0
        if self.howler_tone_count == 0:
            return
        else:
            self.howler_tone_count = self.howler_tone_count-1
            if (self.howler_tone_count & 1) == 1:
                self.howler_tone_timer.start(700, 0, self.howler_tone_timer_handle)
            else:
                self.howler_tone_enable = 1
                self.audio_obj.aud_tone_play(16, 0)
                self.howler_tone_timer.start(700, 0, self.howler_tone_timer_handle)
        
class VoiceCallManager(Abstract):
    """普通电话功能"""
    def __init__(self):
        # self.voice_call = voiceCall
        self.call_ring_timer = osTimer()
        self.ring_timer_start=0
        self.call_state = "0"
        self.number_1 = ""
        self.number_2 = ""
        self.oncall_num = ""
        self.info = {}
        self.log = LogAdapter(self.__class__.__name__)

    def post_processor_after_instantiation(self):
        EventMesh.subscribe("call_start", self.call_start)
        EventMesh.subscribe("call_answer", self.call_answer)
        EventMesh.subscribe("call_end", self.call_end)
        EventMesh.subscribe("call_mic_mute", self.mic_mute)
        EventMesh.subscribe("get_call_state",self.call_state_func)
        EventMesh.subscribe("ignore_comingcall",self.stop_ring)
        EventMesh.subscribe("SIP_ringtone",self.SIP_ringtone)
        EventMesh.subscribe("set_call_state",self.set_call_state)
        """设置通话回调"""
        #self.voice_call.setCallback(self.call_callback)

    def mic_mute(self,topic=None, data=None):
        print("VoiceCallManager:mic_mute ",data)
        if data == "0":
            #静音
            pass
            # self.voice_call.mute(2, 1)
        else:
            #静音恢复
            pass
            # self.voice_call.mute(2, 0)
            
    def SIP_ringtone(self,topic=None, data=None):
        self.call_ringtone_task(None)#电话响铃
        
    def call_ringtone_task(self, args):
        """电话响铃"""
        #如果手柄挂机，声音切到免提
        if EventMesh.publish("get_hook_state"):
            EventMesh.publish("audio_enable")
        #配置通话音量 

        EventMesh.publish("set_audio_volume", 0)
        #播放铃声
        ring=EventMesh.publish("persistent_config_get", "call_ring")
        print("ring  start ring!!!!!!!!!!!=:",ring)
        if ring==1:
            EventMesh.publish("audio_play", AUDIO_FILE_NAME.RING_1)
        elif ring==2:
            EventMesh.publish("audio_play", AUDIO_FILE_NAME.RING_2)
        elif ring==3:
            EventMesh.publish("audio_play", AUDIO_FILE_NAME.RING_3)
        elif ring==4:
            EventMesh.publish("audio_play", AUDIO_FILE_NAME.RING_4)
        if self.ring_timer_start == 1:
            self.call_ring_timer.stop()
        self.ring_timer_start = 1
        if ring==3 or ring==4:
            self.call_ring_timer.start(2 * 1000, 1, self.call_ringtone_task)
        else:
            self.call_ring_timer.start(8 * 1000, 1, self.call_ringtone_task)
    def stop_ring(self, topic=None, args=None):
        if self.ring_timer_start == 1:
            self.call_ring_timer.stop()
            self.ring_timer_start = 0
        EventMesh.publish("audio_play_stop")
            
    def call_start(self, topic=None, phonenum=None):
        """拨打电话"""
        print(" call start phone:",phonenum)
        EventMesh.publish("howler_tone_control",0)
        if phonenum:
            print(EventMesh.publish("get_sip_registered"),EventMesh.publish("get_SIP_call_state"))
            #SIP电话
            # if EventMesh.publish("get_sip_registered")==1 and int(EventMesh.publish("GetConfigData", '48')):#SIP优先 且 注册上SIP服务器
            if EventMesh.publish("get_sip_registered")==1:#SIP优先 且 注册上SIP服务器
                print("sip call_start !!!")
                if EventMesh.publish("get_SIP_call_state") in [0,4]:#挂机状态
                    self.call_state = "1"
                    # 如果手柄挂机，声音切到免提
                    if EventMesh.publish("get_hook_state") :  
                        EventMesh.publish("audio_enable") 
                    
                    if EventMesh.publish("call_start_voip",[phonenum])==-1:
                        print("call_start_voip ERROR")
                    else:
                        self.number_1 = phonenum
                        print("SIP 呼出成功！")
                        # audio.Audio(0).setVolume(int(EventMesh.publish("GetConfigData", '51'))*2)
                else:
                    print("SIP非挂机状态->",EventMesh.publish("get_SIP_call_state"))
            else:  #普通电话
                print("4G call_start !!!")
                try:
                    self.mic_mute(data="1")
                    print(self.call_state,"->1")
                    self.call_state = "1"
                    # 如果手柄挂机，声音切到免提
                    if EventMesh.publish("get_hook_state") :  
                        EventMesh.publish("audio_enable") 
                    state = self.voice_call.callStart(phonenum)
                    self.log.info("call_start state {}".format(state))
                    return state
                except Exception:
                    self.log.error("call_start error")
                    return False

    def call_answer(self, topic=None, args=None):
        """接听电话"""
        if EventMesh.publish("get_sip_registered")==1 and EventMesh.publish("get_SIP_call_state")==1:#SIP优先 且 注册上SIP服务器
            print("sip call_answer !!!")
            self.stop_ring()
            if EventMesh.publish("get_hook_state") == 0:
                EventMesh.publish("audio_disable")
            else:
                EventMesh.publish("audio_enable")
            EventMesh.publish("call_answer_voip")#接听电话 
        else:
            print("4G call_answer !!!")
        try:
            self.stop_ring()
            # self.mic_mute(data="1")
            if EventMesh.publish("get_hook_state") == 0:
                EventMesh.publish("audio_disable")
            else:
                EventMesh.publish("audio_enable")
            if args == 0:
                #   来电1振铃中
                if self.call_state == "3" or self.call_state == "1":
                    # 按接通键告诉基站接通
                    print(self.call_state,"->5")
                    self.call_state = "5"
                    # state = self.voice_call.callAnswer()
                    # self.callAnswer=state
                    # print("接听状态：：：",state)
                    # if state==-1:
                    #     if self.call_state == "0":
                    #         return
                    #     else:
                    #         EventMesh.publish("call_end",1)
                #   在通话2在通话来电1振铃
                elif self.call_state == "13":
                    print(self.call_state,"->7")
                    self.call_state = "7"
                    # 接通第三方，挂起当前通话等待
                    # state = self.voice_call.callHold(2)
                # self.log.info("call_answer state {}".format(state))
            else:
                #  呼叫通话中来电2按键接通
                if self.call_state == "4":
                    print(self.call_state,"->6")
                    self.call_state = "6"
                #  被叫通话中来电2按键接通
                elif self.call_state == "9":
                    print(self.call_state,"->10")
                    self.call_state = "10"
                # 接通第三方，挂起当前通话等待
                # state = self.voice_call.callHold(2)
        except Exception:
            self.log.error("call_answer error")

    def call_end(self, topic=None, data=None):
        """挂断电话"""
        self.stop_ring()
        print("call_end start")
        if self.ring_timer_start == 1:
            self.call_ring_timer.stop()
            self.ring_timer_start = 0
        if EventMesh.publish("get_sip_registered")==1 and EventMesh.publish("get_SIP_call_state")!=0:#SIP优先 且 注册上SIP服务器 
            print("SIP call_end !!!")
            if EventMesh.publish("get_SIP_call_state") == 2:#通话中
                EventMesh.publish("call_end_voip")#挂断电话
            elif EventMesh.publish("get_SIP_call_state") == 1:#来电
                EventMesh.publish("call_bye_voip")#挂断电话
            elif EventMesh.publish("get_SIP_call_state")==3:#呼出中
                EventMesh.publish("call_cancel_voip")#取消拨打电话
                
            elif EventMesh.publish("get_SIP_call_state")==4:#呼出中
                EventMesh.publish("call_failed_voip")
            # self.update_call_log(self.number_1)#挂断电话上报话单  
            self.number_1 = ""
            self.call_state = "0"
            EventMesh.publish("audio_disable")
            if self.call_state=="3":
                self.stop_ring()
            self.oncall_num = ""
        else:
            print("4G call_end !!!")
        try:
            self.log.info("call_end : data{}".format(data))

            if data ==0 or data==1 or data==3 or data==5:
                self.number_1 = ""
            else:
                self.number_2 = ""
            #       呼叫中                   呼叫接通 1              来电1                    来电接通 1         已经挂断了通话1，保留通话2    黑白名单挂断
            if self.call_state=="1" or self.call_state=="2" or self.call_state=="3" or self.call_state=="7" or self.call_state=="13" or self.call_state == "0" or self.call_state == "5":
                # 这些状态直接转到挂机
                print(self.call_state,"->0")
                if self.call_state=="13":
                    self.number_2 = ""
                else:
                    self.number_1 = ""
                    
                self.call_state = "0"
                EventMesh.publish("audio_disable")
                if self.call_state=="3":
                    self.stop_ring()
                # state = self.voice_call.callEnd()
                self.mic_mute(data="0")
                self.oncall_num = ""
            #      呼叫通话中来电 2            被叫通话中来电 2    
            elif self.call_state=="4" or self.call_state=="9":
                # 这些状态来电中挂断是拒接 恢复呼叫接通 1和来电接通 1
                if self.call_state=="4":
                    print(self.call_state,"->2")
                    self.call_state = "2"
                else:
                    print(self.call_state,"->7")
                    self.call_state = "7"
                self.stop_ring()
                # 挂断第三方通话
                # state = self.voice_call.callEnd()
                # state = self.voice_call.callHold(0)
            #         呼叫通话中被叫2通话       被叫通话中被叫2通话
            elif self.call_state=="8" or self.call_state=="11":
                # 这些状态挂断被叫通话2 恢复呼叫接通 1和来电接通 1
                if self.call_state == "8":
                    if data == 3 or data == 5:
                        print(self.call_state,"->13")
                        self.call_state = "13"
                        self.oncall_num = self.number_2
                    elif data == 4 or data == 6:    
                        print(self.call_state,"->2")
                        self.call_state = "2"
                        self.oncall_num = self.number_1
                elif self.call_state=="11":
                    if data == 3 or data == 5:
                        print(self.call_state,"->13")
                        self.call_state = "13"
                        self.oncall_num = self.number_2
                    else:
                        print(self.call_state,"->2")
                        self.call_state = "7"
                        self.oncall_num = self.number_1
                
                # 挂断当前通话恢复第三方通话
                # if data==5 or data==6:
                #     state = self.voice_call.callHold(0)
                # else:
                #     state = self.voice_call.callHold(1)
        except Exception:
            self.log.error("call_end error")

    def call_state_func(self,topic=None,data=None):  
        return self.call_state
    
    def set_call_state(self,topic=None,data=None): 
        self.call_state=data

    def call_callback(self, args):
        """通话状态回调"""
        self.log.info("voicecall callback args {}".format(args))
        if args[0]>=10 and args[0]<=16:
            temp_str = str(args[6])
            if temp_str[0]=='9' and temp_str[1]=='4':
                str_number = str(temp_str).lstrip('94')
                if str_number[0] == '0':
                    phone_num = str_number
                else:
                    phone_num = '0'+ str_number
            else:
                phone_num = str(args[6])
            print("call_callback phone_num:",phone_num)

        if args[0] == 1:
            self.log.info("voicecall init")
        elif args[0] == 2:
            # 来电通知
            self.log.info("Call in: {}, {}".format(args[1], args[2]))
        elif args[0] == 3:
            # 电话接通
            self.log.info("Call answer: {}, {}".format(args[1], args[2]))
        elif args[0] == 4:
            # 通话挂断
            self.log.info("Call end: {}, {}".format(args[1], args[2]))
        elif args[0] == 5:
            # 通话挂断
            self.log.error("Call error")
        elif args[0] == 6:
            # 呼叫等待
            print("Call wait args:",args)
        elif args[0] == 7:
            # 呼出中
            print("Call out args:",args)
        elif args[0] == 8:
            # 呼出中
            print("Call out fail args:",args)
        elif args[0] == 9:
            # 呼出中
            print("wait args:",args)
        elif args[0] == 10:
            # 来电通知（volte通话）
            if EventMesh.publish("get_current_screen_name")=="AlarmClockring":
                EventMesh.publish("delayed_alarm")
            if phone_num=='':
                phone_num="UnknownNumber"
            phone_name = EventMesh.publish("get_phone_number_name", phone_num)
            if self.whitelist_handle(phone_num) or self.blacklist_handle(phone_num):
                self.call_end(0)
                print("黑白名单挂断")
                return 
            EventMesh.publish("wake_up")
            print("volte call on args:",args)
            if self.call_state == "2":   #有呼出的通话  1出  通了的状态   ==1呼出号码还没接通，呼出后转到11，接通后就是2
                print(self.call_state,"->4")
                # 主叫通话后来电
                print("呼出通话后来电")
                self.call_state = "4"    #第二个通话打进来  1出2进
                EventMesh.publish("load_screen", {"screen": "call_up_new", "meta_info": [phone_name, phone_num]})
            elif self.call_state == "7":   #1进  通话中
                print(self.call_state,"->9")
                # 被叫通话后来电
                print("来电1通话后再次来电2")
                self.call_state = "9"    #1进2进
                EventMesh.publish("load_screen", {"screen": "call_up_new", "meta_info": [phone_name, phone_num]})
            elif self.call_state == "13":    #两个通话
                print(self.call_state,"->3")
                # 挂断第一通通话保持第二路通话时来电
                self.call_state = "3"  
                EventMesh.publish("load_screen", {"screen": "call_up", "meta_info": [phone_name, phone_num]})
            elif self.call_state == "0":   #无到第一通电话
                print(self.call_state,"->3")
                # 挂机状态下来电
                self.call_state = "3"
                print("First Incoming call:",phone_name,phone_num)
                EventMesh.publish("load_screen", {"screen": "call_up", "meta_info": [phone_name, phone_num]})       
            elif self.call_state == "1":   #正在呼出中到来电第一通电话
                print(self.call_state,"->3")
                # 挂机状态下来电
                self.call_state = "3"
                print("First Incoming call:",phone_name,phone_num)
                EventMesh.publish("load_screen", {"screen": "call_up", "meta_info": [phone_name, phone_num]})  
            if EventMesh.publish("persistent_config_get","disturb")=="1":#免打扰
                print("免打扰模式")
                pass
            else:        
                self.call_ringtone_task(None)
        elif args[0] == 11:
            # 通话接通（volte通话）
            print("volte call answer args:",args)
            EventMesh.publish("set_audio_volume", 2)
            if phone_num=='':
                phone_num="UnknownNumber"
            phone_name = EventMesh.publish("get_phone_number_name", phone_num)

            self.oncall_num = phone_num
            if phone_num == self.number_1:  #呼叫等待切换通话时，防止再次进入通话接通
                return
            if phone_num == self.number_2:
                return
            
            if args[2] == 0:
                print(self.call_state,"->2")
                if self.call_state == "1":
                    self.call_state = "2"
                EventMesh.publish("oncall_timer_start",0)
                self.number_1 = phone_num
                self.info ={"name":phone_name,"number":phone_num,"flag":"0"}
                EventMesh.publish("load_screen", {"screen": "on_call", "meta_info": self.info})
            else:
                if self.call_state == "5" or self.call_state == "3":
                    #来电后 接通第一个来电
                    print(self.call_state,"->7")
                    self.call_state = "7"
                    EventMesh.publish("oncall_timer_start",0)
                    self.number_1 = phone_num
                    self.info ={"name":phone_name,"number":phone_num,"flag":"0"}
                    EventMesh.publish("load_screen", {"screen": "on_call", "meta_info":self.info})
                else:
                    #通话过程中 接通第二个电话
                    if self.call_state == "6":
                        print(self.call_state,"->8")
                        self.call_state = "8"
                    elif self.call_state == "10":
                        print(self.call_state,"->11")
                        self.call_state = "11"
                    EventMesh.publish("oncall_timer_start",1)
                    self.number_2 = phone_num
                    self.info ={"name":phone_name,"number":phone_num,"flag":"0"}
                    EventMesh.publish("load_screen", {"screen": "on_call_new", "meta_info": self.info})
        elif args[0] == 12:
            # 对方挂断通话（volte通话）
            print("volte call end args:",args)
            self.stop_ring()
            if phone_num=='':
                phone_num="UnknownNumber"
            print(phone_num," number_1:",self.number_1," number_2:",self.number_2)
            if phone_num!=self.number_1 and phone_num!=self.number_2:
                print("self.call_state_args12", self.call_state)
                if self.call_state=="3" or self.call_state=="4" or self.call_state=="9" or self.call_state=="1" or self.call_state=="5" or self.call_state=="0":
                    print("call_callback===>>>>>btn_pwk_click_end")
                    EventMesh.publish("btn_pwk_click")
                return
            if phone_num == self.number_1:  #挂断的号码是第一个界面的号码
                # print("11111 挂断的号码是第一个界面的号码")
                # print("11111 当前通话号码 self.oncall_num=",self.oncall_num)
                # print("11111 当前通话状态值 self.call_state=",self.call_state)
                
                if self.oncall_num != self.number_1:
                    print("当前通话号码 self.oncall_num1_1=",self.oncall_num)
                    voiceCall.callHold(0)  # 挂断保持状态的通话
                    EventMesh.publish("save_call_log")
                    if self.call_state=="11":
                        print(self.call_state,"->7")
                        self.call_state = "7"
                    elif self.call_state=="8":
                        print(self.call_state,"->13")
                        self.call_state = "13"
                    if EventMesh.publish("get_current_screen_name") == "oncallselete":
                        EventMesh.publish("oncallselete_return")
                else:
                    self.oncall_num = self.number_2
                    print("当前通话号码 self.oncall_num1_2=",self.oncall_num)
                    if EventMesh.publish("get_current_screen_name") == "oncallselete":
                        EventMesh.publish("oncallselete_return")
                    EventMesh.publish("btn_pwk_click")
            if phone_num == self.number_2:
                # print("22222 挂断的号码是第二个界面的号码")
                # print("22222 当前通话号码 self.oncall_num=",self.oncall_num)
                # print("22222 当前通话状态值 self.call_state=",self.call_state)
                if self.oncall_num != self.number_2:
                    print("self.oncall_num2_1=",self.oncall_num)
                    voiceCall.callHold(0)
                    EventMesh.publish("save_call_new_log")
                    print(self.call_state,"->2")
                    self.call_state = "2"                    
                    if EventMesh.publish("get_current_screen_name") == "oncallselete":
                        EventMesh.publish("oncallselete_return")
                else:
                    self.oncall_num = self.number_1
                    print("self.oncall_num2_2=",self.oncall_num)
                    if EventMesh.publish("get_current_screen_name") == "oncallselete":
                        EventMesh.publish("oncallselete_return")
                    EventMesh.publish("btn_pwk_click")
        elif args[0] == 13:
            # 呼叫等待（volte通话）
            print("volte call wait args:",args)
        elif args[0] == 14:
            # 呼出中（volte通话）
            print("volte call out args:",args)
        elif args[0] == 15:
            # 呼出中，对方未响铃（volte通话）
            print("volte call out 2 args:",args)
        elif args[0] == 16:
            # 等待（volte通话）
            print("volte wait args:",args)
        else:
            self.log.info("voicecall error")

    def blacklist_handle(self,number):
        blacklist = EventMesh.publish("persistent_config_get", "blacklist")
        if blacklist == None:  
            blacklist = []
            return 0
        if number in blacklist:
            return 1
        return 0
    
    def whitelist_handle(self,number):
        whitelist_switch = EventMesh.publish("persistent_config_get", "whitelistSwitch")
        if whitelist_switch == None or whitelist_switch == "1":
            return 0
            
        if whitelist_switch == "0": #白名单开启
            whitelist = EventMesh.publish("persistent_config_get", "whitelist")
            if whitelist == None:  
                whitelist = []
                return 0
            if len(whitelist) == 0:
                return 0
            if number not in whitelist:
                return 1
        return 0

class SmsManager(Abstract):
    '''短信'''
    def __init__(self):
        self.__phonenum = None
        self.log = LogAdapter(self.__class__.__name__)
        self.sms_lock = _thread.allocate_lock()

    def post_processor_after_instantiation(self):
        # 注册事件
        sms.setSaveLoc('SM','SM','SM')
        sms.setCallback(self.sms_cb)
        EventMesh.subscribe("message_send", self.sms_send)
        EventMesh.subscribe("message_read", self.read_sms)
        EventMesh.subscribe("message_delete",self.sms_delete)

    def sms_send(self, topic=None, message=None):
        '''发送短信'''
        msg, phoneNumber = message
        # self.log.info("sms sent message: {}, {}".format(msg, phoneNumber))
        chinese,english,symbol = EventMesh.publish("get_char_len",msg)
        if chinese == 0:
            state = sms.sendTextMsg(phoneNumber, msg,"GSM")
        else:
            state = sms.sendTextMsg(phoneNumber, msg,"UCS2")
        if state == 0:
            new_sendInfo=[]
            message_info = {
                "msg":"",
                "date":"2023/8/12 11:14:20",
                "number": "13888888888"
            }
            date = EventMesh.publish("screen_get_time")
            message_info["date"] = date
            message_info["number"] = phoneNumber
            message_info["msg"] = msg
            sendInfo = EventMesh.publish("persistent_config_get","sendInfo")
            if sendInfo != None:
                new_sendInfo = sendInfo
                if len(new_sendInfo) > 9:
                    new_sendInfo.pop(9)
            new_sendInfo.insert(0,message_info)
            # print("已发短信:",new_sendInfo)
            EventMesh.publish("persistent_config_store",{"sendInfo":new_sendInfo})
        else:
            err_sendInfo=[]
            message_err = {
                "msg":"",
                "number": "13888888888"
            }
            message_err["number"] = phoneNumber
            message_err["msg"] = msg
            errInfo = EventMesh.publish("persistent_config_get","errInfo")
            if errInfo != None:
                err_sendInfo = errInfo
                if len(err_sendInfo) > 9:
                    err_sendInfo.pop(9)
            err_sendInfo.insert(0,message_err)
            print("未发出短信:",err_sendInfo)
            EventMesh.publish("persistent_config_store",{"errInfo":err_sendInfo})
        return state

    def sms_delete(self, topic, index=None):
        '''删除短信'''
        message_id = EventMesh.publish("persistent_config_get","messageid")
        if message_id == None:
            return None
        if index is not None:
            # 删除单条
            for idx,message_index in enumerate(message_id):
                if idx == index:
                    message_id.pop(idx)
                    # print("sms.deleteMsg",index)
                    # sms.deleteMsg(int(index))
                    break
        else:
            # 删除全部
            message_id = []
            sms.deleteMsg(1,4)
        EventMesh.publish("persistent_config_store",{"messageid":message_id})

    def read_sms(self,topic, msg=None):
        '''读取短信'''
        self.language = EventMesh.publish("persistent_config_get", "language")
        print(msg)
        flag,index = msg
        print("flag:",flag," index:",index)
        message_id = EventMesh.publish("persistent_config_get","messageid")
        if flag == 0:
            if message_id!=None and len(message_id)>=100:
                if self.language == "zh":
                    EventMesh.publish("window_show","短信已满")
                else:
                    EventMesh.publish("window_show","SMS is full")
                return None
            phoneNumber, msg, msgLen = sms.searchTextMsg(index)
            new_message_id=[]
            date = EventMesh.publish("screen_get_time")
            message_index = {
                "read":"0",
                "msg_info":"",
                "date":"2023/8/12 11:14:20",
                "number": "13888888888"
            }
            message_index["msg_info"] = msg
            message_index["date"] = date
            if '+86' in phoneNumber:
                message_index["number"] = phoneNumber.lstrip("+86")
            else:
                message_index["number"] = phoneNumber
            num = message_index["number"]
            if self.blacklist_handle(num) or self.whitelist_handle(num):
                sms.deleteMsg(index)
                return -1
            if message_id != None:
                new_message_id = message_id
            new_message_id.insert(0,message_index)
            sms.deleteMsg(index)
            # print("new_message_id:", new_message_id)
            EventMesh.publish("persistent_config_store",{"messageid":new_message_id})
        else:
            if message_id == None:
                return None
            for idx,message_index in enumerate(message_id):
                if idx == index:
                    if message_index["read"] == "0":
                        message_index["read"]="1"
                    msg = message_index["msg_info"]
                    break
            # print("message_id:", message_id," msg_info:",msg)
            EventMesh.publish("persistent_config_store",{"messageid":message_id})
        return msg

    def sms_cb(self, args):
        '''短信监听回调'''
        self.log.info("sms cb message args : " + str(args))
        self.data = 0,args[1]
        print("来短信了：：",self.data)
        _thread.start_new_thread(self.thread_read_sms,(0,args[1]))
        
    def thread_read_sms(self,sms_data1,sms_data2):
        self.sms_lock.acquire()
        ret = self.read_sms(None,msg=self.data)
        if ret == -1:
            self.sms_lock.release()
            return
        # EventMesh.publish("message_read",data)
        
        #播放短信通知铃声
        if EventMesh.publish("get_call_state")!="0" or EventMesh.publish("get_current_screen_name") in ["call_out","call_up","on_call","call_end_screen","AlarmClockring"] or EventMesh.publish("get_howler_tone_enable")==1:
            print("通话状态不响短信铃声")
        else:
            if EventMesh.publish("persistent_config_get","disturb")=="0":
                if EventMesh.publish("get_hook_state"):
                    EventMesh.publish("audio_enable")
                EventMesh.publish("set_audio_volume", 3)
                if EventMesh.publish("persistent_config_get", "SMS_ring")==1:
                    EventMesh.publish("audio_play", AUDIO_FILE_NAME.RING_1)
                elif EventMesh.publish("persistent_config_get", "SMS_ring")==2:
                    EventMesh.publish("audio_play", AUDIO_FILE_NAME.RING_2)
                elif EventMesh.publish("persistent_config_get", "SMS_ring")==3:
                    EventMesh.publish("audio_play", AUDIO_FILE_NAME.RING_3)
                elif EventMesh.publish("persistent_config_get", "SMS_ring")==4:
                    EventMesh.publish("audio_play", AUDIO_FILE_NAME.RING_4)
        EventMesh.publish("update_screen_sms")
        self.sms_lock.release()
    def blacklist_handle(self,number):
        blacklist = EventMesh.publish("persistent_config_get", "blacklist")
        if blacklist == None:  
            blacklist = []
            return 0
        if number in blacklist:
            return 1
        return 0
    
    def whitelist_handle(self,number):
        whitelist_switch = EventMesh.publish("persistent_config_get", "whitelistSwitch")
        if whitelist_switch == None or whitelist_switch == "1":
            return 0
            
        if whitelist_switch == "0": #白名单开启
            whitelist = EventMesh.publish("persistent_config_get", "whitelist")
            if whitelist == None:  
                whitelist = []
                return 0
            if len(whitelist) == 0:
                return 0
            if number not in whitelist:
                return 1
        return 0

class AlarmClockManager(Abstract):
    def __init__(self):
        # 初始化闹钟列表
        self.alarms = list()
        self.RTC=RTC()
        self.alarm_ring=""
        self.alarm_name=""
    def post_processor_after_instantiation(self):
        """订阅此类所有的事件到 EventMesh中"""
        print("AlarmClockManager 设置一下")
        EventMesh.subscribe("start_rtc_task", self.start_rtc_task)
        EventMesh.subscribe("set_next_alarm", self.set_next_alarm)
        EventMesh.subscribe("disable_alarm", self.disable_alarm)
        EventMesh.subscribe("enable_alarm", self.enable_alarm)
        # EventMesh.publish("set_next_alarm")
        
    def initialization(self):
        self.language = EventMesh.publish("persistent_config_get", "language")
        # print("注册成功：",self.RTC.register_callback(self.rtc_callback))
        # self.set_next_alarm()

    def disable_alarm(self, topic=None, mode=None):
        self.RTC.enable_alarm(0)

    def enable_alarm(self, topic=None, mode=None):
        self.RTC.enable_alarm(1)

    def start_rtc_task(self, topic=None, data=None):
        # 注册回调
        print("设置闹钟")
        # 设置到期时间
        # data=[2024, 2, 1, 4, 14, 10, 52, 0]
        print("设置到期时间:",data)
        print("成功：",self.RTC.set_alarm(data))
        self.time_list = data
        hour=self.time_list[4]
        hour=str(hour)
        if len(hour)==1:
            hour = "0{}".format(hour)
        min=self.time_list[5]
        min=str(min)
        if len(min)==1:
            min = "0{}".format(min)
        self.alarm_time="{}:{}".format(hour,min)
        print("self.alarm_time:",self.alarm_time)
        self.alarms_len=len(self.alarms)
        for i in range(self.alarms_len):
            if self.alarms[i][2]== self.alarm_time:
                self.alarm_name=self.alarms[i][1]
                self.alarm_ring=self.alarms[i][3]
                break
        print("self.alarm_ring:",self.alarm_ring)
        print("self.alarm_name:",self.alarm_name)
        # 使能RTC alarm功能
        self.RTC.enable_alarm(1)

    def rtc_callback(self, args):
        # rtc回调
        print("RTCManager rtc_callback")
        # 闹钟响铃
        meta_info = dict()
        meta_info["logo"] = "s"
        meta_info["time"] = self.alarm_time
        meta_info["name"] = self.alarm_name
        meta_info["ring"] = self.alarm_ring
        # if EventMesh.publish("get_current_screen_name") == "charging":
        #     EventMesh.publish("enter_main_screen")
        EventMesh.publish("window_show","闹钟时间到" if self.language=="zh" else "It's alarm time")
        print("时间到")
        if EventMesh.publish("get_call_state")=="0" and EventMesh.publish("get_SIP_call_state")== 0:#非通话状态
            EventMesh.publish("load_screen", {"screen": "AlarmClockring","meta_info":meta_info})
        else:
            EventMesh.publish("obtain_alarm_clock_duplicated")
        # 开启下一个闹钟任务
        EventMesh.publish("set_next_alarm")

    def set_next_alarm(self, topic=None, data=None):
        """ 设置下一个闹钟任务 """
        # 开启下一个闹钟任务前先停止上一个任务
        # EventMesh.publish("disable_alarm")
        self.RTC.enable_alarm(0)
        next_alarm = self.next_alarm_timestamp()
        print("set_next_alarm next_alarm :",next_alarm)
        if next_alarm:
            # 启动RTC 闹钟
            self.start_rtc_task(data=next_alarm)
            EventMesh.publish("persistent_config_store",{"alarm_flag":"1"})
            # EventMesh.publish("start_rtc_task",next_alarm)
        else:
            EventMesh.publish("persistent_config_store",{"alarm_flag":"0"})
            print("没有设置闹钟")


    def next_alarm_timestamp(self):
        """
        计算最近的闹钟任务的时间戳, 周一到周日是0~6
        """
        self.alarms = EventMesh.publish("persistent_config_get", "Alarm_Clock_list")
        # self.alarms=[["0","a","11:50","铃声1","自定义",[0,1,3,5]],["1","b","11:45","铃声1","每天",[8]],["0","c","14:45","铃声2","单次",[]]]
        print("闹钟列表：",self.alarms)
        now_time = EventMesh.publish("get_RTC_date")
        print("RTC_now_time",now_time)  
        now_time = list(EventMesh.publish("get_RTC_date"))
        print("next_alarm_timestamp now_time: ", now_time)
        # 解析出当前时间
        hour_min_str = "{:02d}:{:02d}".format(now_time[4], now_time[5])
        print("next_alarm_timestamp hour_min_str: ", hour_min_str)
        # 获取星期
        current_weekday = now_time[3]#当前星期
        conv_time = utime.localtime()
        print("conv_time:",conv_time)
        mktime = utime.mktime(tuple(conv_time))
        print("mktime:",mktime)
        print("current_weekday:",current_weekday)
        current_weekday = (current_weekday + 6) % 7 # 修正星期为0~6, 0为周一
        print("current_weekday:",current_weekday)
        next_alarm_time = None  
        for i in range(len(self.alarms)):
            print("alarm:",self.alarms[i])
            if self.alarms[i][0]=="1":#闹钟开启
                alarm_hm = self.alarms[i][2]#取出设置的闹钟 时 分
                print("当前时间和设置的时间:",hour_min_str, alarm_hm)#输出当前时间和设置的时间
                # if alarm[5][0]==0:
                #     alarm[5][0]=""
                if self.alarms[i][5]: #有设置星期
                    print("有设置星期")
                    new_mon_day_hm_str = self.find_the_next_weekday(self.alarms[i][5], current_weekday,
                                                                    hour_min_str, alarm_hm, mktime)
                    print("下一个闹钟的时间：",new_mon_day_hm_str)
                else:
                    print("不重复")
                    new_times = mktime + 86400 if hour_min_str >= alarm_hm else mktime
                    new_time = list(utime.localtime(new_times))
                    new_mon_day_hm_str = "{:02d}.{:02d} {}".format(new_time[1], new_time[2], alarm_hm)
                    print("下一个闹钟的时间：",new_mon_day_hm_str)

                if next_alarm_time and new_mon_day_hm_str < next_alarm_time:
                    next_alarm_time = new_mon_day_hm_str
                elif not next_alarm_time:
                    next_alarm_time = new_mon_day_hm_str
                  
        # 处理设置RTC时间的格式 
        print("next_alarm_time:",next_alarm_time) 
        if next_alarm_time:
            date, time = next_alarm_time.split(" ")
            month, day = date.split(".")
            hour, min = time.split(":")
            now_time[1] = int(month)
            now_time[2] = int(day)
            now_time[4] = int(hour)
            now_time[5] = int(min)
            # 赋值为0是取时间整数，去除秒数
            now_time[6] = 0
            now_time[7] = 0
        else:
            # 当前没有闹钟任务或没有在当前时间后的闹钟任务
            now_time = None
        return now_time
    
    def find_the_next_weekday(self, weekdays, current_weekday, hour_min_str, alarm_hm, now_time):
        """找到alarm[1]中比当前星期大的第一个星期"""
        if weekdays[0]==8:
            print("每天")
            weekdays[0]=0
            for i in range(6):
                weekdays.append(i+1)
            print("每天：",weekdays)  
        for weekday in weekdays:
            # 如果闹钟星期大于当前星期 或者 闹钟星期等于当前星期并且闹钟时间大于当前时间
            if weekday > current_weekday or (weekday == current_weekday and hour_min_str < alarm_hm):
                time_difference = (weekday - current_weekday + 7) % 7  # 计算到下一个星期的天数
                break
        else:
            time_difference = (7 - current_weekday + weekdays[0]) % 7 or 7 # 计算到下一个星期的天数,如果是0则为7
          
        time_difference = time_difference * 86400
        new_times = now_time + time_difference
        new_time = utime.localtime(new_times)
        new_mon_day_hm_str = "{:02d}.{:02d} {}".format(new_time[1], new_time[2], alarm_hm)
        return new_mon_day_hm_str
    
class NtpTimeManager(Abstract):
    """
    NTP 对时功能
    """
    def __init__(self):
        pass

    def post_processor_after_instantiation(self):
        # 注册事件
        _thread.start_new_thread(self.start_ntp_task, ())

    def start_ntp_task(self):
        """
        开启对时任务，成功后退出
        """
        while 1:
            try:
                # 检查注网状态
                if net.getState() != -1 and net.getState()[1][0] == 1:
                    # 参数8为时区
                    ntptime.settime(8)
                    print("对时")
                    EventMesh.publish("set_next_alarm")
                    break
                else:
                    utime.sleep(60)
            except:
                pass
    